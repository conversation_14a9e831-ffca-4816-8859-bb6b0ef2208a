'use client'

import * as SliderPrimitive from '@radix-ui/react-slider'
import type { ComponentPropsWithoutRef, ElementRef, Ref } from 'react'

import { cn } from '~/core/ui/utils'

const Step = (
  props: ComponentPropsWithoutRef<typeof SliderPrimitive.Root> & {
    classNameOverride?: {
      root: string
      track: string
      range: string
      thumb: string
    }
    ref?: Ref<ElementRef<typeof SliderPrimitive.Root>>
  }
) => {
  const { classNameOverride, ref, ...restProps } = props
  const { min, max } = restProps
  const rangeStep = Number(max) - Number(min)
  const dividerStep = rangeStep - 1

  return (
    <SliderPrimitive.Root
      ref={ref}
      className={cn('relative flex w-full touch-none items-center select-none', classNameOverride?.root)}
      {...restProps}
    >
      <SliderPrimitive.Track className={cn('relative h-2 w-full grow overflow-hidden rounded-full bg-gray-100', classNameOverride?.track)}>
        <SliderPrimitive.Range className={cn('bg-primary-300 absolute h-full', classNameOverride?.range)} />
        {dividerStep ? (
          <>
            {Array.from(Array(dividerStep).keys()).map((item, index) => (
              <span key={index} className="absolute h-full w-0.5 bg-white" style={{ left: `${((index + 1) / rangeStep) * 100}%` }} />
            ))}
          </>
        ) : null}
      </SliderPrimitive.Track>
      <SliderPrimitive.Thumb
        className={cn(
          `ring-offset-background focus-visible:ring-ring block h-5 w-5 rounded-full border-4 border-white bg-white transition-colors before:absolute before:-top-px before:-left-px before:h-[22px] before:w-[22px] before:rounded-full before:border before:border-gray-100 before:content-[''] focus-visible:outline-hidden disabled:pointer-events-none disabled:opacity-50`,
          classNameOverride?.thumb
        )}
      />
    </SliderPrimitive.Root>
  )
}

Step.displayName = SliderPrimitive.Root.displayName

export { Step }
