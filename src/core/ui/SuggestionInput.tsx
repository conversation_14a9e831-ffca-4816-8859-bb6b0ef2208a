'use client'

import type { FetchNextPageOptions, InfiniteQueryObserverResult } from '@tanstack/react-query'
import type { UIEventHandler } from 'react'
import { useEffect, useRef, useState } from 'react'

import type { InputOptionsProps } from '~/core/ui/Input'
import { Input } from '~/core/ui/Input'
import type { ISelectOption } from '~/core/ui/Select'
import { SelectOption } from '~/core/ui/Select'
import { cn } from '~/core/ui/utils'

interface SuggestionInputProps extends InputOptionsProps {
  ref?: React.Ref<HTMLInputElement>
  onClear?: () => void
  value: string | number
  destructive?: boolean
  onChange: (value: string | number) => void
  dataOptions: ISelectOption[]
  onScrollToBottom?: (options?: FetchNextPageOptions | undefined) => Promise<InfiniteQueryObserverResult<any, {}>>
  dropdownClassName?: string
}
const SuggestionInput = ({
  ref,
  value,
  onChange,
  onClear,
  size = 'sm',
  destructive = false,
  dataOptions,
  dropdownClassName,
  onScrollToBottom,
  ...props
}: SuggestionInputProps) => {
  const [openSuggestionOptions, setOpenSuggestionOptions] = useState(false)
  const dropDownContainerRef = useRef<HTMLDivElement>(null)

  const handleScroll: UIEventHandler<HTMLDivElement> = e => {
    if (!!e.currentTarget) {
      const { scrollTop, scrollHeight, clientHeight } = e.currentTarget
      const currPos = scrollTop + clientHeight
      if (scrollHeight - currPos < 1) {
        //scroll to bottom
        onScrollToBottom && onScrollToBottom()
      }
    }
  }
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropDownContainerRef.current && !dropDownContainerRef.current.contains(event.target as Node)) {
        setOpenSuggestionOptions(false)
      }
    }

    if (openSuggestionOptions) {
      document.addEventListener('click', handleClickOutside)
    }

    return () => {
      document.removeEventListener('click', handleClickOutside)
    }
  }, [openSuggestionOptions])

  return (
    <div className="relative" ref={dropDownContainerRef}>
      <div>
        <Input
          placeholder={props.placeholder}
          size={size}
          value={value}
          onChange={onChange}
          destructive={destructive}
          onClick={() => setOpenSuggestionOptions(true)}
        />
      </div>

      {openSuggestionOptions && dataOptions.length > 0 && (
        <div className="absolute top-full left-0 z-50 translate-y-2">
          <div
            onScroll={handleScroll}
            className={cn('shadow-ats max-h-[308px] w-[158px] overflow-y-auto rounded-xs bg-white p-1 outline-hidden', dropdownClassName)}
          >
            {dataOptions.map((item, index) => (
              <div
                key={index}
                onClick={() => {
                  onChange(item.value)
                }}
              >
                <SelectOption
                  option="radio"
                  size="sm"
                  data={item}
                  isSelected={value === item.value}
                  isFocused={value === item.value}
                  isOption={true}
                  isHeading={false}
                />
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

SuggestionInput.displayName = 'SuggestionInput'

export { SuggestionInput }
