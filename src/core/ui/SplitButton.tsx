'use client'

import { cva } from 'class-variance-authority'
import React from 'react'

import { DropdownMenu } from '~/core/ui/DropdownMenu'
import type { LucideIconName } from '~/core/ui/IconWrapper'
import IconWrapper from '~/core/ui/IconWrapper'
import { cn } from '~/core/ui/utils'

export const splitButtonVariants = cva('border-primary-600 flex items-center space-x-1 border-r border-solid', {
  variants: {
    size: {
      xs: 'h-6 px-2 py-[3px]',
      s: 'h-8 px-3 py-1.5',
      sm: 'h-[38px] px-[18px] py-[9px]',
      md: 'h-[42px] px-[18px] py-[9px]'
    }
  },
  defaultVariants: {
    size: 'sm'
  }
})

export const splitIconVariants = cva('hover:bg-primary-600 flex cursor-pointer items-center justify-center rounded-r', {
  variants: {
    size: {
      xs: 'p-[5px]',
      s: 'p-[7px]',
      sm: 'p-[9px]',
      md: 'p-[10px]'
    },
    selected: {
      default: '',
      selected: 'bg-primary-600'
    }
  },
  defaultVariants: {
    size: 'sm',
    selected: 'default'
  }
})

const getSplitButtonIcon = {
  md: 22,
  sm: 20,
  s: 18,
  xs: 14
}

const getSplitButtonLabel = {
  md: 'text-base',
  sm: 'text-sm',
  s: 'text-sm',
  xs: 'text-xs'
}

type SplitButtonSizeProps = 'xs' | 's' | 'sm' | 'md'
type SplitButtonMenuItemOption = {
  label?: string
  icon?: LucideIconName
  onClick?: (event: React.MouseEvent<HTMLElement>) => void
}
type SplitButtonMenuItemString = 'separate-line'
type SplitButtonMenuItem = SplitButtonMenuItemOption | SplitButtonMenuItemString

interface SplitButtonProps {
  size?: SplitButtonSizeProps
  onClick?: (event: React.MouseEvent<HTMLElement>) => void
  label: string
  menu?: Array<SplitButtonMenuItem | undefined>
  htmlType?: 'button' | 'submit' | 'reset'
  iconMenus?: LucideIconName
  hideIcon?: boolean
  ref?: React.Ref<HTMLButtonElement>
}

const SplitButton = (props: SplitButtonProps) => {
  const { size = 'sm', onClick, label = '', menu = [], htmlType = 'button', iconMenus, hideIcon = false, ref } = props
  return (
    <div className="bg-primary-400 relative flex items-center rounded-xs">
      <button
        ref={ref}
        type={htmlType}
        onClick={event => {
          if (onClick) {
            onClick(event)
          }
        }}
        className={splitButtonVariants({
          size,
          className: hideIcon ? 'border-none' : ''
        })}
      >
        {iconMenus ? <IconWrapper size={getSplitButtonIcon[size]} name={iconMenus} className="-ml-0.5 text-white" /> : null}
        <span className={cn('block font-medium text-white', getSplitButtonLabel[size], 'leading-none')}>{label}</span>
      </button>

      {hideIcon === false ? (
        <DropdownMenu
          side="bottom"
          align="end"
          menuClassName="w-auto"
          trigger={openDropdown => (
            <div
              className={splitIconVariants({
                size,
                selected: openDropdown ? 'selected' : 'default'
              })}
            >
              <IconWrapper size={getSplitButtonIcon[size]} name="ChevronDown" className="text-white" />
            </div>
          )}
          menu={menu}
        />
      ) : null}
    </div>
  )
}

SplitButton.displayName = 'SplitButton'

export { SplitButton }
export type { SplitButtonMenuItem, SplitButtonProps, SplitButtonSizeProps }
