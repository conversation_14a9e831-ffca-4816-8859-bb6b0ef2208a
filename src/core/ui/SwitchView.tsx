'use client'

import { cva } from 'class-variance-authority'
import { useState } from 'react'

import type { LucideIconName } from '~/core/ui/IconWrapper'
import IconWrapper from '~/core/ui/IconWrapper'
import { cn } from '~/core/ui/utils'

const tabSwitchViewVariants = cva('flex h-full cursor-pointer items-center rounded-xs px-1.5', {
  variants: {
    active: {
      default: 'text-gray-600 hover:bg-white hover:text-gray-900 hover:shadow-2xs dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300',
      active: 'bg-gray-100 text-gray-900 shadow-2xs dark:bg-gray-700 dark:text-gray-300'
    }
  },
  defaultVariants: {
    active: 'default'
  }
})

interface SwitchViewProps {
  ref?: React.Ref<HTMLDivElement>
  source?: Array<{ icon: LucideIconName; value: string; className?: string }>
  value?: string | string[] | any
  defaultValue?: string | string[] | any
  setValue?: (value: string | string[]) => void
}

const SwitchView = ({ ref, value, defaultValue, setValue, source }: SwitchViewProps) => {
  const [tabState, setTabState] = useState(defaultValue || value)

  return (
    <div className="w-full">
      <div className="flex items-center justify-between rounded-xs bg-gray-100 p-0.5 dark:bg-gray-800">
        <div className="flex h-5 items-center" ref={ref}>
          {(source || []).map((session, index) => (
            <div
              key={index}
              onClick={() => {
                setTabState(session.value)
                if (setValue) {
                  setValue(session.value)
                }
              }}
              className={cn(
                tabSwitchViewVariants({
                  active: tabState.includes(session.value) ? 'active' : 'default'
                })
              )}
            >
              <div className="flex items-center">{session.icon ? <IconWrapper name={session.icon} size={10} className="" /> : null}</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

SwitchView.displayName = 'SwitchView'

export { SwitchView }
export type { SwitchViewProps }
