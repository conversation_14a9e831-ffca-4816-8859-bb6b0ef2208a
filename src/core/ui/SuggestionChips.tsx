'use client'

import { cva } from 'class-variance-authority'

import type { LucideIconName } from '~/core/ui/IconWrapper'
import IconWrapper from '~/core/ui/IconWrapper'
import { Tooltip } from '~/core/ui/Tooltip'
import { cn, truncateTextWithDot } from '~/core/ui/utils'
import { isHTML } from '~/core/utilities/common'

const chipVariants = cva(
  'focus:ring-primary-300 dark:focus:ring-primary-700 flex items-center rounded-xs border border-solid border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-1 disabled:pointer-events-none disabled:border-gray-200 disabled:text-gray-400 dark:border-gray-600 dark:bg-gray-900 dark:text-gray-300 dark:hover:bg-gray-800 dark:disabled:border-gray-700 dark:disabled:text-gray-700',
  {
    variants: {
      variant: {
        4: 'pl-3',
        3: 'pl-2.5',
        2: 'pl-2',
        1: 'pl-1.5'
      },
      size: {
        lg: 'h-8 py-1.5 pr-3',
        md: 'h-6 py-0.5 pr-2.5',
        sm: 'h-5 py-px pr-2'
      }
    },
    defaultVariants: {
      variant: 4,
      size: 'lg'
    }
  }
)

const chipLabelVariants = cva('', {
  variants: {
    disabled: {
      disabled: 'pointer-events-none border-gray-200 text-gray-400 dark:border-gray-700 dark:text-gray-700',
      default:
        'focus:ring-primary-300 dark:focus:ring-primary-700 border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-1 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-800'
    },
    size: {
      lg: 'text-sm',
      md: 'text-sm',
      sm: 'text-xs'
    }
  },
  defaultVariants: {
    size: 'lg',
    disabled: 'default'
  }
})

const getSuggestionChipPaddingCondition = ({ size = 'lg', type = 'default' }: { size?: 'lg' | 'md' | 'sm'; type?: 'default' | 'icon-leading' }) => {
  if (size === 'lg' && type === 'default') return 4

  if ((size === 'md' && type === 'default') || (size === 'lg' && type === 'icon-leading')) {
    return 3
  }

  if ((size === 'sm' && type === 'default') || (size === 'md' && type === 'icon-leading')) {
    return 2
  }

  if (size === 'sm' && type === 'icon-leading') return 1

  return 4
}

const suggestionChipDefaultSizeIcon: { [key: string]: number } = {
  lg: 16,
  md: 14,
  sm: 12
}

interface SuggestionChipsGeneralProps {
  size?: 'lg' | 'md' | 'sm'
  type?: 'default' | 'icon-leading'
  isDisabled?: boolean
  isRemovable?: boolean
  classNameChip?: string
  startIconMenus?: LucideIconName
  startIconMenusClassName?: string
  classNameChipText?: string
}

type SuggestionChipsProps = SuggestionChipsGeneralProps & {
  label?: string
  iconLeading?: LucideIconName
  index?: number
  onClick?: (e: any) => void
  maxLength?: number
}

type SuggestionChipsSelectProps = SuggestionChipsGeneralProps & {
  source: Array<SuggestionChipsProps>
  maxItems?: number
  className?: string
  clickableOnMore?: boolean
}

const SuggestionChipsItem = ({
  index,
  maxLength,
  size = 'lg',
  type = 'default',
  isRemovable = false,
  label,
  isDisabled = false,
  onClick,
  classNameChip = '',
  startIconMenus,
  startIconMenusClassName = '',
  classNameChipText = ''
}: SuggestionChipsProps) => {
  return (
    <button
      type="button"
      className={cn(
        'line-clamp-1',
        chipVariants({
          variant: getSuggestionChipPaddingCondition({ size, type }),
          size,
          className: classNameChip
        })
      )}
      disabled={isDisabled}
      onClick={e => {
        if (onClick) {
          onClick(e)
        }
      }}
    >
      <>
        {startIconMenus && (
          <IconWrapper name={startIconMenus} size={suggestionChipDefaultSizeIcon[size]} className={cn('mr-1', startIconMenusClassName)} />
        )}
        {label && maxLength && label?.length > maxLength ? (
          <Tooltip content={label && isHTML(label) ? <div dangerouslySetInnerHTML={{ __html: label }} /> : label} classNameAsChild="line-clamp-1">
            {label && isHTML(label) ? (
              <span
                className={cn(
                  'line-clamp-1 break-all',
                  chipLabelVariants({
                    size,
                    disabled: isDisabled ? 'disabled' : 'default',
                    className: classNameChip
                  })
                )}
                dangerouslySetInnerHTML={{
                  __html: truncateTextWithDot(label, maxLength + (label?.match(/<mark>/gim)?.length || 0) * 13)
                }}
              />
            ) : (
              <span
                className={cn(
                  'line-clamp-1 break-all',
                  chipLabelVariants({
                    size,
                    disabled: isDisabled ? 'disabled' : 'default',
                    className: classNameChip
                  })
                )}
              >
                {truncateTextWithDot(label, maxLength)}
              </span>
            )}
          </Tooltip>
        ) : (
          <>
            {label && isHTML(label) ? (
              <span
                className={cn(
                  'line-clamp-1 break-all',
                  chipLabelVariants({
                    size,
                    disabled: isDisabled ? 'disabled' : 'default',
                    className: classNameChip
                  })
                )}
                dangerouslySetInnerHTML={{ __html: label }}
              />
            ) : (
              <span
                className={cn(
                  'line-clamp-1 break-all',
                  chipLabelVariants({
                    size,
                    disabled: isDisabled ? 'disabled' : 'default',
                    className: classNameChip
                  })
                )}
              >
                {label}
              </span>
            )}
          </>
        )}
        {isRemovable && (
          <span className="ml-1.5 cursor-pointer">
            <IconWrapper
              name="X"
              size={suggestionChipDefaultSizeIcon[size]}
              className={isDisabled === false ? 'text-gray-700 dark:text-gray-300' : 'text-gray-400 dark:text-gray-700'}
            />
          </span>
        )}
      </>
    </button>
  )
}

const SuggestionChips = (props: SuggestionChipsSelectProps) => {
  const { maxItems = 0, className, source = [], ...otherProps } = props
  return (
    <div className={`-mt-2 flex flex-wrap items-center ${className || ''}`}>
      {(maxItems > 0 ? source.slice(0, maxItems) : source).map((suggestionChip, index) => {
        return (
          <div key={`suggestion-chip-${index}`} className="mt-2 mr-2 max-w-full flex-none last:mr-0">
            <SuggestionChipsItem index={index} {...otherProps} {...suggestionChip} />
          </div>
        )
      })}
      {maxItems > 0 && maxItems < source.length && (
        <Tooltip
          align="center"
          content={source.slice(maxItems).map((suggestionChip, index) => (
            <div
              key={`suggestion-tooltip-${index}`}
              className={suggestionChip.onClick ? 'cursor-pointer hover:underline' : ''}
              onClick={e => {
                if (suggestionChip.onClick) {
                  suggestionChip.onClick(e)
                }
              }}
              dangerouslySetInnerHTML={{ __html: String(suggestionChip.label) }}
            />
          ))}
          position="top"
        >
          <div className="mt-2 mr-2 max-w-full flex-none last:mr-0">
            <SuggestionChipsItem index={9999} {...otherProps} label={`+${source.length - maxItems}`} />
          </div>
        </Tooltip>
      )}
    </div>
  )
}

const SuggestionInlineChips = (props: SuggestionChipsSelectProps) => {
  const { maxItems = 0, className, source = [], clickableOnMore, ...otherProps } = props
  const onClickButtonMore = clickableOnMore && source.length > 0 ? source?.[0]?.onClick : null
  return (
    <>
      {(maxItems > 0 ? source.slice(0, maxItems) : source).map((suggestionChip, index) => {
        return (
          <div key={`suggestion-chip-${index}`} className={cn('mt-2 mr-2 max-w-full flex-none last:mr-0', className)}>
            <SuggestionChipsItem index={index} {...otherProps} {...suggestionChip} />
          </div>
        )
      })}
      {maxItems > 0 && maxItems < source.length && (
        <Tooltip
          align="center"
          classNameAsChild="flex-none"
          sideOffset={0}
          content={source.slice(maxItems).map((suggestionChip, index) => (
            <div
              key={`suggestion-tooltip-${index}`}
              className={suggestionChip.onClick ? 'cursor-pointer hover:underline' : ''}
              onClick={e => {
                if (suggestionChip.onClick) {
                  suggestionChip.onClick(e)
                }
              }}
              dangerouslySetInnerHTML={{ __html: String(suggestionChip.label) }}
            />
          ))}
          position="top"
        >
          <div className="mt-2 mr-2 max-w-full flex-none last:mr-0">
            <SuggestionChipsItem
              {...otherProps}
              onClick={e => onClickButtonMore && onClickButtonMore(e)}
              index={9999}
              label={`+${source.length - maxItems}`}
            />
          </div>
        </Tooltip>
      )}
    </>
  )
}

export { SuggestionChips, SuggestionChipsItem, SuggestionInlineChips }
export type { SuggestionChipsGeneralProps, SuggestionChipsProps, SuggestionChipsSelectProps }
