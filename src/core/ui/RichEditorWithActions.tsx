'use client'

import type { Editor as CoreEditor } from '@tiptap/core'
import { cva } from 'class-variance-authority'
import type { Dispatch, ReactNode, SetStateAction } from 'react'
import React, { useRef } from 'react'

import { <PERSON><PERSON> } from '~/core/ui/Button'
import { Dialog } from '~/core/ui/Dialog'
import { AlertCircleFill } from '~/core/ui/FillIcons'
import { IconButton } from '~/core/ui/IconButton'
import IconWrapper from '~/core/ui/IconWrapper'
import type { IExtraToolbar, RichEditorSizeProps } from '~/core/ui/RichEditor'
import { RichEditor } from '~/core/ui/RichEditor'
import { TypographyText } from '~/core/ui/Text'
import { Tooltip } from '~/core/ui/Tooltip'
import { cn } from '~/core/ui/utils'

const richEditorWithActionsWrapperVariants = cva('rounded-xs', {
  variants: {
    variant: {
      unstyled: 'shadow-textarea border-t border-solid border-gray-100',
      outline:
        'border border-solid border-gray-300 disabled:border disabled:border-solid disabled:border-gray-200 disabled:bg-gray-50 dark:border-gray-600 dark:bg-gray-900 dark:disabled:border-gray-700 dark:disabled:bg-gray-800'
    },
    position: {
      top: 'top-0',
      bottom: 'bottom-0'
    },
    expand: {
      default: 'h-[48px]',
      expand: 'pb-[48px]'
    }
  },
  defaultVariants: {
    variant: 'outline',
    position: 'bottom',
    expand: 'default'
  }
})

const richEditorActionsVariants = cva('absolute right-px z-5 flex h-[49px] items-center justify-between p-3', {
  variants: {
    position: {
      top: 'top-0',
      bottom: 'bottom-0'
    },
    expand: {
      default: '',
      expand: 'left-px border-t border-solid border-gray-100'
    }
  },
  defaultVariants: {
    position: 'bottom',
    expand: 'default'
  }
})

interface RichEditorWithActionsProps {
  autoFocus?: boolean
  className?: string
  classNameWrapper?: string
  destructiveText?: string
  variant?: 'unstyled' | 'outline'
  position?: 'top' | 'bottom'
  value?: string
  onChange?: (value?: string | number) => void
  placeholder?: string
  isExpand: boolean
  setIsExpand?: Dispatch<SetStateAction<boolean>>
  isExpandModal?: boolean
  setIsExpandModal?: Dispatch<SetStateAction<boolean>>
  actions?: {
    leftComponents?: (isDialogDisplay?: boolean) => ReactNode
    onCancelText?: string
    onCancel: () => void
    onSubmitText?: string
    onSubmit?: (value: string | number | undefined) => void
    modal?: {
      title: string
    }
  }
  size?: RichEditorSizeProps
  extraToolbar?: IExtraToolbar
  editorRef?: (editor: CoreEditor) => void
  getElementAppendById?: string
  ref?: React.Ref<HTMLTextAreaElement>
}

const RichEditorWithActions = (props: RichEditorWithActionsProps) => {
  const {
    autoFocus,
    value,
    onChange,
    className = '',
    classNameWrapper = '',
    destructiveText = '',
    placeholder = '',
    variant = 'outline',
    position = 'bottom',
    isExpand,
    setIsExpand,
    isExpandModal,
    setIsExpandModal,
    actions,
    size,
    extraToolbar,
    editorRef,
    getElementAppendById,
    ref
  } = props
  const richEditorRef = useRef<any>(null)

  const rendered = ({ isDialog = false }: { isDialog?: boolean }) => {
    return (
      <div className={cn('relative w-full bg-white', classNameWrapper)}>
        <div
          className={cn(
            richEditorWithActionsWrapperVariants({
              variant,
              position,
              expand: isExpand ? 'expand' : 'default'
            }),
            isDialog ? 'border-0 pb-[56px]' : ''
          )}
        >
          <div
            className={cn('w-full', isExpand ? '' : 'flex h-[48px] items-center', isDialog ? 'rounded-xs border border-solid border-gray-300' : '')}
          >
            <div className="w-full" onClick={() => setIsExpand && setIsExpand(true)}>
              <RichEditor
                autoFocus={autoFocus}
                size={size}
                toolbarPosition="bottom"
                getElementAppendById={isDialog ? 'modal-append-container' : getElementAppendById}
                extraToolbar={extraToolbar}
                editorRef={(editor: CoreEditor) => {
                  editorRef && editorRef(editor)
                  richEditorRef.current = editor
                }}
                placeholder={placeholder}
                content={value}
                showToolbar={isExpand}
                variant="unstyled"
                className={className}
                classNameWrapper={cn(
                  isExpand ? 'max-h-[324px] min-h-[98px] overflow-y-auto' : 'mb-0 h-auto min-h-[48px] w-full pt-3 pb-0',
                  isDialog
                    ? `mb-0 max-h-none border-white pb-[46px] ${
                        extraToolbar?.attachments?.list && extraToolbar?.attachments?.list?.length > 0
                          ? 'h-[calc(100vh-299px)]'
                          : 'h-[calc(100vh-239px)]'
                      }`
                    : '',
                  className
                )}
                editorMenuClassName={isDialog ? 'bottom-0' : ''}
                onChange={e => onChange && onChange(e)}
                showCount={false}
              />
            </div>

            {isExpand && destructiveText && !isDialog ? (
              <div className="relative mb-3 flex items-center px-3">
                <div className="min-w-[16px]">
                  <AlertCircleFill />
                </div>

                <div className="ml-1">
                  <p className="text-sm font-normal text-red-500 dark:text-red-500">{destructiveText}</p>
                </div>
              </div>
            ) : null}

            {isExpand === true && extraToolbar?.attachments?.list?.length ? (
              <div className="max-h-[106px] overflow-y-auto px-3 pb-3">
                <div className="-mx-1 flex flex-wrap">
                  {extraToolbar.attachments.list.map((item, index) => (
                    <div
                      key={`file-${index}`}
                      className="mx-1 mb-2 flex flex-col rounded-xs bg-gray-50 px-3 py-2"
                      style={{ width: extraToolbar.attachments?.itemPerRow }}
                    >
                      <div className="flex items-center">
                        <div className="w-4">
                          {item.status === 'error' ? (
                            <AlertCircleFill className="fill-red-500" />
                          ) : (
                            <IconWrapper className="text-gray-400 dark:text-gray-400" name="Paperclip" size={16} />
                          )}
                        </div>

                        <Tooltip
                          align="start"
                          classNameAsChild={cn('block truncate px-2 text-sm font-medium text-gray-700', extraToolbar?.attachments?.classNameItem)}
                          content={item.name}
                        >
                          {item.name}
                        </Tooltip>

                        <IconButton
                          type="secondary-destructive"
                          size="xs"
                          iconMenus="Trash2"
                          onClick={() => {
                            if (extraToolbar.attachments?.onDelete) {
                              extraToolbar.attachments?.onDelete({
                                id: item.id,
                                index
                              })
                            }
                          }}
                        />
                      </div>
                      {item.status === 'error' ? (
                        <TypographyText className="px-6 text-xs text-red-500">{item.statusDescription}</TypographyText>
                      ) : null}
                    </div>
                  ))}
                </div>
              </div>
            ) : null}
          </div>

          {isExpand && destructiveText && isDialog ? (
            <div className="relative mb-3 flex items-center pt-1">
              <div className="min-w-[16px]">
                <AlertCircleFill />
              </div>

              <div className="ml-1">
                <p className="text-sm font-normal text-red-500 dark:text-red-500">{destructiveText}</p>
              </div>
            </div>
          ) : null}

          <div
            className={cn(
              richEditorActionsVariants({
                position,
                expand: isExpand ? 'expand' : 'default'
              }),
              isDialog ? '-bottom-6 h-auto border-none px-0 py-6' : ''
            )}
          >
            <div>{isExpand && actions?.leftComponents ? actions.leftComponents(isDialog) : null}</div>

            <div className="flex items-center space-x-2">
              {isExpand ? (
                <Button
                  size={isExpandModal ? 'sm' : 'xs'}
                  type="secondary"
                  label={actions?.onCancelText || 'Cancel'}
                  onClick={() => {
                    if (actions?.onCancel) {
                      actions.onCancel()
                    }
                  }}
                />
              ) : null}

              <Button
                size={isExpandModal ? 'sm' : 'xs'}
                type="primary"
                label={actions?.onSubmitText || 'Create'}
                onClick={() => {
                  if (actions?.onSubmit) {
                    actions.onSubmit(value)
                  }
                }}
              />
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (isExpandModal) {
    return (
      <Dialog
        open
        size="lg"
        onOpenChange={() => {
          if (!!extraToolbar?.expand?.onClick) {
            extraToolbar.expand.onClick()
          } else if (setIsExpandModal) {
            setIsExpandModal(false)
          }
        }}
        isPreventAutoFocusDialog={true}
        label={actions?.modal?.title || 'Add'}
      >
        {rendered({ isDialog: true })}
      </Dialog>
    )
  }

  return rendered({})
}

RichEditorWithActions.displayName = 'RichEditorWithActions'
export { RichEditorWithActions }

export type { RichEditorWithActionsProps }
