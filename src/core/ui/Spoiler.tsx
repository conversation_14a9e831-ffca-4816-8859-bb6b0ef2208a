'use client'

import type { ReactNode } from 'react'
import { useState } from 'react'

import { TextButton } from '~/core/ui/TextButton'

interface SpoilerProps {
  ref?: React.Ref<HTMLDivElement>
  className?: string
  maxHeight?: number
  hideLabel?: string
  showLabel?: string
  children: ReactNode
  collapse?: boolean
}

const Spoiler = (props: SpoilerProps) => {
  const { ref, className, maxHeight, hideLabel, showLabel, children, collapse = false, ...others } = props
  const [show, setShowState] = useState(collapse)

  const spoilerMoreContent = show ? hideLabel : showLabel

  return (
    <div className={className} ref={ref} {...others}>
      <div
        className="overflow-hidden"
        style={{
          maxHeight: !show ? maxHeight : undefined
        }}
      >
        <div>{children}</div>
      </div>

      <TextButton label={spoilerMoreContent} onClick={() => setShowState(opened => !opened)} />
    </div>
  )
}

Spoiler.displayName = 'Spoiler'

export { Spoiler }
export type { SpoilerProps }
