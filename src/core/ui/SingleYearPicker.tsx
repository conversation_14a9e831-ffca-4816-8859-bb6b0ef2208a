'use client'

import type { FC } from 'react'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'

import ComboboxSelect from '~/core/ui/ComboboxSelect'
import { IconButton } from '~/core/ui/IconButton'
import type { ISelectOption } from '~/core/ui/Select'
import type { DatePickerValue } from '~/core/ui/SingleDateWithYearOnlyPicker'

interface SingleYearPickerProps {
  value?: DatePickerValue
  onChange?: (value?: DatePickerValue) => void
  defaultEndRangeYear: number
}

const DEFAULT_STEP_YEAR = 12
export const DEFAULT_MIN_YEAR = 1920

const getEndYear = (startYear: number) => startYear + DEFAULT_STEP_YEAR - 1

const arrayRange = (start: number, stop: number, step: number) =>
  Array.from({ length: (stop - start) / step + 1 }, (value, index) => start + index * step)

const getYearRangeList = (currentYear: number): ISelectOption[] => {
  let maximumCurrentYearOption = DEFAULT_MIN_YEAR
  const yearOptions: ISelectOption[] = []

  while (maximumCurrentYearOption <= currentYear) {
    yearOptions.push({
      value: String(maximumCurrentYearOption),
      supportingObj: {
        name: `${maximumCurrentYearOption} - ${getEndYear(maximumCurrentYearOption)}`
      }
    })

    maximumCurrentYearOption += DEFAULT_STEP_YEAR
  }

  return yearOptions
}

const SingleYearPicker: FC<SingleYearPickerProps> = props => {
  const { value, defaultEndRangeYear, onChange } = props
  const parentRef = useRef<HTMLDivElement>(null)
  const [startYearSelected, setStartYearSelected] = useState<ISelectOption>() // value of ISelection is Start Year

  const yearOptions = useMemo(() => getYearRangeList(defaultEndRangeYear), [defaultEndRangeYear])

  const backToPreviousRangeYear = useCallback(() => {
    const year = Number(startYearSelected?.value)
    const indexSelectedYearRange = yearOptions.findIndex(yearRange => {
      const startYear = Number(yearRange.value)
      const endYear = getEndYear(startYear)

      return year >= startYear && year <= endYear
    })

    if (indexSelectedYearRange === 0) return

    setStartYearSelected(yearOptions[indexSelectedYearRange - 1])
  }, [startYearSelected, yearOptions])

  const nextRangeYear = useCallback(() => {
    const year = Number(startYearSelected?.value)
    const indexSelectedYearRange = yearOptions.findIndex(yearRange => {
      const startYear = Number(yearRange.value)
      const endYear = getEndYear(startYear)

      return year >= startYear && year <= endYear
    })

    if (indexSelectedYearRange === yearOptions.length - 1) return

    setStartYearSelected(yearOptions[indexSelectedYearRange + 1])
  }, [startYearSelected, yearOptions])

  useEffect(() => {
    const year = value?.year || new Date().getFullYear()
    const yearRangeSelected = yearOptions.find(yearRange => {
      const startYear = Number(yearRange.value)
      const endYear = getEndYear(startYear)

      return year >= startYear && year <= endYear
    })

    setStartYearSelected(yearRangeSelected)
  }, [value, yearOptions])

  return (
    <div ref={parentRef}>
      <div className="flex items-center justify-between py-1 pl-3">
        <div>
          <ComboboxSelect
            type="unstyled"
            closeOnSelect
            configSelectOption={{
              supportingText: ['name']
            }}
            onChange={value => setStartYearSelected(value as ISelectOption)}
            size="sm"
            value={startYearSelected}
            isSearchable={false}
            menuOptionSide="bottom"
            isClearable={false}
            menuOptionAlign="start"
            dropdownMenuClassName="min-w-[106px] max-w-[114px]"
            buttonClassName="max-w-[114px]"
            options={yearOptions}
          />
        </div>
        <div className="flex items-center">
          <IconButton
            onClick={backToPreviousRangeYear}
            type="secondary"
            size="sm"
            iconMenus="ChevronLeft"
            className="focus:shadow-none focus:ring-0 focus:ring-white"
          />
          <IconButton
            onClick={nextRangeYear}
            type="secondary"
            size="sm"
            iconMenus="ChevronRight"
            className="focus:shadow-none focus:ring-0 focus:ring-white"
          />
        </div>
      </div>
      <div className="grid grid-cols-3 gap-y-1">
        {arrayRange(Number(startYearSelected?.value), getEndYear(Number(startYearSelected?.value)), 1).map(year => (
          <button
            key={`year-option-${year}`}
            onClick={() => {
              onChange &&
                onChange({
                  year: year,
                  month: null,
                  date: null
                })
            }}
            className="inline-flex h-10 w-full items-center justify-center rounded-xs p-1 text-sm text-gray-700 hover:bg-gray-100"
          >
            {year === value?.year ? (
              <div className="bg-primary-400 inline-flex h-full w-full items-center justify-center rounded-xs leading-[13px] text-white">{year}</div>
            ) : (
              year
            )}
          </button>
        ))}
      </div>
    </div>
  )
}

SingleYearPicker.displayName = 'SingleYearPicker'

export { SingleYearPicker }
export type { SingleYearPickerProps }
