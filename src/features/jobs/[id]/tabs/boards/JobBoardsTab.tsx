'use client'

import { useRouter } from 'next/navigation'
import { useEffect } from 'react'
import { useTranslation } from 'react-i18next'

import configuration from '~/configuration'
import { PUBLIC_APP_NAME } from '~/core/constants/env'
import { Dialog } from '~/core/ui/Dialog'
import { adminAndMemberCanAction } from '~/core/utilities/permission'

import type { IJobDetail } from '~/lib/features/jobs/graphql/query-job-detail-mini'
import { PLAN_FEATURE_KEYS } from '~/lib/features/settings/plans/utilities/enum'
import useBoundStore from '~/lib/store'
import useToastStore from '~/lib/store/toast'

import PostJobInputForm from '~/components/JobBoard/PostJobInputForm'
import type { FeatureName } from '~/components/Subscription/subscription'
import { withSubscriptionPlanLockFearture } from '~/components/Subscription/SubscriptionPlan'

import JobBoardsView from './JobBoardsView'

const JobBoardsTab = ({
  jobDetail,
  onPublishJobPosting,
  onUnPublishJobPosting,
  changeStatusCallback,
  open,
  setOpen,
  isPublishing
}: {
  jobDetail?: IJobDetail
  onPublishJobPosting: (sourceDetail: string, label: string) => Promise<void>
  onUnPublishJobPosting: (sourceDetail: string, label: string) => Promise<void>
  changeStatusCallback: (status: string) => Promise<void>
  open: boolean
  setOpen: (open: boolean) => void
  isPublishing: boolean
}) => {
  const { t } = useTranslation()
  const router = useRouter()
  const { currentRole } = useBoundStore()
  const { setToast } = useToastStore()

  const onHandleCancelModal = () => {
    setOpen(false)
    setTimeout(() => {
      setToast({
        open: true,
        type: 'error',
        title: t('notification:missingInformationToPublishDomain', {
          domain: PUBLIC_APP_NAME
        })
      })
    }, 300)
  }

  useEffect(() => {
    if (!adminAndMemberCanAction(currentRole?.code)) {
      setToast({
        open: true,
        type: 'error',
        title: `${t('notification:errorPermission')}`
      })
      router.push(configuration.path.errorAccessDenied)
    }
  }, [currentRole])

  return (
    <div className="m-auto max-w-[776px]">
      <JobBoardsView
        setOpen={setOpen}
        changeStatusCallback={changeStatusCallback}
        onPublishJobPosting={onPublishJobPosting}
        onUnPublishJobPosting={onUnPublishJobPosting}
        jobDetail={jobDetail}
      />
      <Dialog
        open={open}
        size="md"
        onOpenChange={onHandleCancelModal}
        label={`${t('job:detail:boards:postJobOnDomain', {
          domain: PUBLIC_APP_NAME
        })}`}
        description={`${t('job:detail:boards:descriptionPostJobOnDomain')}`}
      >
        <PostJobInputForm isPublishing={isPublishing} onPublishJobPosting={onPublishJobPosting} setOpen={onHandleCancelModal} job={jobDetail} />
      </Dialog>
    </div>
  )
}

export default withSubscriptionPlanLockFearture(JobBoardsTab, PLAN_FEATURE_KEYS.job_board as FeatureName)
