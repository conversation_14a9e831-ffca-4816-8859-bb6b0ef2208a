import React, { useCallback, useMemo } from 'react'
import { useTranslation } from 'react-i18next'

import pathConfiguration from 'src/configuration/path'
import IconWrapper from '~/core/ui/IconWrapper'
import If from '~/core/ui/If'
import { TextButton } from '~/core/ui/TextButton'

import type { IJobDetail } from '~/lib/features/jobs/graphql/query-job-detail-mini'
import { JOB_STATUS_ENUM, LISTS_PUBLISH_JOB_BOARDS } from '~/lib/features/jobs/utilities/enum'

import JobBoardItem from './JobBoardItem'

const JobBoardsView = ({
  jobDetail,
  onPublishJobPosting,
  onUnPublishJobPosting,
  changeStatusCallback,
  setOpen
}: {
  jobDetail?: IJobDetail
  onPublishJobPosting: (sourceDetail: string, label: string) => Promise<void>
  onUnPublishJobPosting: (sourceDetail: string, label: string) => Promise<void>
  changeStatusCallback: (status: string) => Promise<void>
  setOpen: (open: boolean) => void
}) => {
  const { t } = useTranslation()
  const onChangeJobStatus = () => {
    changeStatusCallback(JOB_STATUS_ENUM.publish)
  }

  const isDisableAction = useMemo(() => jobDetail?.jobsShow.status === JOB_STATUS_ENUM.archived, [jobDetail])

  const getStatusPublished = useCallback(
    (type: 'google_for_jobs' | 'freec') => {
      if (type === 'google_for_jobs') {
        const published = jobDetail?.jobsShow?.preferences?.google_job_boards?.published || false
        return {
          published,
          showLabelReviewing: false,
          disabled: false
        }
      }
      if (type === 'freec') {
        const published = jobDetail?.jobsShow?.preferences?.freec_job_boards?.published || false
        const showLabelReviewing = jobDetail?.jobsShow?.preferences?.freec_job_boards?.status === 'reviewing'
        return {
          published,
          showLabelReviewing,
          disabled: (published && showLabelReviewing) || false
        }
      }
      return {
        published: false,
        showLabelReviewing: false,
        disabled: false
      }
    },
    [jobDetail]
  )

  return (
    <>
      <If condition={jobDetail?.jobsShow?.status !== JOB_STATUS_ENUM.publish}>
        <div className="mt-[80px] flex flex-col items-center justify-center">
          <p className="text-base font-medium text-gray-900">{t('job:detail:boards:attractMoreCandidates')}</p>
          <div className="mt-1">
            <p className="contents text-sm font-normal text-gray-600">{`${t('job:detail:boards:publishTheJobCandidates')} `}</p>
            <TextButton
              className="contents"
              classNameText="font-normal"
              label={`${t('common:learn_more')}`}
              onClick={() => {
                window.open(pathConfiguration.helpCenter.jobBoard, '_blank')
              }}
            />
          </div>
          <TextButton isDisabled={isDisableAction} onClick={onChangeJobStatus} className="mt-2">
            {t('button:publishJob')}
          </TextButton>
        </div>
      </If>

      <If condition={jobDetail?.jobsShow?.status === JOB_STATUS_ENUM.publish}>
        <div>
          <div className="mt-4 flex flex-row items-center bg-gray-50 px-3 py-2">
            <IconWrapper name="Lightbulb" size={16} className="text-gray-700" />
            <div className="ml-2">
              <p className="contents text-sm font-normal text-gray-700">{`${t('job:detail:boards:reachMoreCandidatesWithJobBoards')} `}</p>
              <TextButton
                isDisabled={isDisableAction}
                className="contents"
                classNameText="font-normal"
                label={`${t('common:learn_more')}`}
                onClick={() => {
                  window.open(pathConfiguration.helpCenter.jobBoard, '_blank')
                }}
              />
            </div>
          </div>
          <div className="mt-4">
            {LISTS_PUBLISH_JOB_BOARDS.map(i => (
              <JobBoardItem
                disabled={getStatusPublished(i.code as 'google_for_jobs' | 'freec').disabled || isDisableAction}
                setOpen={setOpen}
                published={getStatusPublished(i.code as 'google_for_jobs' | 'freec').published}
                showLabelReviewing={getStatusPublished(i.code as 'google_for_jobs' | 'freec').showLabelReviewing}
                onPublishJobPosting={onPublishJobPosting}
                onUnPublishJobPosting={onUnPublishJobPosting}
                key={i.code}
                item={i}
              />
            ))}
          </div>
        </div>
      </If>
    </>
  )
}

export default JobBoardsView
