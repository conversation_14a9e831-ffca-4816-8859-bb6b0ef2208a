import Image from 'next/image'
import React from 'react'
import { useTranslation } from 'react-i18next'

import { Badge } from '~/core/ui/Badge'
import IconWrapper from '~/core/ui/IconWrapper'
import If from '~/core/ui/If'
import { Toggle } from '~/core/ui/Toggle'
import { Tooltip } from '~/core/ui/Tooltip'

import type { IJobBoardItem } from '~/lib/features/jobs/types'

const JobBoardItem = ({
  item,
  onPublishJobPosting,
  onUnPublishJobPosting,
  published,
  setOpen,
  showLabelReviewing,
  disabled
}: {
  item: IJobBoardItem
  onPublishJobPosting: (sourceDetail: string, label: string) => Promise<void>
  onUnPublishJobPosting: (sourceDetail: string, label: string) => Promise<void>
  published?: boolean
  setOpen: (open: boolean) => void
  showLabelReviewing: boolean
  disabled: boolean
}) => {
  const { t } = useTranslation()
  const { code, label, icon, tooltip, w, h } = item

  return (
    <div className="mb-3 flex h-[72px] flex-row items-center rounded-md border border-gray-100 px-4 py-2">
      <div className="flex w-[80px] justify-center">
        <Image src={icon} width={w} height={h} alt={code} />
      </div>
      <div className="mx-4 flex flex-1 flex-row items-center">
        <div className="flex flex-1 flex-col">
          <div className="flex flex-row items-center">
            <p className="text-base font-medium text-gray-900">{label}</p>
            <Tooltip content={tooltip}>
              <div className="ml-[6px] cursor-pointer">
                <IconWrapper name="Info" size={12} />
              </div>
            </Tooltip>
          </div>

          <p className="text-sm font-normal text-gray-600">{t('job:detail:boards:publishForFree')}</p>
        </div>
        <If condition={showLabelReviewing}>
          <Badge color="yellow" className="mr-4" radius="rounded" size="md">
            {t('job:detail:boards:inReview')}
          </Badge>
        </If>
        <Toggle
          isChecked={published}
          isDisabled={disabled}
          isDebounce
          name="view-switch"
          onCheckedChange={checked => {
            if (checked) {
              onPublishJobPosting(code, label)
            } else {
              onUnPublishJobPosting(code, label)
            }
          }}
          size="sm"
          text=""
          toggle="trailing"
        />
      </div>
    </div>
  )
}

export default JobBoardItem
