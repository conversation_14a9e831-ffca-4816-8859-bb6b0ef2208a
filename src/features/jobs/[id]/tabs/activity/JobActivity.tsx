import type { ComponentProps, FC } from 'react'
import { useCallback, useMemo, useRef } from 'react'

import withQueryClientProvider from 'src/hoc/with-query-client-provider'
import type { IRouterWithID } from '~/core/@types/global'
import { Container } from '~/core/ui/Container'
import { Skeleton } from '~/core/ui/Skeleton'
import { cn } from '~/core/ui/utils'

import QueryJobActivities from '~/lib/features/activity/graphql/query-job-activities'
import type { ActivityBase } from '~/lib/features/activity/types'
import { useInfinityGraphPage } from '~/lib/features/jobs/hooks/use-infinity-graph-page'

import ActivityView from '~/components/Activity'
import { useClassBasedTopSpace } from '~/components/Subscription/TopSpace'

const JobActivity: FC<{ jobId?: IRouterWithID }> = ({ jobId }) => {
  const { refetch, data, hasNextPage, fetchNextPage, isLoading, isFetchedAfterMount, isFetching } = useInfinityGraphPage({
    queryDocumentNote: useMemo(() => QueryJobActivities, []),
    getVariable: useCallback(page => ({ jobId: parseInt(jobId as string), limit: 10, page }), [jobId]),
    getPageAttribute: (_lastGroup, groups) => ({
      totalCount: _lastGroup?.jobActivitiesList?.metadata?.totalCount,
      pageLength: Number(groups?.[0]?.jobActivitiesList?.collection?.length)
    }),

    queryKey: ['job-activity-list', jobId as string]
  })
  const activityList = data?.pages.reduce<ActivityBase[]>((list, page) => [...list, ...page?.jobActivitiesList?.collection], [])
  const isFirstLoading = isLoading || isFetchedAfterMount === false
  const scrollRef = useRef<HTMLDivElement>(null)
  const calcHeightScroll = useClassBasedTopSpace({
    34: 'h-[calc(100vh-133px)]',
    default: 'h-[calc(100vh-99px)]'
  })

  if (isFirstLoading) {
    return (
      <Container overrideClass="max-w-[776px] pt-4">
        {[1, 2, 3, 4, 5].map(index => (
          <div key={index} className="mb-4 flex items-center space-x-3">
            <Skeleton className="h-6 w-6 min-w-[24px] rounded-full" />
            <div className="w-full space-y-1">
              <Skeleton className="h-2 w-2/3" />
              <Skeleton className="h-2 w-full" />
            </div>
          </div>
        ))}
      </Container>
    )
  }

  return (
    <div className={cn('overflow-y-scroll', calcHeightScroll)} ref={scrollRef}>
      <Container overrideClass="max-w-[776px] pt-4">
        <ActivityView infinityScrollRef={scrollRef} activityList={activityList} fetchNextPage={fetchNextPage} hasNextPage={hasNextPage} />
      </Container>
    </div>
  )
}
const ActivityWithProvider = (props: ComponentProps<typeof JobActivity>) => {
  return <JobActivity {...props} />
}

export default withQueryClientProvider(ActivityWithProvider)
