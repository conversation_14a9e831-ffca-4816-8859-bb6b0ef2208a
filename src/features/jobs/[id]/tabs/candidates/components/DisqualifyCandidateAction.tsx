import type { FC } from 'react'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'

import { useRequestState } from '~/core/hooks/use-request-state'
import { IconButton } from '~/core/ui/IconButton'
import { Tooltip } from '~/core/ui/Tooltip'

import type { ICandidateApplicant } from '~/lib/features/candidates/types'

import RejectApplicantView from '~/components/Candidates/Profile/components/Reject/RejectApplicantView'

const DisqualifyCandidateAction: FC<{
  applicantId: number
  email: string[]
  id: string
  fullName: string
  callback: () => void
  defaultValue: ICandidateApplicant
}> = ({ applicantId, email, id, fullName, callback, defaultValue }) => {
  const { t } = useTranslation()
  const [open, setOpen] = useState(false)
  const [applicant, setApplicant] = useState<ICandidateApplicant>()
  const {
    setRefetch,
    state: { refetch }
  } = useRequestState()
  return (
    <div>
      <Tooltip content={`${t('tooltip:disqualify')}`}>
        <IconButton
          onClick={() => {
            setOpen(true)
            setApplicant(defaultValue)
          }}
          type="secondary-destructive"
          size="xs"
          iconMenus="Slash"
        />
      </Tooltip>
      {open && applicantId ? (
        <RejectApplicantView
          onEmailUpdateProfile={callback}
          email={email}
          id={id}
          fullName={fullName}
          setApplicant={setApplicant}
          applicant={applicant}
          setOpen={setOpen}
          open={open}
          setRefetch={setRefetch}
        />
      ) : null}
    </div>
  )
}

export default DisqualifyCandidateAction
