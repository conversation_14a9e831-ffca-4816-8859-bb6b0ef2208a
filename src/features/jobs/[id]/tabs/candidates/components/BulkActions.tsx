'use client'

import { formatISO } from 'date-fns'
import type { FC } from 'react'
import { memo, useCallback, useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'

import type { IToast } from '~/core/@types/global'
import { AGENCY_TENANT } from '~/core/constants/enum'
import type { IDataTableInfinityOrdering } from '~/core/ui/TableInfinityOrdering'
import { cn } from '~/core/ui/utils'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'

import MutationProfilesAssignJob from '~/lib/features/candidates/graphql/mutation-assign-profiles-to-job'
import MutationBulkCreatePlacement from '~/lib/features/candidates/graphql/mutation-bulk-create-placement'
import MutationBulkDeletePlacements from '~/lib/features/candidates/graphql/mutation-bulk-delete-placements'
import MutationBulkDirectMarkAsHiredApplicants from '~/lib/features/candidates/graphql/mutation-bulk-direct-mark-as-hired'
import MutationBulkMarkAsHiredApplicants from '~/lib/features/candidates/graphql/mutation-bulk-mark-as-hired'
import MutationDisqualifyBulkApplicants from '~/lib/features/candidates/graphql/mutation-disqualify-bulk-applicants'
import MutationMoveStageApplicants from '~/lib/features/candidates/graphql/mutation-move-stage-applicants'
import MutationProfilesAssignTalentPools from '~/lib/features/candidates/graphql/mutation-profiles-assign-talent-pools'
import type { AddCandidateFormType } from '~/lib/features/candidates/types'
import { splitIntoPatches } from '~/lib/features/candidates/utilities'
import type { IJobApplicants, IMarkAsHiredBulkForm } from '~/lib/features/jobs/types'
import { JOB_APPLICANT_STATUS } from '~/lib/features/jobs/utilities/enum'
import { formatSubmitCustomFieldData } from '~/lib/features/settings/profile-fields/mapping/custom-field-mapping'
import useDetectCompanyWithKind from '~/lib/hooks/use-detect-company-with-kind'
import { useSubmitCommon } from '~/lib/hooks/use-submit-graphql-common'
import useBoundStore from '~/lib/store'
import useLoadingBlockStore from '~/lib/store/loading-block'
import useToastStore from '~/lib/store/toast'

import BottomActionBar from '~/components/BottomActionBar'
import TalentPoolSelector from '~/components/Candidates/BulkActions/Candidate/TalentPoolSelector'
import type { IEmailForm } from '~/components/SendMailFormControl/EmailContentEditor'
import type { ICreateEditPlacement } from '~/features/placements/schema'

import AssignJobApplicantsSelector from './AssignJobApplicantsSelector'
import type { MoveStageFormType } from './MoveStageSelector'
import MoveStageSelector from './MoveStageSelector'
import MovingStageBulkProgress from './MovingStageBulkProgress'
import RejectApplicantsSelector from './RejectApplicantsSelector'

const PATCH_SIZE = 10

export type BulkStageItem = {
  id: number
  label: string
  jobStageId?: number | string
  stageTypeId?: number | string
  stageGroup?: string
}

const BulkActions: FC<{
  candidates?: IDataTableInfinityOrdering
  stages?: Array<BulkStageItem>
  currentJobId?: string
  className?: string
}> = ({ candidates, stages, currentJobId, className }) => {
  const { bulkValuesKanban, setBulkValuesKanbanUpdated, resetBulkValuesKanban, setRefetchMyList } = useBoundStore()
  const { setToast } = useToastStore()
  const { setShowLockApp, setCloseLockApp } = useLoadingBlockStore()
  const { t } = useTranslation()
  const [showProcessing, setShowProcessing] = useState<boolean>(false)
  const { isCompanyKind } = useDetectCompanyWithKind({ kind: AGENCY_TENANT })

  const { trigger: assignTalentPools, isLoading: isLoadingAssignTalentPools } = useSubmitCommon(MutationProfilesAssignTalentPools)
  const { trigger: moveStageApplicants, isLoading: isMovingStageApplicants } = useSubmitCommon(MutationMoveStageApplicants)
  const { trigger: deletePlacements, isLoading: deletingPlacements } = useSubmitCommon(MutationBulkDeletePlacements)
  const { trigger: markAsHired, isLoading: isMarkAsHired } = useSubmitCommon(
    isCompanyKind ? MutationBulkMarkAsHiredApplicants : MutationBulkDirectMarkAsHiredApplicants
  )
  const { trigger: createPlacement, isLoading: creatingPlacement } = useSubmitCommon(
    isCompanyKind ? MutationBulkCreatePlacement : MutationBulkDirectMarkAsHiredApplicants
  )
  const { trigger: disqualifyApplicants, isLoading: isDisqualifyingApplicants } = useSubmitCommon(MutationDisqualifyBulkApplicants)
  const { trigger: assignJob, isLoading: isLoadingAssignJob } = useSubmitCommon(MutationProfilesAssignJob)

  const handleClose = () => {
    resetBulkValuesKanban()
  }

  const onAssignTalentPools = useCallback(
    async (data: AddCandidateFormType) => {
      const { profileTalentPoolIds } = data
      setShowLockApp('')
      // const params = mappingAdvancedFilterCandidates(filterValues, user)
      if (isLoadingAssignTalentPools) {
        return
      }
      assignTalentPools({
        applicantIds: bulkValuesKanban?.map(id => Number(id)),
        talentPoolIds: profileTalentPoolIds?.map(poolItem => Number(poolItem.value))
        // selectAll: bulkSelectedAll,
        // ...params
      }).then(result => {
        if (result.error) {
          return catchErrorFromGraphQL({
            error: result.error,
            setToast
          })
        }
        const { profilesAssignTalentPools } = result.data
        if (profilesAssignTalentPools.success) {
          setCloseLockApp()
          setRefetchMyList(true)
          resetBulkValuesKanban()
          setToast({
            open: true,
            type: 'success',
            title: t('notification:assign_talent_pool_success')
          })
        }
        return
      })
    },
    [bulkValuesKanban]
  )

  const onApplicantsMoveStage = (data: MoveStageFormType) => {
    if (isMovingStageApplicants) {
      return Promise.resolve()
    }

    setShowProcessing(true)
    let updatedApplicantIds: Array<string> = []
    const patchesBulkValues = splitIntoPatches({
      arr: bulkValuesKanban,
      patchSize: PATCH_SIZE
    })

    const arrFetchingMoveStageAPIs = patchesBulkValues.map(applicantIdsPatch => {
      return moveStageApplicants({
        applicantIds: applicantIdsPatch?.map(id => Number(id)),
        jobStageId: Number(data?.jobStageId?.value)
      }).then(result => {
        if (result.error) {
          return catchErrorFromGraphQL({
            error: result.error,
            setToast
          })
        }
        const { applicantsBulkChangeStage } = result.data
        if (applicantsBulkChangeStage.success) {
          const newArrUpdatedApplicantIds = [...(updatedApplicantIds || []), ...applicantIdsPatch]

          updatedApplicantIds = newArrUpdatedApplicantIds
          setBulkValuesKanbanUpdated(updatedApplicantIds)
        }
        return
      })
    })

    return Promise.all(arrFetchingMoveStageAPIs).then(() => {
      setRefetchMyList(true)
      setTimeout(() => {
        setCloseLockApp()
        resetBulkValuesKanban()
        setShowProcessing(false)
        setToast({
          open: true,
          type: 'success',
          title: t('notification:candidateMoveToStage', {
            stageName: stages?.find(stage => Number(stage.jobStageId) === Number(data?.jobStageId?.value))?.label
          })
        })
      }, 1000)
    })
  }

  const onDeletePlacements = (data: MoveStageFormType) => {
    if (deletingPlacements) {
      return Promise.resolve()
    }

    setShowProcessing(true)
    let updatedApplicantIds: Array<string> = []
    const patchesBulkValues = splitIntoPatches({
      arr: bulkValuesKanban,
      patchSize: PATCH_SIZE
    })

    const arrFetchingMoveStageAPIs = patchesBulkValues.map(applicantIdsPatch => {
      return deletePlacements({
        applicantIds: applicantIdsPatch?.map(id => Number(id)),
        jobStageId: Number(data?.jobStageId?.value)
      }).then(result => {
        if (result.error) {
          return catchErrorFromGraphQL({
            error: result.error,
            setToast
          })
        }
        const { placementsBulkDelete } = result.data
        if (placementsBulkDelete.success) {
          const newArrUpdatedApplicantIds = [...(updatedApplicantIds || []), ...applicantIdsPatch]

          updatedApplicantIds = newArrUpdatedApplicantIds
          setBulkValuesKanbanUpdated(updatedApplicantIds)
        }
        return
      })
    })

    return Promise.all(arrFetchingMoveStageAPIs).then(() => {
      setRefetchMyList(true)
      setTimeout(() => {
        setCloseLockApp()
        resetBulkValuesKanban()
        setShowProcessing(false)
        setToast({
          open: true,
          type: 'success',
          title: t('notification:candidateMoveToStage', {
            stageName: stages?.find(stage => Number(stage.jobStageId) === Number(data?.jobStageId?.value))?.label
          })
        })
      }, 1000)
    })
  }

  const onApplicantsMarkAsHired = (data: IMarkAsHiredBulkForm) => {
    if (isMarkAsHired) {
      return Promise.resolve()
    }

    setShowProcessing(true)
    let updatedApplicantIds: Array<string> = []
    const patchesBulkValues = splitIntoPatches({
      arr: bulkValuesKanban,
      patchSize: PATCH_SIZE
    })

    const arrFetchingMoveStageAPIs = patchesBulkValues.map(applicantIdsPatch => {
      return markAsHired({
        applicantIds: applicantIdsPatch?.map(id => Number(id)),
        jobStageId: data?.jobStageId,
        hiredById: Number(data?.hiredById?.value),
        hiredDate: formatISO(data?.hiredDate),
        onboardDate: data?.onboardDate ? formatISO(data?.onboardDate) : undefined
      }).then(result => {
        if (result.error) {
          return catchErrorFromGraphQL({
            error: result.error,
            setToast
          })
        }
        const { applicantsBulkMarkAsHired } = result.data
        if (applicantsBulkMarkAsHired.success) {
          const newArrUpdatedApplicantIds = [...(updatedApplicantIds || []), ...applicantIdsPatch]

          updatedApplicantIds = newArrUpdatedApplicantIds
          setBulkValuesKanbanUpdated(updatedApplicantIds)
        }
        return
      })
    })

    return Promise.all(arrFetchingMoveStageAPIs).then(() => {
      setRefetchMyList(true)
      setTimeout(() => {
        setCloseLockApp()
        resetBulkValuesKanban()
        setShowProcessing(false)
        setToast({
          open: true,
          type: 'success',
          title: `${t('notification:mark_as_hired:success', {
            name: t('notification:mark_as_hired:candidate')
          })}`
        })
      }, 1000)
    })
  }

  const onApplicantsCreatePlacement = (data: ICreateEditPlacement) => {
    if (creatingPlacement) {
      return Promise.resolve()
    }

    setShowProcessing(true)
    let updatedApplicantIds: Array<string> = []
    const patchesBulkValues = splitIntoPatches({
      arr: bulkValuesKanban,
      patchSize: PATCH_SIZE
    })
    const arrFetchingMoveStageAPIs = patchesBulkValues.map(applicantIdsPatch => {
      return createPlacement({
        applicantIds: applicantIdsPatch?.map(id => Number(id)),
        jobStageId: Number(data?.jobStageId),
        hiredDate: formatISO(data.hiredDate),
        onboardDate: data.onboardDate ? formatISO(data.onboardDate) : undefined,
        endOfProbationDate: data.endOfProbationDate ? formatISO(data.endOfProbationDate) : undefined,
        salary: data.salary ? +data.salary : undefined,
        typeOfSalary: data.typeOfSalary,
        currencyOfSalary: 'USD',
        fee: data.fee ? +data.fee : undefined,
        typeOfFee: data.typeOfFee,
        revenue: data.revenue ? +data.revenue : undefined,
        currencyOfRevenue: data.currencyOfRevenue ? data.currencyOfRevenue : undefined,
        profitSplits: data.profitSplits.map(item => ({
          userId: +item.user_id?.value,
          profitPercentage: +item.profit_percentage
        })),
        hiredById: Number(data?.hiredBy?.value),
        noteContent: data?.notes?.trim() ? data?.notes?.trim() : undefined,
        //@ts-ignore
        customFields: formatSubmitCustomFieldData(data?.customFields || {})
      }).then(result => {
        if (result.error) {
          return catchErrorFromGraphQL({
            error: result.error,
            setToast
          })
        }
        const { placementsBulkCreate, applicantsBulkMarkAsHired } = result.data
        if (placementsBulkCreate?.success || applicantsBulkMarkAsHired?.success) {
          const newArrUpdatedApplicantIds = [...(updatedApplicantIds || []), ...applicantIdsPatch]

          updatedApplicantIds = newArrUpdatedApplicantIds
          setBulkValuesKanbanUpdated(updatedApplicantIds)
        }
        return
      })
    })

    return Promise.all(arrFetchingMoveStageAPIs).then(() => {
      setRefetchMyList(true)
      setTimeout(() => {
        setCloseLockApp()
        resetBulkValuesKanban()
        setShowProcessing(false)
        setToast({
          open: true,
          type: 'success',
          title: t('placements:placementCreated')
        })
      }, 1000)
    })
  }

  const onSubmitRejectApplicant = useCallback(
    (params: IEmailForm) => {
      setShowLockApp('')
      if (isDisqualifyingApplicants) {
        return Promise.resolve()
      }

      const dataSubmit = {
        ...params,
        applicantIds: bulkValuesKanban?.map(id => Number(id)),
        status: JOB_APPLICANT_STATUS.rejected,
        rejectedReason: params?.rejectedReason?.supportingObj?.name || '',
        rejectedReasonId: Number(params.rejectedReason?.value) || '',
        subject: params.sendRejectEmail ? params.subject : undefined,
        delayTime: params.delayTime || undefined,
        htmlBody: params.sendRejectEmail ? params.htmlBody : undefined,
        cc: (params?.cc || [])?.length > 0 ? params?.cc?.map(cc => cc.value) : undefined,
        bcc: (params?.bcc || [])?.length > 0 ? params?.bcc?.map(bcc => bcc.value) : undefined,
        emailTemplateId: params.sendRejectEmail ? (params?.emailTemplate?.value ? Number(params?.emailTemplate?.value) : undefined) : undefined
      }
      return disqualifyApplicants(dataSubmit).then(result => {
        if (result.error) {
          return catchErrorFromGraphQL({
            error: result.error,
            setToast
          })
        }

        const { applicantsBulkDisqualify } = result.data
        if (applicantsBulkDisqualify.success) {
          setCloseLockApp()
          setRefetchMyList(true)
          resetBulkValuesKanban()
          const stateToast: IToast = {
            open: true,
            type: 'success',
            title: `${t('notification:candidateDisqualified')}`
          }
          if (params.sendRejectEmail) {
            stateToast.description = `${t('notification:descriptionCandidateDisqualified')}`
          }
          setToast(stateToast)
        }
        return
      })
    },
    [bulkValuesKanban, isDisqualifyingApplicants]
  )

  const onAssignJobApplicants = (jobId: string) => {
    setShowLockApp('')
    if (isLoadingAssignJob) {
      return Promise.resolve()
    }

    return assignJob({
      applicantIds: bulkValuesKanban?.map(id => Number(id)),
      assignJobId: Number(jobId)
    }).then(result => {
      if (result.error) {
        catchErrorFromGraphQL({
          error: result.error,
          setToast
        })

        return Promise.resolve()
      }

      const { profilesAssignJobs } = result.data
      if (profilesAssignJobs.success) {
        setCloseLockApp()
        setRefetchMyList(true)
        resetBulkValuesKanban()
        setToast({
          open: true,
          type: 'success',
          title: t('notification:assign_job_success')
        })
      }
      return Promise.resolve()
    })
  }

  useEffect(() => {
    return () => {
      // clear data when unmout
      resetBulkValuesKanban()
    }
  }, [])

  if (showProcessing) {
    return <MovingStageBulkProgress />
  }

  if (bulkValuesKanban?.length)
    return (
      <BottomActionBar
        totalCount={candidates?.pages[0]?.meta?.totalRowCount}
        canSelectAll={false}
        data={bulkValuesKanban || []}
        isSelectedAll={false}
        onClose={handleClose}
        className={cn('z-50 mb-6', className)}
      >
        <AssignJobApplicantsSelector onSubmit={onAssignJobApplicants} excludedJobIds={[Number(currentJobId)]} />
        <TalentPoolSelector onSubmit={onAssignTalentPools} />
        <MoveStageSelector
          stages={stages}
          onSubmit={onApplicantsMoveStage}
          onSubmitMarkAsHired={onApplicantsMarkAsHired}
          onSubmitCreatePlacement={onApplicantsCreatePlacement}
          onSubmitDeletePlacements={onDeletePlacements}
          applicants={candidates?.pages?.[0]?.data as IJobApplicants[]}
        />
        <RejectApplicantsSelector applicants={candidates?.pages?.[0]?.data as IJobApplicants[]} onSubmit={onSubmitRejectApplicant} />
      </BottomActionBar>
    )

  return null
}

export default memo(BulkActions)
