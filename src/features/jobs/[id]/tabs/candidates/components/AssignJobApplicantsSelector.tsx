import type { FC } from 'react'
import React, { useCallback, useState } from 'react'
import { useTranslation } from 'react-i18next'

import configuration from '~/configuration'
import { AGENCY_TENANT } from '~/core/constants/enum'
import { Button } from '~/core/ui/Button'
import { Dialog } from '~/core/ui/Dialog'

import useJobManagement from '~/lib/features/jobs/hooks/use-job-management'
import { DEFAULT_SELECTED_JOB_STATUS_ROLE_MAP } from '~/lib/features/jobs/utilities/enum'
import useDetectCompanyWithKind from '~/lib/hooks/use-detect-company-with-kind'

import AssignJobListModal from '~/components/Jobs/AssignJobListModal'

const DialogJobList: FC<{
  onSubmit: (data: string) => Promise<void>
  open: boolean
  onClose: (val: boolean) => void
  excludedJobIds?: number[]
}> = ({ onSubmit, open, onClose, excludedJobIds = [] }) => {
  const { t } = useTranslation()
  const { isCompanyKind, isLoaded } = useDetectCompanyWithKind({
    kind: AGENCY_TENANT
  })

  const { jobPaging, filterControl } = useJobManagement({
    isCompanyKind,
    isLoaded,
    isActive: true,
    excludedJobIds
  })

  const [filter] = useState({
    page: 1,
    limit: configuration.defaultPageSize,
    search: '',
    status: DEFAULT_SELECTED_JOB_STATUS_ROLE_MAP['defaultNoDraft'].map(status => ({
      value: status,
      supportingObj: { name: '' }
    })),
    excludedJobIds
  })

  const handleSubmit = (jobId: string) => {
    onClose(false)
    onSubmit(jobId)
  }

  return (
    <Dialog open={open} size="sm" onOpenChange={onClose} isPreventAutoFocusDialog={true} label={`${t('button:assignJob')}`}>
      <AssignJobListModal
        filter={{
          ...filter,
          search: filterControl.value?.search || ''
        }}
        setFilter={filterControl.onChange}
        setOpen={onClose}
        data={jobPaging.data}
        onAssign={handleSubmit}
        hasNextPage={jobPaging.hasNextPage}
        fetchNextPage={jobPaging.fetchNextPage}
      />
    </Dialog>
  )
}

const AssignJobApplicantsSelector: FC<{
  excludedJobIds?: number[]
  onSubmit: (data: string) => Promise<void>
}> = props => {
  const { t } = useTranslation()
  const [openDialog, setOpenDialog] = useState<boolean>(false)

  return (
    <>
      <Button size="sm" iconMenus="Send" type="secondary" onClick={() => setOpenDialog(true)} label={`${t('button:assignJob')}`} />
      {openDialog && <DialogJobList open={openDialog} onClose={setOpenDialog} {...props} />}
    </>
  )
}

export default AssignJobApplicantsSelector
