'use client'

import type { DropResult } from '@hello-pangea/dnd'
import type { FC } from 'react'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import toast from 'react-hot-toast'
import { useTranslation } from 'react-i18next'

import configuration from '~/configuration'
import type { IRouterWithID } from '~/core/@types/global'
import { AGENCY_TENANT, STAGE_TYPE } from '~/core/constants/enum'
import { openAlert } from '~/core/ui/AlertDialog'
import { Badge } from '~/core/ui/Badge'
import { DebouncedInput } from '~/core/ui/DebouncedInput'
import If from '~/core/ui/If'
import { KanbanContext } from '~/core/ui/Kanban'
import { SplitButton } from '~/core/ui/SplitButton'
import { Toast } from '~/core/ui/Toast'
import { cn } from '~/core/ui/utils'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'
import { reorder } from '~/core/utilities/common'

import useCandidateStore from '~/lib/features/candidates/store'
import type { ICandidateProfile } from '~/lib/features/candidates/types'
import QueryChangeStageJobMutation from '~/lib/features/jobs/graphql/submit-change-stage-job-mutation'
import type { IJobApplicants, IJobStages, IStageTypes } from '~/lib/features/jobs/types'
import { JOB_APPLICANT_STATUS, JOB_STAGE_GROUP, JOB_STATUS_ENUM, PLACE_VIEW_PIPELINE_ENUM } from '~/lib/features/jobs/utilities/enum'
import usePermissionJob from '~/lib/features/permissions/hooks/use-permission-job'
import usePermissionPlacement from '~/lib/features/permissions/hooks/use-permission-placement'
import type { IPlaceViewPipeline } from '~/lib/features/settings/hiring-pipelines/types'
import { PLAN_FEATURE_KEYS } from '~/lib/features/settings/plans/utilities/enum'
import useDetectCompanyWithKind from '~/lib/hooks/use-detect-company-with-kind'
import { useClient } from '~/lib/hooks/use-is-client'
import { useSubmitCommon } from '~/lib/hooks/use-submit-graphql-common'
import useBoundStore from '~/lib/store'
import useLoadingBlockStore from '~/lib/store/loading-block'
import useToastStore from '~/lib/store/toast'

import CreateCandidateModal from '~/components/Candidates/CreateCandidateModal'
import SendToCandidateHook from '~/components/Candidates/Profile/components/SendToCandidate/SendToCandidateHook'
import SendToClientHook from '~/components/Candidates/Profile/components/SendToClient/SendToClientHook'
import type { ChangeJobStageWithModalActionProps } from '~/components/ChangeJobStageWithModal/ChangeJobStageWithModalView'
import useSubscriptionPlan from '~/components/Subscription/useSubscriptionPlan'
import type { ISwitchLayoutView } from '~/components/SwitchLayout/SwitchLayoutView'
import { useCreatePlacementModel } from '~/features/placements/components/CreatePlacementDialog'
import { useDirectCreatePlacementModel } from '~/features/placements/components/DirectCreatePlacementDialog'
import { useOpenPlacementDetailDialog } from '~/features/placements/components/PlacementDetailDialog'
import useDeletePlacement from '~/features/placements/components/use-delete-placement'

import BulkActions from './components/BulkActions'
import JobStageKanban from './components/JobStageKanban'

export type IRefetchActions = Array<{
  label: string
  action: 'added' | 'deleted' | 'refetch'
  extraData?: {
    id: number | string
    item?: object
  }
}>

export interface IStageKanban {
  id: number
  label: string
  jobStageId?: number | string
  stageTypeId?: number | string
  stageGroup?: string
  data: Array<IJobApplicants>
  totalCount?: number
}

type IStagesKanban = Array<IStageKanban>

interface CandidatesTabContentProps extends ChangeJobStageWithModalActionProps {
  getUUidV4?: string
  jobStatus: string
  jobStages?: IJobStages
  stageTypes: IStageTypes
  jobId?: IRouterWithID
  isDragDisabled?: boolean
  actions?: {
    configSwitchLayout: {
      path: Array<string>
      redirectUrls: Array<string>
    }
    setConfigSwitchLayout: (param: { path: Array<string>; redirectUrls: Array<string> }) => void
    switchView: ISwitchLayoutView
    setSwitchView: (param: ISwitchLayoutView) => void
  }
  jobTitle?: string
  companyName?: string
  placeViewPipeline?: IPlaceViewPipeline
  callback?: (applicantsCallback: {
    collection: Array<IJobApplicants>
    metadata?: {
      totalCount?: number
    }
  }) => void
  openHiringSuccessModel: ChangeJobStageWithModalActionProps['openHiringSuccessModel']
}

const CandidatesTabContent: FC<CandidatesTabContentProps> = ({
  getUUidV4,
  jobStatus,
  jobStages = [],
  stageTypes = [],
  jobId,
  actions,
  isDragDisabled = false,
  jobTitle,
  companyName,
  placeViewPipeline = PLACE_VIEW_PIPELINE_ENUM.jobDetail,
  callback,
  setOpenMarkAsHired,
  setApplicantCurrent,
  openHiringSuccessModel
}) => {
  const { t } = useTranslation()
  const { isClient } = useClient()
  const { setRefetchMyList } = useBoundStore()
  const { setToast } = useToastStore()
  const { setShowLockApp, setCloseLockApp } = useLoadingBlockStore()
  const { setOpenUploadDrawer, setUploadDrawerJobId } = useCandidateStore()
  const { actionJob } = usePermissionJob()
  const { trigger: triggerChangeStages, isLoading } = useSubmitCommon(QueryChangeStageJobMutation)
  const { deletePlacement } = useDeletePlacement()
  const { fullPermission: fullPermissionPlacement } = usePermissionPlacement()
  const { isCompanyKind } = useDetectCompanyWithKind({
    kind: AGENCY_TENANT
  })
  const { PlacementModalComponent, openPlacementModel } = useCreatePlacementModel()
  const { PlacementDetailModalComponent, openPlacementDetailModel } = useOpenPlacementDetailDialog()

  const [isFirstLoading, setFirstLoading] = useState(true)
  const [searchState, setSearch] = useState('')
  const [statusState, setStatusState] = useState(JOB_APPLICANT_STATUS.inprocessing)
  const [openCreateCandidate, setOpenCreateCandidate] = useState<boolean>(false)
  const [stages, setStages] = useState<IStagesKanban>(
    jobStages
      .sort((a, b) => (a.index || 0) - (b.index || 0))
      .map((item, index) => {
        return {
          id: index + 1,
          jobStageId: item.id,
          label: item.stageLabel,
          stageTypeId: item.stageTypeId,
          stageGroup: item.stageGroup,
          data: [],
          totalCount: undefined
        }
      })
  )
  const [refetchActions, setRefetchActions] = useState<IRefetchActions>([])
  const [candidate, setCandidate] = useState<ICandidateProfile>()
  const [openSendToClient, setOpenSendToClient] = useState<boolean>(false)

  const applicantsRef = useRef<any[]>([])
  const stagesRef = useRef<any[]>([])
  const isDragDisabledKanban = !actionJob.update || statusState === JOB_APPLICANT_STATUS.rejected || isDragDisabled
  const totalCountAllApplicants = stages
    .map(stage => stage.totalCount)
    .reduce((accumulator, currentValue) => Number(accumulator) + Number(currentValue), 0)

  useEffect(() => {
    const cloneStages = JSON.parse(JSON.stringify(jobStages))
    reloadKanbanAfterEditPipeline(stages, cloneStages)
  }, [jobStages])

  useEffect(() => {
    const applicantsCallback: {
      collection: Array<IJobApplicants>
      metadata: {
        totalCount: number
      }
    } = {
      collection: [],
      metadata: {
        totalCount: 0
      }
    }

    stages?.forEach((stage: IStageKanban) => {
      applicantsCallback.collection.push(...stage.data)
      applicantsCallback.metadata.totalCount += Number(stage.totalCount || 0)
    })

    callback && callback(applicantsCallback)
  }, [stages])

  useEffect(() => {
    return () => {
      setFirstLoading(false)
      applicantsRef.current = []
    }
  }, [])

  // --------------- FUNCTION HANDLER ---------------

  const reloadKanbanAfterEditPipeline = (currentStages: IStagesKanban, newStage: IJobStages) => {
    if (isFirstLoading === false) {
      applicantsRef.current = []
    }
    const sortedStages = newStage
      .sort((a, b) => (a.index || 0) - (b.index || 0))
      .map((item, index) => {
        return {
          id: index + 1,
          jobStageId: item.id,
          label: item.stageLabel,
          stageTypeId: item.stageTypeId,
          stageGroup: item.stageGroup,
          totalCount: 0,
          data: [] as Array<IJobApplicants>
        }
      })

    currentStages.forEach(s => {
      const findIndexStage = sortedStages.findIndex(t => t.jobStageId === s.jobStageId)
      if (findIndexStage !== -1 && sortedStages[findIndexStage]) {
        sortedStages[findIndexStage].data = s.data
        sortedStages[findIndexStage].totalCount = Number(s.totalCount || 0)
      }
    })

    setStages(sortedStages)
  }

  const move = (
    source = [],
    destination = [],
    droppableSource = { index: 0, droppableId: '0' },
    droppableDestination = { index: 0, droppableId: '0' }
  ) => {
    const sourceClone = Array.from(source)
    const destClone = Array.from(destination)
    const [removed] = sourceClone.splice(droppableSource.index, 1)

    destClone.splice(droppableDestination.index, 0, removed as never)

    const result = {} as {
      [key: string]: object
    }
    result[droppableSource.droppableId] = sourceClone
    result[droppableDestination.droppableId] = destClone

    return result
  }

  const { isFeatureEnabled, isUnLockFeature, data: dataPlan } = useSubscriptionPlan()
  const isShowPlacementFeature = isFeatureEnabled(PLAN_FEATURE_KEYS.placement) && isUnLockFeature(PLAN_FEATURE_KEYS.placement)

  const isOpenPlacementModal = useMemo(() => {
    return isShowPlacementFeature && !!companyName
  }, [isShowPlacementFeature, companyName])

  const { PlacementModalComponent: DirectPlacementModalComponent, openPlacementModel: openDirectPlacementModel } = useDirectCreatePlacementModel()

  const handleShowToastErrPermission = useCallback((title: string) => {
    setToast({
      open: true,
      type: 'error',
      title
    })
  }, [])

  const onDragEnd = useCallback(
    async (result: DropResult) => {
      const { source, destination } = result
      if (!destination) {
        return
      }
      const draggableId = result.draggableId
      const cloneData = JSON.parse(JSON.stringify(stages))
      const applicantList = stages
        .map(item => item.data)
        .flat()
        .find(i => Number(i.id) === Number(draggableId))

      const sInd = +source.droppableId
      const dInd = +destination.droppableId
      const sItem = jobStages.find((_, index) => String(index) === String(sInd))
      const dItem = jobStages.find((_, index) => String(index) === String(dInd))

      const canDeletePlacement = fullPermissionPlacement || applicantList?.placement?.editablePlacement || applicantList?.placement?.ownedPlacement

      const moveStage = (isCallApi = true, desApplicantDataMerge?: IJobApplicants) => {
        const dataApplicantMerge = {
          ...desApplicantDataMerge,
          jobStage: dItem
        }
        const resultMove = move(cloneData[sInd].data, cloneData[dInd].data, source, destination)

        const applicantId = Number(cloneData[sInd].data[result.source.index].id)
        const jobStageId = Number(cloneData[dInd].jobStageId)
        cloneData[sInd].data = resultMove[sInd]
        cloneData[sInd].totalCount = cloneData[sInd].totalCount - 1
        cloneData[dInd].data = dataApplicantMerge
          ? (resultMove[dInd] as any).map((item: any) =>
              item.id === dataApplicantMerge?.id ? { ...item, ...dataApplicantMerge } : { ...item, jobStage: dItem }
            )
          : resultMove[dInd]
        cloneData[dInd].totalCount = cloneData[dInd].totalCount + 1

        if (isCallApi) {
          if (isLoading) {
            return
          }

          triggerChangeStages({
            id: applicantId,
            jobStageId,
            placementId: applicantList?.placement?.id ? Number(applicantList?.placement?.id) : undefined
          }).then(result => {
            if (result.error) {
              setTimeout(() => {
                const resultMove = move(cloneData[dInd].data, cloneData[sInd].data, destination, source)

                cloneData[sInd].data = resultMove[sInd]
                cloneData[dInd].data = resultMove[dInd]

                setStages(cloneData)
                applicantsRef.current = cloneData
              }, 1500)

              return catchErrorFromGraphQL({
                error: result.error,
                page: configuration.path.jobs.list,
                setToast
              })
            }

            const { applicantsChangeStage } = result.data
            if (applicantsChangeStage.applicant.id) {
              if (cloneData[dInd]?.data?.length === 1) {
                setRefetchActions([{ label: cloneData[dInd]?.label, action: 'refetch' }])
              }

              toast.custom((to: { id: string }) => (
                <Toast
                  open
                  type="success"
                  title={
                    sItem?.stageGroup === JOB_STAGE_GROUP.hires && dItem?.stageGroup !== JOB_STAGE_GROUP.hires
                      ? isCompanyKind
                        ? `${t('notification:moveStageAndPlacementDeleted', {
                            candidateName: applicantList?.profile?.fullName,
                            stageName: dItem?.stageLabel
                          })}`
                        : `${t('notification:mark_as_hired:remove', {
                            name: applicantList?.profile?.fullName,
                            hired: '"hired"'
                          })}`
                      : sItem?.stageGroup !== JOB_STAGE_GROUP.hires && dItem?.stageGroup === JOB_STAGE_GROUP.hires
                        ? `${t('placements:placementCreated')}`
                        : t('notification:jobs:detail:changesSaved')
                  }
                  onClose={() => toast.remove(to.id)}
                />
              ))
              setApplicantCurrent && setApplicantCurrent({})
            }

            return
          })
        }

        setStages(cloneData)
        applicantsRef.current = cloneData
      }

      if (dItem?.id === sItem?.id) {
        return
      }

      if (Number(dItem?.stageTypeId) === STAGE_TYPE.clientSubmission.id && sItem?.stageGroup !== JOB_STAGE_GROUP.hires) {
        setCandidate({
          ...applicantList?.profile,
          applicantId: Number(applicantList?.id),
          company: applicantList?.job.permittedFields?.company?.value,
          job: applicantList?.job,
          accountManagers: applicantList?.job?.accountManagers
        })
        setOpenSendToClient(true)
      }

      if (dItem?.stageGroup === JOB_STAGE_GROUP.hires) {
        if (isCompanyKind) {
          if (!applicantList?.placement) {
            openPlacementModel({
              header: {
                candidateName: applicantList?.profile?.fullName,
                jobTitle: jobTitle,
                companyName: companyName
              },
              applicant: {
                hiredDate: new Date(),
                createdAt: applicantList?.createdAt,
                id: Number(applicantList?.id)
              },
              profileId: applicantList?.profile?.id,
              defaultValues: {
                applicationId: Number(applicantList?.id),
                jobStageId: jobStages.find((_, index) => String(index) === String(dInd))?.id
              },
              onPlacementCreated: placement => {
                const applicant = {
                  item: {
                    ...applicantList,
                    jobStageId: Number(jobStages.find((_, index) => String(index) === String(dInd))?.id)
                  } as any,
                  callback: () => {
                    moveStage(false)
                    setApplicantCurrent && setApplicantCurrent({})
                  }
                }
                setApplicantCurrent &&
                  setApplicantCurrent({
                    item: {
                      ...applicantList,
                      jobStageId: Number(jobStages.find((_, index) => String(index) === String(dInd))?.id)
                    } as any,
                    callback: () => {
                      moveStage(false)
                      setApplicantCurrent && setApplicantCurrent({})
                    }
                  })
                openHiringSuccessModel &&
                  openHiringSuccessModel({
                    applicant: {
                      ...applicant.item,
                      job: { title: jobTitle }
                    },
                    placement: placement
                  })
                moveStage(false, { ...applicantList, placement } as any)
                toast.custom((to: { id: string }) => (
                  <Toast open type="success" title={`${t('placements:placementCreated')}`} onClose={() => toast.remove(to.id)} />
                ))
                // setTimeout(() => {
                //   setRefetchMyList(true)
                // }, 100)
              }
            })
          } else {
            moveStage(true)
          }
        } else {
          // handle for case: move candidate in hires group
          if (sItem?.stageGroup === JOB_STAGE_GROUP.hires) {
            if (isOpenPlacementModal && !canDeletePlacement) {
              handleShowToastErrPermission(t('placements:permission:moveToAnotherStage'))
              return
            }
            if (dItem?.id !== sItem?.id) {
              moveStage(true)
            }

            return
          } else {
            if (!isCompanyKind && isOpenPlacementModal) {
              openDirectPlacementModel({
                header: {
                  candidateName: applicantList?.profile?.fullName,
                  jobTitle: jobTitle,
                  companyName: companyName
                },
                applicant: {
                  hiredDate: new Date(),
                  createdAt: applicantList?.createdAt,
                  id: Number(applicantList?.id)
                },
                profileId: applicantList?.profile?.id,
                defaultValues: {
                  applicationId: Number(applicantList?.id),
                  jobStageId: jobStages.find((_, index) => String(index) === String(dInd))?.id
                },
                onPlacementCreated: placement => {
                  const applicant = {
                    item: {
                      ...applicantList,
                      jobStageId: Number(jobStages.find((_, index) => String(index) === String(dInd))?.id)
                    } as any,
                    callback: () => {
                      moveStage(false)
                      // callback && callback()
                      setApplicantCurrent && setApplicantCurrent({})
                    }
                  }
                  setApplicantCurrent &&
                    setApplicantCurrent({
                      item: {
                        ...applicantList,
                        jobStageId: Number(jobStages.find((_, index) => String(index) === String(dInd))?.id)
                      } as any,
                      callback: () => {
                        moveStage(false)
                        setApplicantCurrent && setApplicantCurrent({})
                      }
                    })
                  openHiringSuccessModel &&
                    openHiringSuccessModel({
                      applicant: {
                        ...applicant.item,
                        job: { title: jobTitle }
                      },
                      placement: placement
                    })
                  moveStage(false, { ...applicantList, placement } as any)
                  toast.custom(to => <Toast open type="success" title={`${t('placements:placementCreated')}`} onClose={() => toast.remove(to.id)} />)
                  // setTimeout(() => {
                  //   setRefetchMyList(true)
                  // }, 100)
                }
              })
            } else {
              setApplicantCurrent &&
                setApplicantCurrent({
                  item: {
                    ...applicantList,
                    jobStageId: Number(dItem?.id)
                  } as any,
                  callback: () => {
                    setRefetchActions([{ label: dItem.stageLabel, action: 'refetch' }])
                    moveStage(false)
                    setApplicantCurrent && setApplicantCurrent({})
                  }
                })
              setOpenMarkAsHired && setOpenMarkAsHired(true)
            }

            return
          }
        }

        return
      }

      if (sItem?.stageGroup === JOB_STAGE_GROUP.hires) {
        if (isCompanyKind || isOpenPlacementModal) {
          if (isOpenPlacementModal && !canDeletePlacement) {
            handleShowToastErrPermission(t('placements:permission:deletePlacement'))
            return
          }
          const destStage = jobStages.find((_, index) => String(index) === String(dInd))
          openAlert({
            isPreventAutoFocusDialog: false,
            className: 'w-[480px]',
            title: `${t('common:modal:move_back_to_stage', {
              stageLabel: destStage?.stageLabel
            })}`,
            description: (
              <div
                dangerouslySetInnerHTML={{
                  __html: t('common:modal:move_back_to_stage_description', {
                    fullName: applicantList?.profile?.fullName,
                    stageLabel: destStage?.stageLabel
                  }) as string
                }}
              />
            ),
            actions: [
              {
                label: `${t('button:cancel')}`,
                type: 'secondary',
                size: 'sm'
              },
              {
                isCallAPI: true,
                label: `${t('button:move')}`,
                type: 'destructive',
                size: 'sm',
                onClick: async () => {
                  setShowLockApp('')
                  await deletePlacement({
                    id: +applicantList?.placement?.id
                  }).then(res => {
                    if (!!res.data?.placementsDelete?.success) {
                      setCloseLockApp()
                    }
                  })
                  await moveStage(true, {
                    ...applicantList,
                    placement: null
                  } as any)

                  if (Number(dItem?.stageTypeId) === STAGE_TYPE.clientSubmission.id) {
                    setCandidate({
                      ...applicantList?.profile,
                      applicantId: Number(applicantList?.id),
                      company: applicantList?.job?.permittedFields?.company?.value,
                      job: applicantList?.job,
                      accountManagers: applicantList?.job?.accountManagers
                    })
                    setOpenSendToClient(true)
                  }
                }
              }
            ]
          })
        } else {
          openAlert({
            isPreventAutoFocusDialog: false,
            className: 'w-[480px]',
            title: `${t('common:modal:unmark_as_hired_title')}`,
            description: t('common:modal:unmark_as_hired_description', {
              title: applicantList?.profile?.fullName
            }),
            actions: [
              {
                label: `${t('button:cancel')}`,
                type: 'secondary',
                size: 'sm'
              },
              {
                isCallAPI: true,
                label: `${t('button:unmarkAsHired')}`,
                type: 'destructive',
                size: 'sm',
                onClick: async () => await moveStage(true)
              }
            ]
          })
        }

        return
      }

      if (sInd === dInd) {
        const items = reorder(cloneData[sInd].data, source.index, destination.index)
        cloneData[sInd].data = items

        setStages(cloneData)
        applicantsRef.current = cloneData
      } else {
        moveStage(true)
      }
    },
    [callback, isLoading, jobStages, stages, fullPermissionPlacement, triggerChangeStages]
  )

  const handleCallbackLoaded = (
    newValue: IStageKanban & {
      totalCount: number
      isRefetchAll: boolean
    }
  ) => {
    if (applicantsRef?.current) {
      if (applicantsRef.current.length === stages.length) {
        if (newValue.isRefetchAll) {
          if (stagesRef.current) {
            if (stagesRef.current.length === stages.length - 1) {
              const applicantCollections = [...stagesRef.current, newValue].sort((a, b) => a.id - b.id)

              stagesRef.current = []
              setStages(applicantCollections)
            } else {
              stagesRef.current.push(newValue)
            }
          }
        } else {
          const applicantCollections = JSON.parse(JSON.stringify(stages))
          const findIndexStage = applicantCollections.findIndex((item: IStageKanban) => item.id === newValue.id)

          if (findIndexStage !== -1) {
            applicantCollections[findIndexStage].data = newValue.data
            applicantCollections[findIndexStage].totalCount = newValue.totalCount

            setStages(applicantCollections)
          }
        }
      } else {
        if (applicantsRef.current.length === stages.length - 1) {
          const applicantCollections = [...applicantsRef.current, newValue].sort((a, b) => a.id - b.id)

          applicantsRef.current.push(newValue)
          setStages(applicantCollections)
          setFirstLoading(false)
        } else {
          applicantsRef.current.push(newValue)
        }
      }
    }
  }

  return (
    <div className="relative">
      {isCompanyKind ? <PlacementModalComponent /> : <DirectPlacementModalComponent />}

      <PlacementDetailModalComponent />
      {!isCompanyKind ? (
        <If condition={openSendToClient}>
          <SendToCandidateHook
            open={openSendToClient}
            setOpen={setOpenSendToClient}
            applicantId={Number(candidate?.applicantId)}
            applicantData={candidate}
            job={candidate?.job}
            stageTypeId={Number(STAGE_TYPE.clientSubmission.id)}
            jobStages={jobStages}
          />
        </If>
      ) : (
        <If condition={openSendToClient}>
          <SendToClientHook
            open={openSendToClient}
            setOpen={setOpenSendToClient}
            applicantId={Number(candidate?.applicantId)}
            applicantData={candidate}
            job={candidate?.job}
            stageTypeId={STAGE_TYPE.clientSubmission.id}
            jobStages={jobStages}
            isNotification
          />
        </If>
      )}

      <If condition={placeViewPipeline === PLACE_VIEW_PIPELINE_ENUM.jobDetail}>
        <div className="mb-3 flex justify-between px-6">
          <div className="flex items-center justify-between rounded-xs bg-gray-100 p-px dark:bg-gray-800">
            {[
              {
                value: JOB_APPLICANT_STATUS.inprocessing,
                label: `${t('button:qualified')}`
              },
              {
                value: JOB_APPLICANT_STATUS.rejected,
                label: `${t('button:disqualified')}`
              }
            ].map(item => (
              <button
                type="button"
                key={item.value}
                className={cn(
                  `group rounded-xs px-2 py-[2px] text-gray-900 hover:text-gray-700 dark:bg-gray-700 dark:text-gray-300 dark:hover:text-gray-300`,
                  item.value === statusState ? 'bg-white shadow-2xs hover:bg-white hover:shadow-2xs' : ''
                )}
              >
                <div
                  className="flex items-center space-x-2"
                  onClick={() => {
                    setFirstLoading(true)
                    applicantsRef.current = []

                    setStatusState(item.value)
                    setTimeout(() => {
                      setRefetchMyList(true)
                    }, 100)
                  }}
                >
                  <span className="text-xs font-medium">{item.label}</span>
                  {item.value === statusState && totalCountAllApplicants ? (
                    <Badge radius="circular" size="sm" color="gray">
                      {totalCountAllApplicants}
                    </Badge>
                  ) : null}
                </div>
              </button>
            ))}
          </div>

          <div className="flex items-center space-x-2">
            <DebouncedInput
              forceOnChangeCallBack={searchState}
              value={searchState}
              onChange={value => {
                setSearch(String(value))
                setTimeout(() => {
                  setRefetchMyList(true)
                }, 100)
              }}
              onClear={() => {
                setSearch('')
                setTimeout(() => {
                  setRefetchMyList(true)
                }, 100)
              }}
              placeholder={`${t('label:placeholder:search')}`}
              size="xs"
            />
            <If condition={actionJob.create && jobStatus !== JOB_STATUS_ENUM.archived && jobStatus !== JOB_STATUS_ENUM.draft}>
              <SplitButton
                label={`${t('button:new')}`}
                size="xs"
                menu={[
                  {
                    label: `${t('button:uploadCvsResume')}`,
                    icon: 'Upload',
                    onClick: () => {
                      setUploadDrawerJobId([
                        {
                          defaultValue: true,
                          profileId: undefined,
                          jobId: String(jobId),
                          jobTitle: jobTitle
                        }
                      ])
                      setOpenUploadDrawer(true)
                    }
                  }
                ]}
                onClick={() => setOpenCreateCandidate(true)}
                htmlType="button"
                iconMenus="Plus"
              />
            </If>
          </div>
        </div>
      </If>

      <div className={placeViewPipeline === PLACE_VIEW_PIPELINE_ENUM.jobList ? 'mt-1 max-w-[calc(100vw-108px)] pt-2' : 'bg-gray-50 pl-6'}>
        {isClient ? (
          <KanbanContext onDragEnd={onDragEnd}>
            {stages.map((el, ind) => (
              <div className="relative flex max-w-[280px] flex-col" key={ind}>
                <JobStageKanban
                  getUUidV4={getUUidV4}
                  actions={actions}
                  placeViewPipeline={placeViewPipeline as IPlaceViewPipeline}
                  isDragDisabledKanban={isDragDisabledKanban}
                  el={el}
                  ind={ind}
                  searchState={searchState}
                  statusState={statusState}
                  jobId={jobId}
                  callbackLoaded={handleCallbackLoaded}
                  refetchActions={refetchActions}
                  isFirstLoading={isFirstLoading}
                  openPlacementDetailModel={openPlacementDetailModel}
                  jobTitle={jobTitle}
                  companyName={companyName}
                  jobStages={jobStages}
                  jobStatus={jobStatus}
                  isCompanyKind={isCompanyKind}
                  setOpenMarkAsHired={setOpenMarkAsHired}
                />
              </div>
            ))}
          </KanbanContext>
        ) : null}
      </div>
      <BulkActions
        candidates={{
          pages: [
            {
              data: stages.map(stage => stage.data).flat(),
              meta: {
                totalRowCount: totalCountAllApplicants
              }
            }
          ]
        }}
        stages={stages.map(stage => ({
          id: stage.id,
          jobStageId: stage.jobStageId,
          label: stage.label,
          stageGroup: stage.stageGroup,
          stageTypeId: stage.stageTypeId
        }))}
        currentJobId={jobId as string}
        className="absolute"
      />
      <CreateCandidateModal
        open={openCreateCandidate}
        setOpen={setOpenCreateCandidate}
        defaultValue={{
          jobId: [
            {
              supportingObj: {
                name: jobTitle || ''
              },
              value: jobId as string
            }
          ]
        }}
        reload={() => {
          if (stages?.[0]?.label) {
            setRefetchActions([{ label: stages?.[0]?.label, action: 'refetch' }])
          }
        }}
      />
    </div>
  )
}

export default CandidatesTabContent
