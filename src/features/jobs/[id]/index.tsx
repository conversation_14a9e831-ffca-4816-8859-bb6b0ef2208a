'use client'

import { useRouter } from 'next/navigation'
import { createContext, useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { Trans, useTranslation } from 'react-i18next'

import pathConfiguration from 'src/configuration/path'
import withPermissionFeatureProvider from 'src/hoc/with-permission-feature'
import useEnumsData from 'src/hooks/data/use-enums-data'
import useStaticData from 'src/hooks/data/use-static-data'
import configuration from '~/configuration'
import type { IFormAction, IRouterWithID } from '~/core/@types/global'
import { AGENCY_TENANT, REMOTE_STATUS } from '~/core/constants/enum'
import { PUBLIC_APP_NAME, PUBLIC_APP_URL } from '~/core/constants/env'
import useQueryGraphQL from '~/core/middleware/use-query-graphQL'
import { openAlert } from '~/core/ui/AlertDialog'
import { AvatarGroup } from '~/core/ui/AvatarGroup'
import { Badge } from '~/core/ui/Badge'
import { But<PERSON> } from '~/core/ui/Button'
import ComboboxSelect from '~/core/ui/ComboboxSelect'
import { DashedButton } from '~/core/ui/DashedButton'
import { Dialog } from '~/core/ui/Dialog'
import type { IDotColorProps } from '~/core/ui/Dot'
import { Drawer } from '~/core/ui/Drawer'
import { DropdownMenu } from '~/core/ui/DropdownMenu'
import { TypographyH5 } from '~/core/ui/Heading'
import If from '~/core/ui/If'
import type { ISelectOption } from '~/core/ui/Select'
import { Skeleton } from '~/core/ui/Skeleton'
import { Tabs, TabsContent, TabsList, TabsTrigger, TabsTriggerView } from '~/core/ui/Tabs'
import { Tooltip } from '~/core/ui/Tooltip'
import { uuidV4 } from '~/core/ui/utils'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'
import type { IDefaultPermissionsList } from '~/core/utilities/feature-permission'
import { ACTIONS_PERMISSIONS, canAccessFeature, DEFAULT_PERMISSIONS_LIST, PERMISSIONS_LIST } from '~/core/utilities/feature-permission'
import { pushStateBrowser } from '~/core/utilities/is-browser'
import { adminAndMemberCanAction, adminCanAction, limitedMemberCanAction } from '~/core/utilities/permission'

import { useQueryCareerSetting } from '~/lib/features/calendar/hooks/use-query-career-setting'
import { combineDomainCareerPage } from '~/lib/features/careers/[id]/utilities'
import QueryAgencyTenantJobDetailMini from '~/lib/features/jobs/graphql/agency-query-job-detail-mini'
import { DeleteJobMutation } from '~/lib/features/jobs/graphql/delete-job-mutation'
import type { IMemberInfo } from '~/lib/features/jobs/graphql/query-job-detail-mini'
import QueryTenantJobDetailMini from '~/lib/features/jobs/graphql/query-job-detail-mini'
import { MutationSavedJobHF } from '~/lib/features/jobs/graphql/save-job-mutation'
import QueryAddHiringMemberJobAgencyMutation from '~/lib/features/jobs/graphql/submit-add-hiring-member-job-agency-mutation'
import QueryAddHiringMemberJobMutation from '~/lib/features/jobs/graphql/submit-add-hiring-member-job-mutation'
import QueryChangeRoleJobAgencyMutation from '~/lib/features/jobs/graphql/submit-change-role-hiring-member-job-agency-mutation'
import QueryChangeRoleJobMutation from '~/lib/features/jobs/graphql/submit-change-role-hiring-member-job-mutation'
import QueryChangeRoleJobOfPendingAgencyMutation from '~/lib/features/jobs/graphql/submit-change-role-hiring-member-pending-job-agency-mutation'
import QueryChangeRoleJobOfPendingMutation from '~/lib/features/jobs/graphql/submit-change-role-hiring-member-pending-job-mutation'
import QueryChangeStatusJobMutation from '~/lib/features/jobs/graphql/submit-change-status-job-mutation'
import QueryDeleteHiringMemberJobMutation from '~/lib/features/jobs/graphql/submit-delete-hiring-member-job-mutation'
import QueryEditJobStagesMutation from '~/lib/features/jobs/graphql/submit-edit-job-stages-mutaion'
import QueryPublishJobPostingMutation from '~/lib/features/jobs/graphql/submit-publish-job-posting'
import MutationUpdateReferralReward from '~/lib/features/jobs/graphql/submit-referrals-reward'
import QueryUnPublishJobPostingMutation from '~/lib/features/jobs/graphql/submit-unpublish-job-posting'
import useHiringMemberInJob from '~/lib/features/jobs/hooks/use-hiring-member-logic'
import useShareJobLogic from '~/lib/features/jobs/hooks/use-share-job-logic'
import type { IJobApplicants, IJobForm } from '~/lib/features/jobs/types'
import { authorizingJobStageChange } from '~/lib/features/jobs/utilities/common'
import { JOB_APPLICANT_STATUS, JOB_DOT_STATUS, JOB_NOTE_TAB, JOB_STATUS_ENUM } from '~/lib/features/jobs/utilities/enum'
import usePermissionJob from '~/lib/features/permissions/hooks/use-permission-job'
import { useLogicPipelineTemplate } from '~/lib/features/settings/hiring-pipelines/hooks/user-logic-pipeline'
import type { IPipelineForm, IPipelineItem, IPipelineTemplate, IStageItem } from '~/lib/features/settings/hiring-pipelines/types'
import { PLAN_FEATURE_KEYS } from '~/lib/features/settings/plans/utilities/enum'
import useReferralSetting from '~/lib/features/settings/referrals/hooks/useReferralSetting'
import useBrowserTab from '~/lib/hooks/use-browser-tab'
import useDetectCompanyWithKind from '~/lib/hooks/use-detect-company-with-kind'
import { useSubmitCommon } from '~/lib/hooks/use-submit-graphql-common'
import { useUserCheckKindOf } from '~/lib/hooks/use-user-check-kind'
import { useRouterContext } from '~/lib/next/use-router-context'
import useBoundStore from '~/lib/store'
import useToastStore from '~/lib/store/toast'

import AppHeadMetaTags from '~/components/AppHeadMetaTags'
import BookMarkedSVG from '~/components/CareerHub/[id]/BookMarkedSVG'
import BookMarkSVG from '~/components/CareerHub/[id]/BookMarkSVG'
import ChangeJobStageWithModalView from '~/components/ChangeJobStageWithModal/ChangeJobStageWithModalView'
import ShareJobModal from '~/components/Jobs/ShareJobModal'
import LayoutGrid from '~/components/Layout/LayoutGrid'
import HiringPipelineForm from '~/components/Settings/HiringPipelines/HiringPipelineForm'
import SkeletonContainer from '~/components/Skeleton'
import useSubscriptionPlan from '~/components/Subscription/useSubscriptionPlan'
import SwitchLayoutView from '~/components/SwitchLayout/SwitchLayoutView'
import CandidateProfileContainer from '~/features/candidates/[id]'
import JobActivity from '~/features/jobs/[id]/tabs/activity/JobActivity'

import JobBoardsTab from './tabs/boards/JobBoardsTab'
import CandidatesTabContent from './tabs/candidates/CandidatesTabContent'
import ApplicationFormSettingModal from './tabs/detail/components/ApplicationFormSettingModal'
import ModalAddJobInterviewKits from './tabs/detail/components/ModalAddJobInterviewKits'
import InviteMember4HiringModalForm from './tabs/detail/InviteMember4HiringModalForm'
import JobDetailTab from './tabs/detail/JobDetailTab'
import ModalQuickUpdateHiringAndRecruiters from './tabs/detail/ModalQuickUpdateHiringAndRecruiters'
import NoteTabContent, { checkNoteForbidden } from './tabs/notes/NoteTabContent'
import JobRecommendationTab from './tabs/recommendation/JobRecommendationTab'

export const JobDetailManagementPermissionContext = createContext<IDefaultPermissionsList>(DEFAULT_PERMISSIONS_LIST)

const JobDetailManagementContainer = () => {
  const { user, currentRole, setRefetchMyList, refetchMyList, refetchMyDelete } = useBoundStore()
  const { setToast, clearToast } = useToastStore()
  const router = useRouter()
  const { params } = useRouterContext()
  const id = params?.id as IRouterWithID
  const { t, i18n } = useTranslation()
  const { onShowAlertConfirmDelete, transformData4Form, onShowAlertClose4HiringPipelineFormModal } = useLogicPipelineTemplate()
  const { isFeatureEnabled, isUnLockFeature, showIconFeatureFromAdminSetting, data: dataTenantSettingPlan } = useSubscriptionPlan()
  const { dataReferral } = useReferralSetting({
    suspend: !dataTenantSettingPlan || !isFeatureEnabled(PLAN_FEATURE_KEYS.referral) || !isUnLockFeature(PLAN_FEATURE_KEYS.referral)
  })
  const { listRecommendedMembersJob, triggerRecommendedMembersJob } = useHiringMemberInJob(Number(id))
  const { actionJob, canAccessJobNote } = usePermissionJob()
  const { data: careerSetting, trigger: triggerCareerSetting } = useQueryCareerSetting({
    variables: {},
    shouldPause: true
  })
  const { isCompanyKind } = useDetectCompanyWithKind({ kind: AGENCY_TENANT })
  const { userIsAsClient } = useUserCheckKindOf()
  const { valueShareJobWithCondition } = useShareJobLogic()

  const stageGroup = useEnumsData({
    enumType: 'StageGroup',
    locale: i18n.language
  })
  const stageTypes = useStaticData({
    keyName: 'agency_stageTypes',
    locale: i18n.language
  })

  const [isOpenInviteMember, setOpenInviteMember] = useState(false)
  const [openShareModal, setOpenShareModal] = useState<boolean>(false)
  const [openEditPipeline, setOpenEditPipeline] = useState(false)
  const [showQuickUpdateHiringModal, toggleQuickUpdateModal] = useState(false)
  const [showInputPostJobModal, setShowInputPostJobModal] = useState(false)
  const [openJobInterviewKitsModal, setOpenJobInterviewKitsModal] = useState(false)
  const [anyChangesForm, setAnyChangesFrom] = useState(false)
  const [configSwitchLayout, setConfigSwitchLayout] = useState({
    path: [`${configuration.path.candidates.list}/`],
    redirectUrls: ['']
  })
  const [openApplicationFormSetting, setOpenApplicationFormSetting] = useState(false)

  const isAdminAndMemberRole = adminAndMemberCanAction(currentRole?.code)

  const { trigger: triggerUpdatePipelineStages, isLoading: isLoadingUpdatePipeline } = useSubmitCommon(QueryEditJobStagesMutation)

  const { trigger: triggerFetchDetail, data } = useQueryGraphQL({
    query: isCompanyKind ? QueryAgencyTenantJobDetailMini : QueryTenantJobDetailMini,
    variables: { id: Number(id) },
    shouldPause: false
  })

  const { trigger: deleteJob, isLoading: isLoadingDeleteJob } = useSubmitCommon(DeleteJobMutation)

  const { trigger: triggerChangeStatus, isLoading } = useSubmitCommon(QueryChangeStatusJobMutation)

  const jobStagesWithCondition = useMemo(() => {
    const initDataStages = data?.jobsShow?.jobStages || []
    if (isCompanyKind && userIsAsClient()) {
      return initDataStages.filter(i => i?.clientShared)
    }
    return initDataStages
  }, [data?.jobsShow?.jobStages])

  // API for Modal hiring teams //
  const { trigger: triggerChangeRoleOfJob, isLoading: isLoadingChangeRoleOfJob } = useSubmitCommon(
    isCompanyKind ? QueryChangeRoleJobAgencyMutation : QueryChangeRoleJobMutation
  )

  const { trigger: triggerChangeRoleOfJobOfPending, isLoading: isLoadingChangeRoleOfJobOfPending } = useSubmitCommon(
    isCompanyKind ? QueryChangeRoleJobOfPendingAgencyMutation : QueryChangeRoleJobOfPendingMutation
  )

  const { trigger: triggerAddHiringMember2Job, isLoading: isLoadingAddHiringMember2Job } = useSubmitCommon(
    isCompanyKind ? QueryAddHiringMemberJobAgencyMutation : QueryAddHiringMemberJobMutation
  )

  const { trigger: triggerDeleteHiringMemberOfJob, isLoading: isLoadingDeleteHiringMemberOfJob } = useSubmitCommon(QueryDeleteHiringMemberJobMutation)

  const { trigger: triggerPublishJobPosting, isLoading: isPublishing } = useSubmitCommon(QueryPublishJobPostingMutation)

  const { trigger: triggerUnPublishJobPosting, isLoading: isUnPublishing } = useSubmitCommon(QueryUnPublishJobPostingMutation)
  const { trigger: triggerUpdateReferralReward, isLoading: isLoadingUpdateReferralReward } = useSubmitCommon(MutationUpdateReferralReward)

  const { trigger: bookmarkAPI, isLoading: bookMarkLoading } = useSubmitCommon(MutationSavedJobHF)

  const checkConditionShareJob = valueShareJobWithCondition({
    enablingReferral: dataReferral?.values?.enabling || false,
    enablingCareerSite: user.currentTenant?.careerSiteSettings?.enablingCareerSiteSetting || false,
    enableJobReferral: data?.jobsShow?.jobReferable || false,
    jobStatus: data?.jobsShow?.status || ''
  })

  const isBookMarked = (data?.jobsShow?.listSavedJobMemberIds || []).filter(id => Number(id) === Number(user.id)).length > 0

  const [applicants, setApplicants] = useState<{
    collection: Array<IJobApplicants>
    metadata?: {
      totalCount?: number
    }
  }>({
    collection: [],
    metadata: {
      totalCount: undefined
    }
  })

  useEffect(() => {
    if (id) {
      triggerFetchDetail()
    }
  }, [id, triggerFetchDetail, isCompanyKind])

  const companyId = useMemo(() => {
    return data?.jobsShow?.permittedFields?.company?.value?.id || data?.jobsShow?.company?.id
  }, [data])

  const pipelineValueEdit = useMemo(() => {
    if (data?.jobsShow?.jobStages && stageGroup) {
      const pipelineValue = {
        name: 'edit-pipeline',
        pipelineStages: data?.jobsShow?.jobStages.map(js => ({
          ...js,
          stageType: { id: js.stageTypeId }
        }))
      }
      // Mapping is wrong between `pipelineStages` from pipelineValue - temp solution is ignore first
      return transformData4Form(stageGroup, pipelineValue as unknown as IPipelineTemplate)
    }
    return {}
  }, [data?.jobsShow?.jobStages, stageGroup])

  const deleteJobCallback = useCallback(async () => {
    if (isLoadingDeleteJob) {
      return false
    }
    return await deleteJob({
      id: Number(data?.jobsShow?.id)
    }).then(result => {
      if (result.error) {
        catchErrorFromGraphQL({
          error: result.error,
          page: configuration.path.jobs.list,
          setToast
        })

        return false
      }
      const { jobsDelete } = result.data

      if (jobsDelete.success) {
        setToast({
          open: true,
          type: 'success',
          title: t('notification:jobs:jobCard:deleteJob') || ''
        })
        router.push(configuration.path.jobs.list)
      }

      return true
    })
  }, [data, isLoadingDeleteJob, deleteJob, setToast, t])

  const showAlertConfirmDeleteJob = () => {
    openAlert({
      className: 'w-[480px]',
      title: t('label:optionJobAction:deleteJob:title') || '',
      description: data?.jobsShow?.title ? (
        <Trans
          i18nKey="label:optionJobAction:deleteJob:descriptionDeleted"
          values={{
            jobTitle: data?.jobsShow?.title
          }}
        >
          <span className="font-medium text-gray-900" />
        </Trans>
      ) : (
        ''
      ),
      actions: [
        {
          label: t('button:cancel') || '',
          type: 'secondary',
          size: 'sm'
        },
        {
          isCallAPI: true,
          label: t('button:delete') || '',
          type: 'destructive',
          size: 'sm',
          onClick: async () => await deleteJobCallback()
        }
      ]
    })
  }

  const deleteHiringMemberOfJobCallback = useCallback(
    async (jobRecruiterId: number, userName: string | undefined) => {
      if (isLoadingDeleteHiringMemberOfJob) {
        return false
      }

      return await triggerDeleteHiringMemberOfJob({
        id: Number(id),
        jobRecruiterId
      }).then(result => {
        if (result.error) {
          catchErrorFromGraphQL({
            error: result.error,
            page: configuration.path.jobs.list,
            setToast
          })

          return false
        }

        const { jobsUpdate } = result.data

        if (jobsUpdate.job.id) {
          triggerFetchDetail()
          setToast({
            open: true,
            type: 'success',
            title: t('notification:success_remove_member_from_job', {
              name: userName
            })
          })
        }

        return true
      })
    },
    [isLoadingDeleteHiringMemberOfJob, triggerDeleteHiringMemberOfJob, id, setToast, triggerFetchDetail, t]
  )

  const addHiringMemberOfJobCallback = useCallback(
    async (teamMemberIds: number[], userName: string | undefined) => {
      if (isLoadingAddHiringMember2Job) {
        return false
      }

      const params = isCompanyKind
        ? {
            id: Number(id),
            recruiterIds: [...teamMemberIds, ...(data?.jobsShow?.recruiters || []).map(r => Number(r.id))]
          }
        : {
            id: Number(id),
            teamMemberIds: [...teamMemberIds, ...(data?.jobsShow?.teamMembers || []).map(r => Number(r.id))]
          }
      // params submit
      return await triggerAddHiringMember2Job(params).then(result => {
        if (result.error) {
          catchErrorFromGraphQL({
            error: result.error,
            page: configuration.path.jobs.list,
            setToast
          })

          return false
        }

        const { jobsUpdate } = result.data

        if (jobsUpdate.job.id) {
          triggerFetchDetail()
          setToast({
            open: true,
            type: 'success',
            title: t('notification:success_add_member_to_job', {
              name: userName
            })
          })
        }

        return true
      })
    },
    [
      isLoadingAddHiringMember2Job,
      triggerAddHiringMember2Job,
      id,
      setToast,
      triggerFetchDetail,
      t,
      data?.jobsShow.teamMembers,
      data?.jobsShow.recruiters,
      isCompanyKind
    ]
  )

  const changeMemberRoleOfJobCallback = useCallback(
    async (jobRecruiterId: number, jobRecruiterResponsibility: string) => {
      if (isLoadingChangeRoleOfJob) {
        return
      }

      triggerChangeRoleOfJob({
        id: Number(id),
        jobRecruiterId,
        jobRecruiterResponsibility
      }).then(result => {
        if (result.error) {
          return catchErrorFromGraphQL({
            error: result.error,
            page: configuration.path.jobs.list,
            setToast
          })
        }

        const { jobsUpdate } = result.data

        if (jobsUpdate.job.id) {
          triggerFetchDetail()
          setToast({
            open: true,
            type: 'success',
            title: t('notification:roleUpdated')
          })
        }

        return
      })
    },
    [isLoadingChangeRoleOfJob, triggerChangeRoleOfJob, id, setToast, triggerFetchDetail, t]
  )

  const changeMemberRoleJobOfPendingCallback = useCallback(
    async (jobRecruiterId: number, jobRecruiterResponsibility: string) => {
      if (isLoadingChangeRoleOfJobOfPending) {
        return
      }

      triggerChangeRoleOfJobOfPending({
        id: Number(id),
        memberInvitationId: jobRecruiterId,
        jobRecruiterResponsibility
      }).then(result => {
        if (result.error) {
          return catchErrorFromGraphQL({
            error: result.error,
            page: configuration.path.jobs.list,
            setToast
          })
        }

        const { jobsUpdate } = result.data

        if (jobsUpdate.job.id) {
          triggerFetchDetail()
          setToast({
            open: true,
            type: 'success',
            title: t('notification:roleUpdated')
          })
        }

        return
      })
    },
    [isLoadingChangeRoleOfJobOfPending, triggerChangeRoleOfJobOfPending, id, setToast, triggerFetchDetail, t]
  )

  const changeStatusCallback = useCallback(
    async (status: string) => {
      if (isLoading) {
        return
      }

      triggerChangeStatus({
        id: Number(id),
        status
      }).then(result => {
        if (result.error) {
          return catchErrorFromGraphQL({
            error: result.error,
            page: configuration.path.jobs.list,
            setToast
          })
        }

        const { jobsUpdate } = result.data

        if (jobsUpdate.job.id) {
          triggerFetchDetail()
          setToast({
            open: true,
            type: 'success',
            title: t('notification:jobs:detail:changesSaved')
          })
        }

        return
      })
    },
    [isLoading, triggerChangeStatus, id, setToast, triggerFetchDetail, t]
  )

  const getListAvatarGroup = () => {
    if (!data?.jobsShow) return []
    const supportingObj: { [id: string]: IMemberInfo } = {}
    const listAvatars: IMemberInfo[] = data?.jobsShow?.jobRecruiters?.map(jr => ({
      ...jr.user,
      roles: [{ name: jr.responsibilityDescription }]
    }))

    listAvatars.forEach((member: IMemberInfo) => {
      if (!supportingObj[member.id]) {
        supportingObj[member.id] = member
      }
    })
    return Object.keys(supportingObj).map(id => supportingObj[id])
  }
  const updateReferralReward = useCallback(
    async (data: IJobForm) => {
      if (isLoadingUpdateReferralReward) {
        return
      }
      triggerUpdateReferralReward({
        id: Number(id),
        ...data
      }).then(result => {
        if (result.error) {
          return catchErrorFromGraphQL({
            error: result.error,
            page: configuration.path.jobs.detailTab(Number(id)),
            setToast
          })
        }

        const { jobsUpdate } = result.data
        if (jobsUpdate) {
          triggerFetchDetail()
        }
        return
      })
    },
    [isLoadingUpdateReferralReward, setToast, t, triggerUpdateReferralReward, id]
  )

  const tabControl = useBrowserTab({
    defaultValue: 'candidates',
    queryKeyName: 'tabs',
    excludeQueryKeysName: ['id']
  })

  const onSubmitEditPipeline = useCallback(
    async (data: IPipelineForm, formAction: IFormAction) => {
      if (isLoadingUpdatePipeline) {
        return
      }
      const { pipelineStages } = data
      const jobStages = [
        ...(pipelineStages || []).flatMap(i =>
          i.stages.map(s => ({
            id: s.id?.includes('item') ? undefined : s.id,
            stageLabel: s.stage,
            clientShared: s?.clientShared,
            stageTypeId: s.stageType?.value
          }))
        ),
        ...(pipelineStages || []).flatMap(i =>
          (i?.stagesDeleted || []).map((s, index) => ({
            id: s.id?.includes('item') ? undefined : s.id,
            _destroy: true,
            stageLabel: s.stage,
            clientShared: s?.clientShared,
            stageTypeId: s.stageType?.value
          }))
        )
      ].map((p, index) => {
        if (!p.id) delete p.id
        return { ...p, index }
      })
      triggerUpdatePipelineStages({ id: Number(id), jobStages }).then(result => {
        if (result.error) {
          return catchErrorFromGraphQL({
            error: result.error,
            page: configuration.path.jobs.create,
            formAction,
            setToast
          })
        }

        const { jobsUpdate } = result.data
        if (jobsUpdate.job.id) {
          triggerFetchDetail()
          setOpenEditPipeline(false)
          setTimeout(() => {
            setToast({
              open: true,
              type: 'success',
              title: t('notification:hiring_pipeline_updated')
            })
          }, 300)
        }
        return
      })
    },
    [isLoadingUpdatePipeline, triggerFetchDetail, triggerUpdatePipelineStages, id, setToast, t]
  )

  const onRefetchJobDetail = useCallback(async () => {
    setTimeout(() => {
      triggerFetchDetail()
    }, 250)
  }, [triggerFetchDetail])

  const onShowInputModal = useCallback(async () => {
    clearToast()
    setTimeout(() => {
      setShowInputPostJobModal(true)
    }, 200)
  }, [clearToast])

  const onPublishJobPosting = useCallback(
    async (sourceDetail: string, label: string, extraJobInfo?: object) => {
      if (isPublishing) {
        return
      }

      triggerPublishJobPosting({
        id: Number(data?.jobsShow?.id),
        sourceDetail,
        ...(extraJobInfo || {})
      }).then(result => {
        if (result.error) {
          return catchErrorFromGraphQL({
            error: result.error,
            page: configuration.path.jobs.list,
            setToast,
            callbackHandleStatusError422: (keys, blockedErrorCode) => {
              if (blockedErrorCode === 'CONTRACT_ERROR') {
                onShowInputModal()
              }
            }
          })
        }
        setShowInputPostJobModal(false)
        setTimeout(() => {
          const { publishJobPosting } = result.data
          if (publishJobPosting.success) {
            triggerFetchDetail()
            setToast({
              open: true,
              type: 'success',
              title: t('notification:request_to_post_this_job_sent', {
                name: label
              })
            })
          }
        }, 300)
        return
      })
    },
    [isPublishing, triggerPublishJobPosting, data?.jobsShow?.id, setToast, onShowInputModal, triggerFetchDetail, t]
  )

  const onUnPublishJobPosting = useCallback(
    async (sourceDetail: string, label: string) => {
      if (isUnPublishing) {
        return
      }

      triggerUnPublishJobPosting({
        id: Number(data?.jobsShow?.id),
        sourceDetail
      }).then(result => {
        if (result.error) {
          return catchErrorFromGraphQL({
            error: result.error,
            page: configuration.path.jobs.list,
            setToast
          })
        }
        const { unpublishJobPosting } = result.data
        if (unpublishJobPosting.success) {
          triggerFetchDetail()
          setToast({
            open: true,
            type: 'success',
            title: t('notification:request_to_unpublish_this_job_sent', {
              name: label
            })
          })
        }

        return
      })
    },
    [isUnPublishing, data?.jobsShow?.id, triggerUnPublishJobPosting, setToast, triggerFetchDetail, t]
  )

  const onConfirmChangeStatus = (value: ISelectOption) => {
    const defaultMsg = t('common:modal:change_job_status_description_0', {
      name: value.supportingObj?.name
    })
    const description = `${defaultMsg} ${
      JOB_STATUS_ENUM.archived === value.value
        ? `${t('common:modal:change_job_status_description_1')}`
        : `${t('common:modal:change_job_status_description_2')}`
    }`

    return openAlert({
      isPreventAutoFocusDialog: false,
      className: 'w-[480px]',
      title: `${t('common:modal:change_job_status_title')}`,
      description,
      actions: [
        {
          label: `${t('button:cancel')}`,
          type: 'secondary',
          size: 'sm'
        },
        {
          isCallAPI: true,
          label: `${t('button:changeStatusAnyway')}`,
          type: 'destructive',
          size: 'sm',
          onClick: async () => await changeStatusCallback(value?.value as string)
        }
      ]
    })
  }

  const onDeleteStageCustom = (pipelines: IPipelineItem[], value: IStageItem, onChange: (pipelines: IPipelineItem[]) => void, areaType?: string) => {
    let relateApplicantsData = undefined
    if (applicants?.collection?.length && applicants.collection.filter((s: IJobApplicants) => s.jobStage.id.toString() === value.id).length > 0) {
      const qualifiedCount = applicants.collection.filter(
        ap => ap.status === JOB_APPLICANT_STATUS.inprocessing && ap.jobStage.id.toString() === value.id
      ).length
      const disQualifiedCount = applicants.collection.filter(
        ap => ap.status === JOB_APPLICANT_STATUS.rejected && ap.jobStage.id.toString() === value.id
      ).length
      relateApplicantsData = { qualifiedCount, disQualifiedCount }
    }
    onShowAlertConfirmDelete(relateApplicantsData, value, pipelines, onChange, areaType)
  }

  const onToggleInviteMember = (isShowInviteMember: boolean) => {
    isShowInviteMember ? toggleQuickUpdateModal(false) : setOpenInviteMember(false)
    setTimeout(() => {
      isShowInviteMember ? setOpenInviteMember(true) : toggleQuickUpdateModal(true)
    }, 200)
  }

  const onReloadFetchListMemberOfJob = () => {
    triggerFetchDetail()
  }

  const drawerContainerRef = useRef<HTMLDivElement>(null)
  const returnUrl = id ? configuration.path.jobs.detail(Number(id)) : ''

  const canViewJobBoard = () => {
    return (
      adminAndMemberCanAction(currentRole?.code) &&
      isFeatureEnabled(PLAN_FEATURE_KEYS.job_board) &&
      showIconFeatureFromAdminSetting(PLAN_FEATURE_KEYS.job_board, user?.ownTenant || false) &&
      user?.currentTenant?.careerSiteSettings?.enablingCareerSiteSetting
    )
  }

  const canViewRecommendation = () => {
    return (
      isFeatureEnabled(PLAN_FEATURE_KEYS.recommendation) &&
      showIconFeatureFromAdminSetting(PLAN_FEATURE_KEYS.recommendation, user?.ownTenant || false) &&
      !limitedMemberCanAction(currentRole?.code)
    )
  }

  return (
    <>
      <AppHeadMetaTags
        title={`${t(`common:seo:jobDetail`, {
          jobDetail: String(data?.jobsShow?.title || 'Job Detail'),
          PUBLIC_APP_NAME
        })}`}
      />

      <LayoutGrid>
        <JobDetailManagementPermissionContext.Provider value={actionJob}>
          <SwitchLayoutView
            configurations="ghost"
            returnUrl={returnUrl}
            paths={configSwitchLayout.path}
            redirectUrls={configSwitchLayout.redirectUrls}
          >
            {({ switchView, setSwitchView, renderedActions }) => {
              return (
                <>
                  <Tabs {...tabControl} className="flex h-full flex-col overflow-hidden">
                    <div className="flex flex-none justify-between border-b border-b-gray-100 bg-white px-6 pt-3">
                      <div className="flex flex-col space-y-3">
                        <SkeletonContainer
                          showMoreLabel={`${t('common:infinity:showMore')}`}
                          useLoading={false}
                          isFirstLoading={data?.jobsShow === undefined}
                          renderCustomSkeleton={<Skeleton className="h-[26px] w-full rounded-full" />}
                        >
                          <div className="flex min-h-[26px] items-center space-x-3">
                            <TypographyH5>{data?.jobsShow.title}</TypographyH5>
                            <If condition={data?.jobsShow?.permittedFields?.remoteStatus}>
                              {data?.jobsShow?.permittedFields?.remoteStatus?.value === REMOTE_STATUS.FULLY_REMOTE && (
                                <If condition={data?.jobsShow?.permittedFields?.remoteStatusDescription?.value}>
                                  <Badge type="iconLeading" icon="Wifi" color="green" radius="circular" size="md">
                                    {data?.jobsShow?.permittedFields?.remoteStatusDescription?.value}
                                  </Badge>
                                </If>
                              )}
                              {data?.jobsShow?.permittedFields?.remoteStatus?.value === REMOTE_STATUS.HYDRID_REMOTE && (
                                <If condition={data?.jobsShow?.permittedFields?.remoteStatusDescription?.value}>
                                  <Badge type="iconLeading" icon="Signal" color="green" radius="circular" size="md">
                                    {data?.jobsShow?.permittedFields?.remoteStatusDescription?.value}
                                  </Badge>
                                </If>
                              )}
                            </If>
                          </div>
                        </SkeletonContainer>

                        <TabsList size="sm">
                          <TabsTrigger size="sm" value="candidates">
                            <TabsTriggerView
                              size="sm"
                              session={{
                                value: 'candidates',
                                label: `${t('label:tab:candidates')}`
                              }}
                            />
                          </TabsTrigger>
                          <TabsTrigger size="sm" value="details">
                            <TabsTriggerView
                              size="sm"
                              session={{
                                value: 'details',
                                label: `${t('label:tab:details')}`
                              }}
                            />
                          </TabsTrigger>
                          {data?.jobsShow.status && !checkNoteForbidden(data.jobsShow.status as JOB_STATUS_ENUM, canAccessJobNote) && (
                            <TabsTrigger size="sm" value="notes">
                              <TabsTriggerView
                                size="sm"
                                session={{
                                  value: JOB_NOTE_TAB,
                                  label: `${t('label:tab:notes')}`
                                }}
                              />
                            </TabsTrigger>
                          )}
                          <If condition={canViewJobBoard()}>
                            <TabsTrigger size="sm" value="jobBoards">
                              <TabsTriggerView
                                size="sm"
                                session={{
                                  value: 'jobBoards',
                                  label: `${t('label:tab:job_boards')}`
                                }}
                              />
                            </TabsTrigger>
                          </If>
                          <If condition={!userIsAsClient()}>
                            <TabsTrigger size="sm" value="activities">
                              <TabsTriggerView
                                size="sm"
                                session={{
                                  value: 'activities',
                                  label: `${t('label:tab:activities')}`
                                }}
                              />
                            </TabsTrigger>
                          </If>
                          <If condition={canViewRecommendation()}>
                            <TabsTrigger size="sm" value="jobRecommendation">
                              <TabsTriggerView
                                size="sm"
                                session={{
                                  value: 'jobRecommendation',
                                  label: `${t('job:detail:recommendation:tab:title')}`
                                }}
                              />
                            </TabsTrigger>
                          </If>
                        </TabsList>
                      </div>
                      <div className="flex flex-col items-end space-y-3.5">
                        <div className="flex space-x-2">
                          <div className="relative">
                            <ComboboxSelect
                              closeOnSelect
                              dropdownMenuClassName="w-[356px]! right-0"
                              buttonClassName={isAdminAndMemberRole ? 'min-w-[88px]' : ''}
                              isDisabled={!isAdminAndMemberRole || userIsAsClient()}
                              hideDropdownIcon={!isAdminAndMemberRole}
                              menuOptionAlign="end"
                              value={
                                data?.jobsShow?.status
                                  ? ({
                                      value: data.jobsShow.status,
                                      dot: JOB_DOT_STATUS(data.jobsShow.status || 'gray') as IDotColorProps,
                                      supportingObj: {
                                        name: {
                                          internal: t('label:job_status:internal'),
                                          publish: t('label:job_status:publish'),
                                          archived: t('label:job_status:archived'),
                                          draft: t('label:job_status:draft')
                                        }[data.jobsShow.status]
                                      }
                                    } as ISelectOption)
                                  : undefined
                              }
                              size="sm"
                              isClearable={false}
                              isSearchable={false}
                              isLoading={isLoading}
                              onChange={option => {
                                const singleValue = option as ISelectOption
                                if (singleValue?.value !== data?.jobsShow.status) {
                                  if (data?.jobsShow?.status === JOB_STATUS_ENUM.publish) {
                                    onConfirmChangeStatus(singleValue)
                                  } else {
                                    changeStatusCallback(singleValue.value)
                                  }
                                }
                              }}
                              configSelectOption={{
                                supportingText: ['description']
                              }}
                              options={authorizingJobStageChange(data?.jobsShow.status || JOB_STATUS_ENUM.internal, [
                                {
                                  value: JOB_STATUS_ENUM.internal,
                                  supportingObj: {
                                    name: t('label:job_status:internal'),
                                    description: t('label:optionJobStatus:internal') || ''
                                  }
                                },
                                {
                                  value: JOB_STATUS_ENUM.publish,
                                  supportingObj: {
                                    name: t('label:job_status:publish'),
                                    description: t('label:optionJobStatus:publish') || ''
                                  }
                                },
                                {
                                  value: JOB_STATUS_ENUM.archived,
                                  supportingObj: {
                                    name: t('label:job_status:archived'),
                                    description: t('label:optionJobStatus:archived') || ''
                                  }
                                }
                              ])}
                            />
                          </div>
                          {!userIsAsClient() && (
                            <Button
                              type="secondary"
                              size="xs"
                              onClick={() => {
                                bookmarkAPI({
                                  id: Number(data?.jobsShow.id),
                                  saveJob: isBookMarked ? false : true
                                }).then(result => {
                                  triggerFetchDetail && triggerFetchDetail()
                                  setToast({
                                    open: true,
                                    type: 'success',
                                    title: !isBookMarked ? t('notification:jobs:jobCard:jobSaved') : t('notification:jobs:jobCard:unJobSaved')
                                  })
                                })
                              }}
                              className="w-full cursor-pointer"
                            >
                              {isBookMarked ? <BookMarkedSVG className="h-3 w-3" /> : <BookMarkSVG className="h-3 w-3" />}
                            </Button>
                          )}
                          {data?.jobsShow.status && [JOB_STATUS_ENUM.publish, JOB_STATUS_ENUM.internal].includes(data?.jobsShow.status) ? (
                            <div>
                              <Tooltip
                                position="bottom"
                                content={
                                  checkConditionShareJob?.jobList?.shareIcon
                                    ? t('label:optionJobAction:shareJob')
                                    : t('label:optionJobAction:cannotShareJob')
                                }
                              >
                                <Button
                                  type="secondary"
                                  isDisabled={!checkConditionShareJob?.jobList?.shareIcon}
                                  size="xs"
                                  iconMenus="Share2"
                                  onClick={e => {
                                    setOpenShareModal(true)
                                    if (!careerSetting) triggerCareerSetting()
                                  }}
                                />
                              </Tooltip>
                            </div>
                          ) : null}
                          <If condition={!userIsAsClient()}>
                            {data?.jobsShow.status === JOB_STATUS_ENUM.archived ? (
                              <Button isDisabled size="xs" icon="leading" iconMenus="MoreHorizontal" type="secondary" />
                            ) : limitedMemberCanAction(currentRole?.code) && data?.jobsShow.status === JOB_STATUS_ENUM.internal ? null : (
                              <div className="relative">
                                <DropdownMenu
                                  side="bottom"
                                  align="end"
                                  menuClassName="max-w-[188px]"
                                  trigger={<Button size="xs" icon="leading" iconMenus="MoreHorizontal" type="secondary" />}
                                  menu={[
                                    checkConditionShareJob?.jobList?.viewJob && data?.jobsShow.status !== JOB_STATUS_ENUM.draft
                                      ? {
                                          label: t('label:optionJobAction:viewJob') || '',
                                          icon: 'ExternalLink',
                                          onClick: () =>
                                            window.open(
                                              `${PUBLIC_APP_URL}${data?.jobsShow.tenant?.careerSiteSettings?.canonical_url}${data?.jobsShow?.slug}`
                                            )
                                        }
                                      : undefined,
                                    actionJob.update
                                      ? {
                                          label: t('label:optionJobAction:editJob') || '',
                                          icon: 'Edit3',
                                          onClick: () => router.push(configuration.path.jobs.edit(Number(id || 0)))
                                        }
                                      : undefined,
                                    checkConditionShareJob?.jobList?.moreShare && data?.jobsShow.status !== JOB_STATUS_ENUM.draft
                                      ? {
                                          label: t('label:optionJobAction:shareJob') || '',
                                          icon: 'Share2',
                                          onClick: () => {
                                            setOpenShareModal(true)
                                            if (!careerSetting) triggerCareerSetting()
                                          }
                                        }
                                      : undefined,

                                    actionJob.update || actionJob.create
                                      ? {
                                          label: t('label:optionJobAction:duplicate') || '',
                                          icon: 'Files',
                                          onClick: () => router.push(configuration.path.jobs.duplicateJob(String(id)))
                                        }
                                      : undefined,
                                    isAdminAndMemberRole
                                      ? {
                                          label: `${t('button:editHiringProcess')}`,
                                          icon: 'StretchVertical',
                                          onClick: () => setOpenEditPipeline(true)
                                        }
                                      : undefined,
                                    adminCanAction(currentRole?.code) || user.ownTenant
                                      ? {
                                          color: 'red',
                                          label: `${t('button:delete')}`,
                                          icon: 'Trash2',
                                          onClick: () => showAlertConfirmDeleteJob()
                                        }
                                      : undefined
                                  ]}
                                />
                              </div>
                            )}
                          </If>
                        </div>
                        <ShareJobModal
                          shareInternal={checkConditionShareJob.shareInternal}
                          sharePublic={checkConditionShareJob.sharePublic}
                          urlReferral={`${PUBLIC_APP_URL}${pathConfiguration.careerHub.jobDetail({
                            tenantSlug: data?.jobsShow?.tenant?.slug || '',
                            jobId: data?.jobsShow?.id.toString() || ''
                          })}`}
                          open={openShareModal}
                          setOpen={setOpenShareModal}
                          url={
                            careerSetting
                              ? `${combineDomainCareerPage(careerSetting)}${pathConfiguration?.careers.jobDetail({
                                  tenantSlug: data?.jobsShow?.tenant?.slug || '',
                                  jobId: data?.jobsShow?.slug || ''
                                })}?utm_medium=internal_social_share`
                              : ''
                          }
                        />
                        <div className="flex flex-row">
                          <AvatarGroup
                            orderPlacement="rightToLeft"
                            size="sm"
                            className="flex-row-reverse space-x-reverse"
                            maxUser={3}
                            toolTipPosition="left"
                            toolTipPositionAvatarCount="left"
                            source={getListAvatarGroup().map(item => ({
                              id: Number(item?.id),
                              alt: item?.fullName || item?.email,
                              src: item?.avatarVariants?.thumb?.url,
                              defaultColour: item?.defaultColour,
                              tooltip: `${item?.fullName || item?.email}`
                            }))}
                            extraControl={
                              isAdminAndMemberRole && data?.jobsShow.status !== JOB_STATUS_ENUM.archived ? (
                                <Tooltip content={t('tooltip:add_member')}>
                                  <div className="relative">
                                    <DashedButton onClick={() => toggleQuickUpdateModal(true)} iconMenus="Plus" size="md" className="bg-white" />
                                  </div>
                                </Tooltip>
                              ) : undefined
                            }
                          />
                        </div>
                      </div>
                    </div>

                    <TabsContent value="candidates" className="mt-3 overflow-y-auto">
                      <If
                        condition={(data?.jobsShow?.jobStages || []).length}
                        fallback={
                          <div className="mx-6 space-y-6">
                            {[1, 2, 3, 4, 5].map(item => (
                              <div className="space-y-3" key={item}>
                                <Skeleton className="h-3.5 w-1/2" />
                                <Skeleton className="h-3.5 w-1/3" />
                                <Skeleton className="h-3.5 w-full" />
                              </div>
                            ))}
                          </div>
                        }
                      >
                        {data?.jobsShow?.jobStages?.length && (
                          <ChangeJobStageWithModalView jobTitle={data?.jobsShow?.title || ''}>
                            {({ setOpenMarkAsHired, setApplicantCurrent, openHiringSuccessModel }) => {
                              const mergedOptions = {
                                setOpenMarkAsHired,
                                setApplicantCurrent,
                                openHiringSuccessModel
                              }

                              // eslint-disable-next-line react-hooks/rules-of-hooks
                              const getUUidV4 = useMemo(() => uuidV4(), [data?.jobsShow, refetchMyList, refetchMyDelete])
                              return (
                                <CandidatesTabContent
                                  getUUidV4={getUUidV4}
                                  jobStatus={data?.jobsShow.status}
                                  jobStages={jobStagesWithCondition}
                                  stageTypes={stageTypes}
                                  isDragDisabled={data?.jobsShow.status === JOB_STATUS_ENUM.archived}
                                  jobId={id}
                                  actions={{
                                    configSwitchLayout,
                                    setConfigSwitchLayout,
                                    switchView,
                                    setSwitchView
                                  }}
                                  callback={applicantsCallback => setApplicants(applicantsCallback)}
                                  jobTitle={data?.jobsShow?.title}
                                  companyName={data?.jobsShow?.permittedFields?.company?.value?.name}
                                  {...mergedOptions}
                                />
                              )
                            }}
                          </ChangeJobStageWithModalView>
                        )}
                      </If>
                    </TabsContent>

                    <TabsContent value="details" className="overflow-y-auto">
                      <JobDetailTab
                        stageTypes={stageTypes}
                        jobDetail={data}
                        setOpenEditPipeline={setOpenEditPipeline}
                        toggleQuickUpdateModal={toggleQuickUpdateModal}
                        setOpenJobInterviewKitsModal={setOpenJobInterviewKitsModal}
                        setOpenApplicationFormSetting={setOpenApplicationFormSetting}
                        callback={onRefetchJobDetail}
                        updateReferralReward={updateReferralReward}
                      />
                    </TabsContent>
                    {data?.jobsShow && !checkNoteForbidden(data.jobsShow.status as JOB_STATUS_ENUM, canAccessJobNote) ? (
                      <TabsContent value="notes" className="mt-3.5 overflow-y-auto">
                        {tabControl.value === JOB_NOTE_TAB && (
                          <NoteTabContent jobId={parseInt(id as string)} jobStatus={data.jobsShow.status as JOB_STATUS_ENUM} />
                        )}
                      </TabsContent>
                    ) : null}

                    <TabsContent value="jobBoards" className="overflow-y-auto">
                      <JobBoardsTab
                        open={showInputPostJobModal}
                        setOpen={setShowInputPostJobModal}
                        changeStatusCallback={changeStatusCallback}
                        onPublishJobPosting={onPublishJobPosting}
                        onUnPublishJobPosting={onUnPublishJobPosting}
                        jobDetail={data}
                        isPublishing={isPublishing}
                      />
                    </TabsContent>
                    <If condition={!userIsAsClient()}>
                      <TabsContent value="activities" className="overflow-y-auto">
                        <JobActivity jobId={id} />
                      </TabsContent>
                    </If>
                    <TabsContent className="mt-0 h-full pt-3 pl-6" value="jobRecommendation">
                      <JobRecommendationTab jobId={id} />
                    </TabsContent>
                    <ModalQuickUpdateHiringAndRecruiters
                      jobRecruiters={data?.jobsShow?.jobRecruiters}
                      triggerRecommendedMembersJob={triggerRecommendedMembersJob}
                      onReloadListMemberPending={onReloadFetchListMemberOfJob}
                      membersRecommendedJob={listRecommendedMembersJob || []}
                      membersPendingOfJob={data?.jobsShow?.membersPendingOfJob || []}
                      onToggleInviteMember={onToggleInviteMember}
                      deleteHiringMemberOfJobCallback={deleteHiringMemberOfJobCallback}
                      addHiringMemberOfJobCallback={addHiringMemberOfJobCallback}
                      changeMemberRoleOfJobCallback={changeMemberRoleOfJobCallback}
                      changeMemberRoleJobOfPendingCallback={changeMemberRoleJobOfPendingCallback}
                      isLoadingAddHiringMember2Job={isLoadingAddHiringMember2Job}
                      isLoadingDeleteHiringMemberOfJob={isLoadingDeleteHiringMemberOfJob}
                      isLoadingChangeRoleOfJob={isLoadingChangeRoleOfJob}
                      open={showQuickUpdateHiringModal}
                      setOpen={toggleQuickUpdateModal}
                      jobDetail={data}
                    />

                    <Dialog
                      size="md"
                      className="w-[480px]!"
                      open={isOpenInviteMember}
                      label={`${t('settings:teamMembers:inviteMemberModal:title')}`}
                      description={`${t('settings:teamMembers:inviteMemberModal:description')}`}
                      onOpenChange={onToggleInviteMember}
                    >
                      <InviteMember4HiringModalForm
                        jobId={Number(id)}
                        onOpenChange={onToggleInviteMember}
                        callback={() => {
                          onReloadFetchListMemberOfJob()
                          setTimeout(() => {
                            onToggleInviteMember(false)
                          }, 300)
                        }}
                      />
                    </Dialog>

                    <Dialog
                      className="min-w-[480px]"
                      open={openEditPipeline}
                      isPreventAutoFocusDialog
                      label={`${t('label:edit_pipeline')}`}
                      description=""
                      onOpenChange={() => {
                        if (anyChangesForm) {
                          onShowAlertClose4HiringPipelineFormModal(() => setOpenEditPipeline(false))
                        } else {
                          setOpenEditPipeline(false)
                        }
                      }}
                    >
                      <HiringPipelineForm
                        setAnyChangesFrom={setAnyChangesFrom}
                        applicantLists={applicants?.collection || []}
                        onDeleteStageCustom={onDeleteStageCustom}
                        defaultValue={pipelineValueEdit}
                        onOpen={setOpenEditPipeline}
                        onSubmit={onSubmitEditPipeline}
                        isLoading={isLoadingUpdatePipeline}
                        companyId={String(companyId || '')}
                        isEdit4Job
                      />
                    </Dialog>
                    {openApplicationFormSetting && (
                      <ApplicationFormSettingModal
                        open={openApplicationFormSetting}
                        setOpenDialog={setOpenApplicationFormSetting}
                        jobId={parseInt(id as string)}
                      />
                    )}
                    <Dialog
                      className="min-w-[480px]"
                      open={openJobInterviewKitsModal}
                      isPreventAutoFocusDialog
                      label={`${t('label:add_interview_kit')}`}
                      description=""
                      onOpenChange={() => setOpenJobInterviewKitsModal(false)}
                    >
                      <ModalAddJobInterviewKits
                        setOpenJobInterviewKitsModal={setOpenJobInterviewKitsModal}
                        jobId={parseInt(id as string)}
                        stageTypes={stageTypes}
                        callback={onRefetchJobDetail}
                      />
                    </Dialog>
                  </Tabs>

                  {switchView.view === 'candidates' ? (
                    <Drawer
                      position="right"
                      size="full"
                      drawerClassName="max-w-[calc(100vw-60px)]"
                      contentRef={drawerContainerRef}
                      open
                      onEscapeKeyDown={() => {
                        setRefetchMyList(true)

                        pushStateBrowser({
                          state: {},
                          unused: '',
                          url: returnUrl
                        })
                        setSwitchView({
                          id: undefined,
                          view: '',
                          applicantId: undefined
                        })
                      }}
                      customCloseButton={
                        <div className="absolute top-4 -left-[40px] z-50 flex flex-col space-y-4">{renderedActions && renderedActions()}</div>
                      }
                    >
                      <CandidateProfileContainer
                        returnUrl={returnUrl}
                        isDrawer={true}
                        id={switchView.id}
                        applicantId={switchView.applicantId}
                        setSwitchView={setSwitchView}
                      />
                    </Drawer>
                  ) : null}
                </>
              )
            }}
          </SwitchLayoutView>
        </JobDetailManagementPermissionContext.Provider>
      </LayoutGrid>
    </>
  )
}

export default withPermissionFeatureProvider(
  {
    checkAccessPermission: canAccessFeature,
    keyModule: [PERMISSIONS_LIST.job_management.keyModule],
    keyModuleObject: [PERMISSIONS_LIST.job_management.objects.job.keyModuleObject],
    action: ACTIONS_PERMISSIONS.show
  },
  JobDetailManagementContainer
)
