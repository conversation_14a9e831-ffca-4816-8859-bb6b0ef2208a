import { createContext } from 'react'

import withPermissionFeatureProvider from 'src/hoc/with-permission-feature'
import { AGENCY_TENANT } from '~/core/constants/enum'
import type { IDefaultPermissionsList } from '~/core/utilities/feature-permission'
import { ACTIONS_PERMISSIONS, canAccessFeature, DEFAULT_PERMISSIONS_LIST, PERMISSIONS_LIST } from '~/core/utilities/feature-permission'

import usePermissionJob from '~/lib/features/permissions/hooks/use-permission-job'
import useDetectCompanyWithKind from '~/lib/hooks/use-detect-company-with-kind'
import useBoundStore from '~/lib/store'

import CompanyJobEditView from '~/components/Jobs/CompanyJobEditView'
import JobEditView from '~/components/Jobs/JobEditView'
import LayoutGrid from '~/components/Layout/LayoutGrid'

export const JobEditPermissionContext = createContext<IDefaultPermissionsList>(DEFAULT_PERMISSIONS_LIST)

const JobEditManagementContainer = () => {
  const { isCompanyKind } = useDetectCompanyWithKind({ kind: AGENCY_TENANT })
  const { actionJob } = usePermissionJob()
  const user = useBoundStore()
  return (
    <LayoutGrid>
      <JobEditPermissionContext.Provider value={actionJob}>
        {isCompanyKind === true ? <CompanyJobEditView /> : <JobEditView />}
      </JobEditPermissionContext.Provider>
    </LayoutGrid>
  )
}

export default withPermissionFeatureProvider(
  {
    checkAccessPermission: canAccessFeature,
    keyModule: [PERMISSIONS_LIST.job_management.keyModule],
    keyModuleObject: [PERMISSIONS_LIST.job_management.objects.job.keyModuleObject],
    action: ACTIONS_PERMISSIONS.update
  },
  JobEditManagementContainer
)
