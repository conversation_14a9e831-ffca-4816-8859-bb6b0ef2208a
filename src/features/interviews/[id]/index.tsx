import withPermissionFeatureProvider from 'src/hoc/with-permission-feature'
import type { IRouterWithID } from '~/core/@types/global'
import { ACTIONS_PERMISSIONS, canAccessFeature, PERMISSIONS_LIST } from '~/core/utilities/feature-permission'

import InterviewF<PERSON>backView from '~/components/Interview/Feedback/InterviewFeedbackView'
import LayoutGrid from '~/components/Layout/LayoutGrid'

const InterviewFeedbackContainer = ({ id }: { id?: IRouterWithID }) => {
  return (
    <LayoutGrid>
      <InterviewFeedbackView isDrawer={false} id={id} />
    </LayoutGrid>
  )
}

export default withPermissionFeatureProvider(
  {
    checkAccessPermission: canAccessFeature,
    keyModule: [PERMISSIONS_LIST.job_management.keyModule],
    keyModuleObject: [PERMISSIONS_LIST.job_management.objects.job_ikit_evaluation.keyModuleObject],
    action: ACTIONS_PERMISSIONS.show
  },
  Interview<PERSON><PERSON>backContainer
)
