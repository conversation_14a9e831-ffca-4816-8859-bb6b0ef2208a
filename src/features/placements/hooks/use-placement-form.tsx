import { useTranslation } from 'react-i18next'

import useEnumsData from 'src/hooks/data/use-enums-data'
import configuration from '~/configuration'
import { AGENCY_TENANT } from '~/core/constants/enum'
import type { IResponseContextResult } from '~/core/middleware/use-context-graphQL'
import useContextGraphQL from '~/core/middleware/use-context-graphQL'
import type { IPromiseSearchOption } from '~/core/ui/Select'

import QueryTenantMembers from '~/lib/features/settings/members/graphql/query-tenant-members'
import QueryTenantMembersAgency from '~/lib/features/settings/members/graphql/query-tenant-members-agency'
import type { IMember } from '~/lib/features/settings/members/types'
import useDetectCompanyWithKind from '~/lib/hooks/use-detect-company-with-kind'

import usePlacementStatusWithDotColor from './use-placement-status-with-dot-color'

const usePlacementForm = () => {
  const { i18n } = useTranslation()
  const { clientGraphQL } = useContextGraphQL()
  const { isCompanyKind } = useDetectCompanyWithKind({ kind: AGENCY_TENANT })

  const fetchHiringMembers = (params = {} as IPromiseSearchOption) =>
    new Promise<any>(resolve => {
      return clientGraphQL
        .query(isCompanyKind ? QueryTenantMembersAgency : QueryTenantMembers, {
          ...params,
          ...(!isCompanyKind ? { excludeLimitedMember: true } : undefined)
        })
        .toPromise()
        .then((result: IResponseContextResult<IMember>) => {
          if (result.error) {
            resolve({
              metadata: {
                totalCount: configuration.defaultAsyncLoadingOptions
              },
              collection: []
            })
          }

          const { tenantMembers } = result.data
          const collection = tenantMembers?.collection || []
          const metadata = tenantMembers?.metadata || { totalCount: 0 }

          const cloneData = collection.map((item: IMember) => {
            return {
              value: item.id,
              avatar: item.avatarVariants?.thumb?.url,
              avatarVariants: item.avatarVariants,
              defaultColour: item.defaultColour,
              supportingObj: {
                name: item.fullName,
                description: item.email,
                defaultColour: item.defaultColour,
                helpName: item.roles?.[0]?.name
              }
            }
          })

          return resolve({ metadata, collection: cloneData })
        })
    })
  const placementTypeOfFees = useEnumsData({
    enumType: 'PlacementTypeOfFee',
    locale: i18n.language
  })
  const typesOfSalary = useEnumsData({
    enumType: 'PlacementTypeOfSalary',
    locale: i18n.language
  })
  const typeOfStatus = usePlacementStatusWithDotColor()
  return {
    fetchHiringMembers,
    placementTypeOfFees,
    typesOfSalary,
    typeOfStatus
  }
}
export default usePlacementForm
