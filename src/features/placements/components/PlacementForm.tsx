'use client'

/* eslint-disable react-hooks/rules-of-hooks */
import type { FC, PropsWithChildren } from 'react'
import { useEffect, useMemo, useState } from 'react'
import React from 'react'
import { Controller, useWatch } from 'react-hook-form'
import { useTranslation } from 'react-i18next'

import useEnumsData from 'src/hooks/data/use-enums-data'
import type { IUserInformation } from '~/core/@types/global'
import { AsyncSingleSearchWithSelect } from '~/core/ui/AsyncSingleSearchWithSelect'
import { DynamicImportForm } from '~/core/ui/DynamicImportForm'
import { FormControlItem } from '~/core/ui/FormControlItem'
import { IconButton } from '~/core/ui/IconButton'
import IconWrapper from '~/core/ui/IconWrapper'
import If from '~/core/ui/If'
import { Input } from '~/core/ui/Input'
import { InputRightElement } from '~/core/ui/InputElement'
import { InputGroup } from '~/core/ui/InputGroup'
import { NativeSelect } from '~/core/ui/NativeSelect'
import { RichEditorWithExtrasTools } from '~/core/ui/RichEditorWithExtrasTools'
import type { ISelectOption } from '~/core/ui/Select'
import { SingleDatePicker } from '~/core/ui/SingleDatePicker'
import { TextButton } from '~/core/ui/TextButton'
import { Tooltip } from '~/core/ui/Tooltip'
import { adminCanAction } from '~/core/utilities/permission'

import { changeTimezone } from '~/lib/features/calendar/utilities/helper-schedule-interview'
import useQueryHiringMembersList from '~/lib/features/candidates/hooks/use-query-hiring-members-list'
import type { IPlacementCustomField } from '~/lib/features/placements/types/management-page-type'
import { mappingCustomFieldKind, renderKeyCustomFieldForm } from '~/lib/features/settings/profile-fields/mapping/custom-field-mapping'
import useBoundStore from '~/lib/store'

import CustomField from '~/components/CustomField'

import usePlacementForm from '../hooks/use-placement-form'
import type { ICreateEditPlacement } from '../schema'
import { createEditPlacementSchema } from '../schema'
import ComputeInputRightPadding from './ComputeInputRightPadding'

const makeLimitChange =
  (onChange: (value: string | number) => void, floatDigitLength: number = 2) =>
  (value: string | number) => {
    if (value === '') {
      onChange(value)
    }
    if (value && !!value.toString().match(new RegExp(`^[0-9]\\d*([,.](\\d{1,${floatDigitLength}})?)?$`))) {
      onChange(value)
    } else onChange('')
  }
const onlyPositiveNumberChange = (onChange: (value: string | number) => void) => (value: string | number) => {
  if (value === '') {
    onChange(value)
  }

  if (value && !!value.toString().match(new RegExp(`^[0-9]\\d*([,.](\\d*)?)?$`))) {
    onChange(value)
  } else onChange('')
}

const PlacementForm: FC<
  PropsWithChildren<{
    defaultValue: ICreateEditPlacement
    placementCustomFields: IPlacementCustomField[]
    placementSystemFields: IPlacementCustomField[]
    applicant?: {
      id?: number
      hiredDate?: Date
      createdAt?: Date | string
      createdBy?: IUserInformation
    }
    onSubmit?: (data: ICreateEditPlacement) => Promise<void>
    hiddenFields?: Array<keyof ICreateEditPlacement>
    profileId?: number
  }>
> = ({ defaultValue, children, onSubmit, hiddenFields = [], applicant, profileId, placementCustomFields, placementSystemFields }) => {
  const { t, i18n } = useTranslation()
  const { currentRole, user } = useBoundStore()
  const [isExpandModal, setIsExpandModal] = useState<boolean>(false)
  const [triggerUpdateProfitsMembers, setTriggerUpdateProfitsMembers] = useState<boolean>(false)

  const { fetchHiringMembers, placementTypeOfFees, typesOfSalary, typeOfStatus } = usePlacementForm()

  const isShowNoteField = useMemo(() => {
    if (placementSystemFields.length) {
      return placementSystemFields.filter(f => f.field === 'comments').length
    }
    return false
  }, [placementSystemFields])

  const placementCurrencyOfRevenue = useEnumsData({
    enumType: 'PlacementCurrencyOfRevenue',
    locale: i18n.language
  })
  const { promiseHiringMemberOptions } = useQueryHiringMembersList({
    profileId,
    applicantId: applicant?.id
  })

  return (
    <DynamicImportForm
      key="placement-form"
      defaultValue={defaultValue}
      onSubmit={onSubmit}
      mode="onSubmit"
      schema={createEditPlacementSchema({
        t,
        placementCustomFields
      })}
    >
      {({ formState, control, reset }) => {
        const onboardDateWatcher = useWatch({
          control,
          name: 'onboardDate'
        })

        const [isOnlyOneResetForm, setResetForm] = useState(false)
        useEffect(() => {
          if (isOnlyOneResetForm === false && Object.keys(defaultValue.customFields || {}).length > 0) {
            reset(defaultValue)
            setResetForm(true)
          }
        }, [defaultValue])

        return (
          <>
            {!hiddenFields?.includes('status') ? (
              <div className="mb-4">
                <FormControlItem
                  destructive={!!formState.errors.status}
                  destructiveText={formState.errors.status?.message}
                  mode="vertical"
                  labelRequired
                  label={`${t('placements:management:table:placement_status')}`}
                >
                  <Controller
                    control={control}
                    name="status"
                    render={({ field: { onChange, value }, formState: { errors } }) => {
                      return (
                        <NativeSelect
                          type="dot-leading"
                          size="sm"
                          isClearable={false}
                          isSearchable={false}
                          onChange={newValue => {
                            onChange(newValue)
                          }}
                          value={value}
                          configSelectOption={{ dot: true }}
                          options={typeOfStatus}
                          menuPosition="fixed"
                          classNameOverride={{
                            loadingMessage: `${t('label:loading')}`,
                            noOptionsMessage: `${t('label:noOptions')}`
                          }}
                        />
                      )
                    }}
                  />
                </FormControlItem>
              </div>
            ) : null}

            <div className="mb-4 flex space-x-4">
              <div className="flex-1">
                <FormControlItem
                  destructive={!!formState.errors.hiredBy}
                  destructiveText={formState.errors.hiredBy?.message}
                  mode="vertical"
                  labelRequired
                  label={`${t('placements:management:table:hiredBy')}`}
                >
                  <Controller
                    control={control}
                    name="hiredBy"
                    render={({ field: { onChange, value }, formState: { errors } }) => {
                      return (
                        <AsyncSingleSearchWithSelect
                          size="sm"
                          isClearable={false}
                          isSearchable={false}
                          onChange={newValue => {
                            onChange(newValue)
                          }}
                          destructive={!!formState.errors.hiredBy}
                          value={value}
                          configSelectOption={{
                            supportingText: ['name', 'description'],
                            avatar: true,
                            defaultAvatar: true,
                            isHideAvatarSelectedOption: true
                          }}
                          promiseOptions={promiseHiringMemberOptions}
                          classNameOverride={{
                            loadingMessage: `${t('label:loading')}`,
                            noOptionsMessage: `${t('label:noOptions')}`
                          }}
                          placeholder={`${t('label:placeholder:select')}`}
                        />
                      )
                    }}
                  />
                </FormControlItem>
              </div>

              <div className="flex-1">
                <FormControlItem
                  destructive={!!formState.errors.hiredDate}
                  destructiveText={formState.errors.hiredDate?.message}
                  mode="vertical"
                  labelRequired
                  label={`${t('placements:management:filter:hire_date')}`}
                >
                  <Controller
                    control={control}
                    name="hiredDate"
                    render={({ field: { onChange, value }, formState: { errors } }) => {
                      return (
                        <SingleDatePicker
                          locale={i18n.language}
                          className="w-full"
                          destructive={!!formState.errors.hiredDate}
                          config={{
                            id: 5,
                            defaultOpen: false,
                            onChange,
                            value: value,
                            showClearIndicator: false,
                            disabled: {
                              before:
                                applicant?.createdAt &&
                                changeTimezone({
                                  date: applicant.createdAt,
                                  timezone: user?.timezone
                                }),
                              after: new Date()
                            } as any
                          }}
                          placeholder={`${t('label:placeholder:selectDate')}`}
                          size="sm"
                        />
                      )
                    }}
                  />
                </FormControlItem>
              </div>
            </div>

            <div className="mt-4 flex space-x-4">
              <div className="flex-1">
                <FormControlItem
                  helpIcon={
                    <Tooltip classNameConfig={{ content: 'max-w-[350px]' }} content={t('tooltip:onboardDate')}>
                      <IconWrapper size={14} className="ml-[6px] text-gray-400" name="HelpCircle" />
                    </Tooltip>
                  }
                  label={`${t('placements:management:filter:start_date')}`}
                >
                  <Controller
                    control={control}
                    name="onboardDate"
                    render={({ field: { onChange, value } }) => {
                      return (
                        <SingleDatePicker
                          locale={i18n.language}
                          size="sm"
                          destructive={!!formState.errors.onboardDate}
                          config={{
                            id: 1,
                            defaultOpen: false,
                            onChange,
                            value,
                            showClearIndicator: true,
                            onClear: () => {
                              onChange(undefined)
                            },
                            disabled: !!applicant?.createdAt
                              ? {
                                  before: changeTimezone({
                                    date: applicant.createdAt,
                                    timezone: user?.timezone
                                  })
                                }
                              : undefined
                          }}
                          placeholder={`${t('label:placeholder:selectDate')}`}
                        />
                      )
                    }}
                  />
                </FormControlItem>
              </div>
              <div className="flex-1">
                <FormControlItem
                  helpIcon={
                    <Tooltip classNameConfig={{ content: 'max-w-[350px]' }} content={t('tooltip:endOfProbation')}>
                      <IconWrapper size={14} className="ml-[6px] text-gray-400" name="HelpCircle" />
                    </Tooltip>
                  }
                  label={`${t('placements:management:table:end_date')}`}
                >
                  <Controller
                    control={control}
                    name="endOfProbationDate"
                    render={({ field: { onChange, value } }) => (
                      <SingleDatePicker
                        locale={i18n.language}
                        size="sm"
                        destructive={!!formState.errors.endOfProbationDate}
                        config={{
                          id: 2,
                          defaultOpen: false,
                          showClearIndicator: true,
                          disabled: !!onboardDateWatcher
                            ? {
                                before: onboardDateWatcher
                              }
                            : !!applicant?.createdAt
                              ? {
                                  before: changeTimezone({
                                    date: new Date(),
                                    timezone: user?.timezone
                                  })
                                }
                              : undefined,
                          onClear: () => {
                            onChange(undefined)
                          },
                          onChange,
                          value: value
                        }}
                        placeholder={`${t('label:placeholder:selectDate')}`}
                      />
                    )}
                  />
                </FormControlItem>
              </div>
            </div>
            <div className="mt-4 flex space-x-4">
              <div className="flex-1">
                <FormControlItem label={`${t('placements:management:table:salary_USD')}`}>
                  <Controller
                    control={control}
                    name="salary"
                    render={({ field: { onChange, value } }) => (
                      <InputGroup className="flex" configPadding={{ left: '', right: '' }}>
                        <ComputeInputRightPadding>
                          {({ inputStyle, rightInputRef, createOnPaddingChange }) => (
                            <>
                              <Input
                                size="sm"
                                autoFocus={false}
                                style={inputStyle}
                                value={value}
                                onChange={onlyPositiveNumberChange(onChange)}
                                inputType="number"
                                placeholder="0"
                                className="hide-number-arrows"
                                step="any"
                                pattern="^\d+$"
                                destructive={!!formState.errors.salary}
                              />
                              <InputRightElement>
                                <Controller
                                  control={control}
                                  name="typeOfSalary"
                                  render={({ field: { onChange, value } }) => (
                                    <div ref={rightInputRef}>
                                      <NativeSelect
                                        size="sm"
                                        isClearable={false}
                                        isSearchable={false}
                                        onChange={createOnPaddingChange(newValue => {
                                          onChange((newValue as ISelectOption).value)
                                        })}
                                        value={typesOfSalary.find((item: ISelectOption) => item.value === value)}
                                        options={typesOfSalary}
                                        classNameOverride={{
                                          bordered: 'none',
                                          container: 'z-30',
                                          loadingMessage: `${t('label:loading')}`,
                                          noOptionsMessage: `${t('label:noOptions')}`
                                        }}
                                        menuPosition="fixed"
                                      />
                                    </div>
                                  )}
                                />
                              </InputRightElement>
                            </>
                          )}
                        </ComputeInputRightPadding>
                      </InputGroup>
                    )}
                  />
                </FormControlItem>
              </div>
              <div className="flex-1">
                <FormControlItem
                  destructive={!!formState.errors.fee}
                  destructiveText={formState.errors.fee?.message}
                  label={`${t('placements:management:table:fee')}`}
                >
                  <Controller
                    control={control}
                    name="typeOfFee"
                    render={({ field: { onChange: onChangeTypeOfFee, value: valueTypeOfFee } }) => (
                      <Controller
                        control={control}
                        name="fee"
                        render={({ field: { onChange: onChangeFee, value: fee } }) => (
                          <InputGroup className="" configPadding={{ left: '', right: '' }}>
                            <ComputeInputRightPadding>
                              {({ inputStyle, rightInputRef, createOnPaddingChange }) => (
                                <>
                                  <Input
                                    inputType="number"
                                    size="sm"
                                    autoFocus={false}
                                    value={fee}
                                    pattern="^\d+$"
                                    style={inputStyle}
                                    className="hide-number-arrows"
                                    onChange={makeLimitChange(
                                      onChangeFee,
                                      { percentage: 2, flat: 2, months: 1 }[valueTypeOfFee?.toString() || 'percentage'] || 2
                                    )}
                                    placeholder="0"
                                    step="any"
                                    destructive={!!formState.errors.fee}
                                    // onChangeEnter={() => handleSave({ autoSave: false })}
                                  />

                                  <InputRightElement>
                                    <div ref={rightInputRef}>
                                      <NativeSelect
                                        size="sm"
                                        isClearable={false}
                                        isSearchable={false}
                                        onChange={createOnPaddingChange(newValue => {
                                          onChangeTypeOfFee((newValue as ISelectOption).value)
                                          onChangeFee('')
                                          // trigger('fee')
                                        })}
                                        value={placementTypeOfFees.find((item: ISelectOption) => item.value === valueTypeOfFee)}
                                        options={placementTypeOfFees}
                                        classNameOverride={{
                                          container: 'z-30',
                                          bordered: 'none',
                                          loadingMessage: `${t('label:loading')}`,
                                          noOptionsMessage: `${t('label:noOptions')}`
                                        }}
                                        menuPosition="fixed"
                                      />
                                    </div>
                                  </InputRightElement>
                                </>
                              )}
                            </ComputeInputRightPadding>
                          </InputGroup>
                        )}
                      />
                    )}
                  />
                </FormControlItem>
              </div>
            </div>
            <div className="mt-4 flex">
              <div className="flex-1">
                <FormControlItem label={`${t('placements:management:table:revenue')}`}>
                  <Controller
                    control={control}
                    name="revenue"
                    render={({ field: { onChange, value } }) => (
                      <InputGroup className="relative" configPadding={{ left: '', right: '' }}>
                        <ComputeInputRightPadding>
                          {({ inputStyle, rightInputRef, createOnPaddingChange }) => (
                            <>
                              <Input
                                size="sm"
                                autoFocus={false}
                                inputType="number"
                                style={inputStyle}
                                pattern="^\d+$"
                                value={value}
                                onChange={onlyPositiveNumberChange(onChange)}
                                step="any"
                                className="hide-number-arrows pr-[96px]"
                                destructive={!!formState.errors.revenue}
                                // onChangeEnter={() => handleSave({ autoSave: false })}
                              />
                              <InputRightElement className="z-20">
                                <Controller
                                  control={control}
                                  name="currencyOfRevenue"
                                  render={({ field: { onChange, value } }) => (
                                    <div ref={rightInputRef}>
                                      <NativeSelect
                                        size="sm"
                                        isClearable={false}
                                        isSearchable={false}
                                        onChange={createOnPaddingChange(newValue => {
                                          onChange((newValue as ISelectOption).value)
                                        })}
                                        value={placementCurrencyOfRevenue.find((item: ISelectOption) => item.value === value)}
                                        options={placementCurrencyOfRevenue}
                                        classNameOverride={{
                                          bordered: 'none',
                                          loadingMessage: `${t('label:loading')}`,
                                          noOptionsMessage: `${t('label:noOptions')}`
                                        }}
                                        menuPosition="fixed"
                                      />
                                    </div>
                                  )}
                                />
                              </InputRightElement>
                            </>
                          )}
                        </ComputeInputRightPadding>
                      </InputGroup>
                    )}
                  />
                </FormControlItem>
              </div>
            </div>
            <div className="mt-4 flex">
              <div className="flex-1">
                <Controller
                  control={control}
                  name="profitSplits"
                  render={({ field: { onChange, value }, formState: { errors } }) => {
                    const addNew = () => {
                      onChange([...value, {}])
                    }
                    const removeByIndex = (rindex: number) => {
                      if (value[rindex]?.id !== undefined) {
                        onChange(value.map((item, mindex) => (mindex === rindex ? { ...item, _destroy: true } : item)))
                      } else {
                        onChange(value.filter((item, mindex) => mindex !== rindex))
                      }
                    }

                    return (
                      <div>
                        <div className="flex justify-between">
                          <div className="text-sm font-medium text-gray-700 dark:text-gray-300">{t('placements:management:table:profitsSplit')}</div>
                          <div>
                            <TextButton
                              onClick={e => {
                                e.preventDefault()
                                addNew()
                              }}
                              size="md"
                              icon="leading"
                              iconMenus="Plus"
                              label={`${t('button:addMember')}`}
                              underline={false}
                            />
                          </div>
                        </div>
                        <div>
                          {value.map((item, index) => {
                            const getValue = (key: string) =>
                              //@ts-ignore
                              value[index][key] || ''
                            const createOnChangeField =
                              //@ts-ignore
                              (key: string) => newValue => {
                                onChange(value.map((item, i) => (i === index ? { ...item, [key]: newValue } : item)))
                                setTriggerUpdateProfitsMembers(isUpdated => !isUpdated)
                              }
                            const getFieldError = (key: string) =>
                              //@ts-ignore
                              errors?.profitSplits?.[index]?.[key]
                            const allSelectedIds = value.filter(item => !item._destroy).map(item => item.user_id?.value)
                            return !item._destroy ? (
                              <React.Fragment key={index}>
                                <div className="mt-4 flex items-start space-x-4 first:mt-2">
                                  <div className="flex-1">
                                    <AsyncSingleSearchWithSelect
                                      promiseOptions={params =>
                                        fetchHiringMembers(params).then(rs => ({
                                          ...rs,
                                          collection: rs.collection.filter((item: { value: string }) => !allSelectedIds.includes(item.value))
                                        }))
                                      }
                                      forceUpdatePromiseOptions={triggerUpdateProfitsMembers}
                                      isSearchable
                                      isClearable={false}
                                      size="sm"
                                      onChange={createOnChangeField('user_id')}
                                      placeholder={`${t('label:placeholder:select')}`}
                                      configSelectOption={{
                                        supportingText: ['name', 'description'],
                                        avatar: true
                                      }}
                                      value={getValue('user_id')}
                                      destructive={getFieldError('user_id')}
                                      menuPlacement="top"
                                      classNameOverride={{
                                        loadingMessage: `${t('label:loading')}`,
                                        noOptionsMessage: `${t('label:noOptions')}`
                                      }}
                                    />
                                  </div>
                                  <div className="flex-1">
                                    <InputGroup className="" configPadding={{ left: '', right: '' }}>
                                      <Input
                                        size="sm"
                                        autoFocus={false}
                                        className="pr-[37px]"
                                        value={getValue('profit_percentage')}
                                        onChange={createOnChangeField('profit_percentage')}
                                        destructive={!!getFieldError('profit_percentage') || !!errors.profitSplits?.message}
                                        // onChangeEnter={() => handleSave({ autoSave: false })}
                                      />
                                      <InputRightElement className="flex w-[35px] items-center justify-center border-l border-gray-300">
                                        <IconWrapper size={12} name="Percent" />
                                      </InputRightElement>
                                    </InputGroup>
                                  </div>
                                  <div className="pt-1">
                                    <IconButton
                                      iconMenus="Trash2"
                                      onClick={() => removeByIndex(index)}
                                      size="xs"
                                      type="secondary-destructive"
                                      // iconMenus="Trash2"
                                    />
                                  </div>
                                </div>
                                <div className="mt-1 flex items-center space-x-4">
                                  <div className="w-[288px]">
                                    <FormControlItem destructive={getFieldError('user_id')} destructiveText={getFieldError('user_id')?.message} />
                                  </div>
                                  <div className="flex-1">
                                    <FormControlItem
                                      destructive={getFieldError('profit_percentage')}
                                      destructiveText={getFieldError('profit_percentage')?.message}
                                    />
                                  </div>
                                </div>
                              </React.Fragment>
                            ) : null
                          })}
                        </div>
                        {!!errors.profitSplits?.message && (
                          <div className="mt-1 flex items-center space-x-4">
                            <div className="w-[288px]" />
                            <div className="flex-1">
                              <FormControlItem destructive={!!errors.profitSplits?.message} destructiveText={errors.profitSplits?.message} />
                            </div>
                          </div>
                        )}
                      </div>
                    )
                  }}
                />
              </div>
            </div>
            <If condition={isShowNoteField}>
              <div className="mt-4">
                <FormControlItem
                  destructive={!!formState.errors.notes}
                  destructiveText={formState.errors.notes?.message}
                  mode="vertical"
                  label={`${t('placements:management:table:notes')}`}
                >
                  <Controller
                    control={control}
                    name="notes"
                    render={({ field: { onChange, value }, formState: { errors } }) => {
                      return (
                        <RichEditorWithExtrasTools
                          size="sm"
                          className="h-full min-h-[166px] w-full max-w-full"
                          onChange={onChange}
                          isDisabled={
                            !!defaultValue?.id && !(!!adminCanAction(currentRole?.code) || String(applicant?.createdBy?.id) === String(user?.id))
                          }
                          content={value}
                          limit={10000}
                          placeholder={`${t('placements:management:table:notesPlaceholder')}`}
                          extraToolbar={{
                            expand: {
                              show: true,
                              isActive: isExpandModal,
                              onClick: () => {
                                setIsExpandModal(!isExpandModal)
                              },
                              expandDialog: {
                                title: `${t('placements:management:table:notes')}`
                              }
                            }
                          }}
                        />
                      )
                    }}
                  />
                </FormControlItem>
              </div>
            </If>
            <Controller
              control={control}
              name="customFields"
              render={({ field: { onChange: onChangeCustomFields, value: customFields } }) => {
                return (
                  <div className="mt-6 grid grid-cols-1 gap-6">
                    {placementCustomFields.map((field: IPlacementCustomField) => {
                      const key = renderKeyCustomFieldForm({
                        fieldKind: field.field_kind,
                        id: field.custom_setting_id
                      })

                      return (
                        <div key={`additional-field-${field.index}`}>
                          <CustomField
                            type={mappingCustomFieldKind(field.field_kind)}
                            display="default"
                            viewDefaultPlaceholder={`${t('candidates:tabs:candidateOverview:notAvailable')}`}
                            name={key}
                            classNameConfig={{
                              paragraphWrapper: 'w-full max-w-full'
                            }}
                            error={formState.errors?.['customFields']}
                            label={field.field_name}
                            labelRequired={!!field.required}
                            value={customFields?.[key]?.value}
                            onChange={value => {
                              onChangeCustomFields({
                                ...customFields,
                                [key]: {
                                  ...customFields?.[key],
                                  value
                                }
                              })
                            }}
                            extraProps={{
                              options: field.select_options.map(option => ({
                                value: option.key,
                                supportingObj: {
                                  name: option.description
                                }
                              }))
                            }}
                          />
                        </div>
                      )
                    })}
                  </div>
                )
              }}
            />

            {children}
          </>
        )
      }}
    </DynamicImportForm>
  )
}
export default PlacementForm
