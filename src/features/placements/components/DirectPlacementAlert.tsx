import { useMemo } from 'react'
import { Trans, useTranslation } from 'react-i18next'

import { CheckVerifyFillIcon } from '~/core/ui/FillIcons'
import { IconButton } from '~/core/ui/IconButton'
import If from '~/core/ui/If'
import { Tooltip } from '~/core/ui/Tooltip'
import { defaultFormatDate } from '~/core/utilities/format-date'
import { adminAndMemberCanAction } from '~/core/utilities/permission'

import usePermissionPlacement from '~/lib/features/permissions/hooks/use-permission-placement'
import useBoundStore from '~/lib/store'

import type { IPlacement } from '../placement'
import { useDirectEditPlacementModel } from './DirectEditPlacementDialog'
import { useOpenPlacementDetailDialog } from './PlacementDetailDialog'

const DirectPlacementAlert = (props: { placement: IPlacement; className?: string; onPlacementEdited: () => Promise<void> }) => {
  const { placement } = props
  const { t } = useTranslation()
  const { PlacementDetailModalComponent, openPlacementDetailModel } = useOpenPlacementDetailDialog()
  const { EditDirectPlacementModalComponent, openDirectEditPlacementModel } = useDirectEditPlacementModel()

  const { fullPermission: fullPermissionPlacement } = usePermissionPlacement()

  const isShowEditButton = useMemo(() => {
    return fullPermissionPlacement || placement?.ownedPlacement || placement?.editablePlacement
  }, [fullPermissionPlacement, placement?.ownedPlacement, placement?.editablePlacement])

  const { currentRole } = useBoundStore()

  return (
    <div className={`group bg-ava-bg-400 relative flex min-h-[32px] justify-between space-x-4 rounded-sm px-3 py-1.5 ${props.className}`}>
      <PlacementDetailModalComponent />
      <EditDirectPlacementModalComponent />
      <div className="flex w-[100px] flex-1 items-center text-sm text-green-800">
        <div className="mr-2">
          <CheckVerifyFillIcon size={16} className="mr-2 shrink-0 fill-green-800" />
        </div>
        <div>
          <Trans
            i18nKey={'candidates:tabs:candidateOverview:profileInformation:hiredForJob'}
            values={{
              jobTitle: props.placement?.permittedFields?.job.value?.title,
              hiredBy: props.placement?.permittedFields?.hiredBy?.value?.fullName
            }}
          >
            <span className="font-medium" />
          </Trans>
          <span className="relative top-[-2px] mx-2 inline-block h-0.5 w-0.5 rounded-full bg-green-800" />
          {props.placement?.permittedFields?.hiredDate.value && defaultFormatDate(new Date(props.placement?.permittedFields?.hiredDate.value))}
        </div>
      </div>
      <If condition={adminAndMemberCanAction(currentRole?.code)}>
        <div className="absolute top-[2px] right-[2px] flex rounded-sm bg-white p-1 opacity-0 group-hover:opacity-100">
          <div>
            <Tooltip content={t('tooltip:viewDetails')} classNameAsChild="-my-0.5">
              <IconButton
                onClick={() => {
                  openPlacementDetailModel({
                    placement: props.placement,
                    header: {
                      candidateName: props.placement?.permittedFields?.applicant?.value?.profile?.fullName,
                      jobTitle: props.placement?.permittedFields?.job?.value?.title,
                      companyName: props.placement?.permittedFields?.company?.value?.name
                    },
                    onPlacementEdited: () => {
                      return props.onPlacementEdited()
                    }
                  })
                }}
                iconMenus="ArrowUpRight"
                type="secondary"
                size="xs"
              />
            </Tooltip>
          </div>
          <If condition={isShowEditButton}>
            <div className="ml-0.5">
              <Tooltip content={t('tooltip:edit')} classNameAsChild="-my-0.5">
                <IconButton
                  onClick={() => {
                    const { permittedFields } = props.placement
                    const applicantValue = permittedFields?.applicant.value
                    const jobValue = permittedFields?.job.value
                    const companyValue = permittedFields?.company.value
                    openDirectEditPlacementModel({
                      header: {
                        candidateName: applicantValue?.profile?.fullName,
                        jobTitle: jobValue?.title,
                        companyName: companyValue?.name
                      },
                      placement: props.placement,
                      onPlacementEdited: () => {
                        return props.onPlacementEdited()
                      }
                    })
                  }}
                  size="xs"
                  iconMenus="Edit3"
                  type="secondary"
                />
              </Tooltip>
            </div>
          </If>
        </div>
      </If>
    </div>
  )
}
export default DirectPlacementAlert
