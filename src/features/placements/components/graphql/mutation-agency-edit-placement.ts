import { gql } from 'urql'

import type { CustomFieldFormType } from '~/lib/features/settings/profile-fields/types/custom-field'

import type { IPlacement } from '../../placement'

export const editAgencyPlacementMutation = gql<
  {
    placementsUpdate: {
      placement: IPlacement
    }
  },
  Partial<{
    id: number
    applicantId: number
    jobStageId: number
    status?: string
    hiredDate: string
    onboardDate?: string | null
    endOfProbationDate?: string | null
    salary?: number | null
    typeOfSalary?: string | null
    currencyOfSalary: string
    fee?: number | null
    typeOfFee?: string | null
    revenue?: number | null
    currencyOfRevenue: string | null
    profitSplits?: { userId: number; profitPercentage: number }[]
    hiredById?: number
    noteContent?: string
    noteId?: number
    customFields?: CustomFieldFormType[]
  }>
>`
  mutation (
    $id: Int!
    $applicantId: Int!
    $jobStageId: Int!
    $status: PlacementStatus
    $hiredDate: ISO8601DateTime!
    $onboardDate: ISO8601DateTime
    $endOfProbationDate: ISO8601DateTime
    $salary: Float
    $typeOfSalary: PlacementTypeOfSalary
    $currencyOfSalary: PlacementCurrencyOfRevenue
    $fee: Float
    $typeOfFee: PlacementTypeOfFee
    $revenue: Float
    $currencyOfRevenue: PlacementCurrencyOfRevenue
    $profitSplits: [JSON!]
    $hiredById: Int
    $noteContent: String
    $noteId: Int
    $customFields: [JSON!]
  ) {
    placementsUpdate(
      input: {
        id: $id
        applicantId: $applicantId
        jobStageId: $jobStageId
        status: $status
        hiredDate: $hiredDate
        onboardDate: $onboardDate
        endOfProbationDate: $endOfProbationDate
        salary: $salary
        typeOfSalary: $typeOfSalary
        currencyOfSalary: $currencyOfSalary
        fee: $fee
        typeOfFee: $typeOfFee
        revenue: $revenue
        currencyOfRevenue: $currencyOfRevenue
        profitSplits: $profitSplits
        hiredById: $hiredById
        noteContent: $noteContent
        noteId: $noteId
        customFields: $customFields
      }
    ) {
      placement {
        id
        permittedFields
        customFields
      }
    }
  }
`
