import { gql } from 'urql'

import type { CustomFieldFormType } from '~/lib/features/settings/profile-fields/types/custom-field'

import type { IPlacement } from '../../placement'

export const editPlacementMutation = gql<
  {
    placementsUpdate: {
      placement: IPlacement
    }
  },
  Partial<{
    id: number
    placementId: number
    jobStageId: number
    status?: string
    hiredDate: string
    onboardDate?: string | null
    endOfProbationDate?: string | null
    salary?: number | null
    typeOfSalary?: string | null
    currencyOfSalary: string
    fee?: number | null
    typeOfFee?: string | null
    revenue?: number | null
    currencyOfRevenue: string | null
    profitSplits?: { userId: number; profitPercentage: number }[]
    hiredById?: number
    noteContent?: string
    noteId?: number
    customFields?: CustomFieldFormType[]
  }>
>`
  mutation (
    $id: Int!
    $placementId: Int!
    $jobStageId: Int!
    $status: EmployerPlacementStatus
    $hiredDate: ISO8601DateTime!
    $onboardDate: ISO8601DateTime
    $endOfProbationDate: ISO8601DateTime
    $salary: Float
    $typeOfSalary: EmployerPlacementTypeOfSalary
    $currencyOfSalary: EmployerPlacementCurrencyOfRevenue
    $fee: Float
    $typeOfFee: EmployerPlacementTypeOfFee
    $revenue: Float
    $currencyOfRevenue: EmployerPlacementCurrencyOfRevenue
    $profitSplits: [JSON!]
    $hiredById: Int
    $noteContent: String
    $noteId: Int
    $customFields: [JSON!]
  ) {
    placementsUpdate(
      input: {
        placementId: $placementId
        id: $id
        jobStageId: $jobStageId
        status: $status
        hiredDate: $hiredDate
        onboardDate: $onboardDate
        endOfProbationDate: $endOfProbationDate
        salary: $salary
        typeOfSalary: $typeOfSalary
        currencyOfSalary: $currencyOfSalary
        fee: $fee
        typeOfFee: $typeOfFee
        revenue: $revenue
        currencyOfRevenue: $currencyOfRevenue
        profitSplits: $profitSplits
        hiredById: $hiredById
        noteContent: $noteContent
        noteId: $noteId
        customFields: $customFields
      }
    ) {
      placement {
        id
        permittedFields
        customFields
      }
    }
  }
`
