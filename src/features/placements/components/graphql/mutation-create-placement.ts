import { gql } from 'urql'

import type { IPlacementCustomFieldParam } from '~/lib/features/placements/types/management-page-type'

import type { IPlacement } from '../../placement'

export const createPlacementMutation = gql<
  {
    placementsCreate: {
      placement: IPlacement
    }
  },
  {
    applicantId: number
    jobStageId: number
    hiredDate: string
    onboardDate?: string
    endOfProbationDate?: string
    salary?: number
    typeOfSalary?: string | null
    currencyOfSalary: string
    fee?: number
    typeOfFee?: string | null
    revenue?: number
    currencyOfRevenue?: string | null
    profitSplits?: { userId: number; profitPercentage: number }[]
    hiredById?: number
    noteContent?: string
    customFields?: IPlacementCustomFieldParam[]
  }
>`
  mutation (
    $applicantId: Int!
    $jobStageId: Int!
    $hiredDate: ISO8601DateTime!
    $onboardDate: ISO8601DateTime
    $endOfProbationDate: ISO8601DateTime
    $salary: Float
    $typeOfSalary: PlacementTypeOfSalary
    $currencyOfSalary: PlacementCurrencyOfRevenue
    $fee: Float
    $typeOfFee: PlacementTypeOfFee
    $revenue: Float
    $currencyOfRevenue: PlacementCurrencyOfRevenue
    $profitSplits: [JSON!]
    $hiredById: Int
    $noteContent: String
    $customFields: [JSON!]
  ) {
    placementsCreate(
      input: {
        applicantId: $applicantId
        jobStageId: $jobStageId
        hiredDate: $hiredDate
        onboardDate: $onboardDate
        endOfProbationDate: $endOfProbationDate
        salary: $salary
        typeOfSalary: $typeOfSalary
        currencyOfSalary: $currencyOfSalary
        fee: $fee
        typeOfFee: $typeOfFee
        revenue: $revenue
        currencyOfRevenue: $currencyOfRevenue
        profitSplits: $profitSplits
        hiredById: $hiredById
        noteContent: $noteContent
        customFields: $customFields
      }
    ) {
      placement {
        id
        customFields
        permittedFields
      }
    }
  }
`
