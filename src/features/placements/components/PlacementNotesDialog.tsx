import { formatDistanceToNowStrict } from 'date-fns'
import DOMPurify from 'dompurify'
import { useTranslation } from 'react-i18next'

import type { IUserInformation } from '~/core/@types/global'
import { Avatar } from '~/core/ui/Avatar'
import { But<PERSON> } from '~/core/ui/Button'
import { Dialog } from '~/core/ui/Dialog'
import IconWrapper from '~/core/ui/IconWrapper'
import { Tooltip } from '~/core/ui/Tooltip'

import createModalHook from '~/lib/hooks/create-modal-hooks'

import ToolTipOnOverflow from './ToolTipOnOverflow'

export type CommentType = {
  id: string
  content: string
  user: IUserInformation
  createdAt: string
}

type OpenParams = {
  comments?: Array<CommentType>
  header: { candidateName?: string; jobTitle?: string; companyName?: string }
  //   onPlacementEdited: (placement: IPlacement) => Promise<any>
}
const PlacementNotesDialog = ({
  open,
  setOpen,
  openParams
}: {
  open: boolean
  setOpen: (open: boolean, param?: OpenParams) => void
  openParams?: OpenParams
}) => {
  const { t } = useTranslation()
  return (
    <Dialog
      open={open}
      onOpenChange={setOpen}
      isDivider={true}
      isPreventAutoFocusDialog={true}
      size="lg"
      label={`${t('placements:management:table:notes')}`}
      description={
        <div>
          <div className="flex items-center">
            <div className="mr-2">
              <IconWrapper size={16} name="User" />
            </div>
            <Tooltip content={openParams?.header?.candidateName}>
              <div className="line-clamp-1 text-sm text-gray-900">{openParams?.header?.candidateName}</div>
            </Tooltip>
          </div>
          <div className="mt-1.5 flex items-center">
            <div className="mr-2 shrink-0">
              <IconWrapper size={16} name="Briefcase" />
            </div>
            <ToolTipOnOverflow text={openParams?.header?.jobTitle} className="max-w-[200px] shrink-0 truncate text-sm text-gray-900" />
            <div className="mr-2 ml-2 h-0.5 w-0.5 shrink-0 rounded-full bg-gray-400"></div>
            <ToolTipOnOverflow className="shrink-1 truncate" text={openParams?.header?.companyName} />
          </div>
        </div>
      }
    >
      {openParams?.comments?.map((comment, index) => (
        <div key={`comment-${index}`} className="mt-4 flex items-start gap-3 first:mt-0">
          <div className="flex gap-2 py-1">
            <Avatar size="md" color={comment.user?.defaultColour} src={comment.user?.avatarVariants?.thumb?.url} alt={comment.user?.fullName} />
          </div>
          <div className="flex-1">
            <div className="flex h-5 flex-col justify-between gap-0.5">
              <div className="inline-flex items-center gap-4">
                <div className="shrink grow basis-0 text-sm font-medium text-gray-900">{comment.user?.fullName}</div>
                <div className="text-xs text-gray-700">{comment.createdAt ? formatDistanceToNowStrict(new Date(comment.createdAt)) : ''}</div>
              </div>
            </div>
            <div className="flex flex-col gap-0.5">
              <div
                className="text-sm text-gray-900"
                dangerouslySetInnerHTML={{
                  __html: DOMPurify.sanitize(comment.content)
                }}
              />
            </div>
          </div>
        </div>
      ))}
      <div className="flex justify-end pt-6">
        <Button label={`${t('button:close')}`} size="sm" onClick={() => setOpen(false)} />
      </div>
    </Dialog>
  )
}

export const useOpenPlacementNotesDialog = () => {
  const { ModalComponent: PlacementNotesModalComponent, openModal: openPlacementNotesModel } = createModalHook(PlacementNotesDialog)(data => {
    return Promise.resolve()
  })

  return {
    PlacementNotesModalComponent,
    openPlacementNotesModel
  }
}
