import { useRouter } from 'next/navigation'
import { useCallback } from 'react'

import configuration from '~/configuration'
import type { IFormAction } from '~/core/@types/global'
import { useRecaptcha, verifyRecaptcha } from '~/core/hooks/use-verify-captcha'
import useMiddlewareRequest from '~/core/middleware/use-middleware-request'
import AppRouteLoading from '~/core/ui/AppRouteLoading'
import { catchErrorFromRequest } from '~/core/utilities/catch-api-error'

import type { IRegisterForm } from '~/lib/features/register/types'
import { useTrackingUTM } from '~/lib/hooks/use-tracking-utm'

import LayoutHybrid from '~/components/Layout/LayoutHybrid'
import LayoutLoginRegister from '~/components/Layout/LayoutLoginRegister'
import RegisterView from '~/components/Register'

const RegisterContainer: React.FC<{
  providers: object
}> = ({ providers }) => {
  useRecaptcha()
  const router = useRouter()
  const { utm_campaign, utm_source, utm_medium, utm_content, isHasParam } = useTrackingUTM()

  const { trigger, isMutating: isLoading } = useMiddlewareRequest({
    endpoint: configuration.api.login,
    method: 'POST'
  })

  const navigateToVerifyEmailPage = useCallback((data: IRegisterForm) => {
    router.push(`${configuration.path.verifyEmail}?email=${data.email}`)
  }, [])

  const registerCallback = useCallback(
    async (data: IRegisterForm, formAction: IFormAction) => {
      if (isLoading) {
        return
      }

      verifyRecaptcha(async (isNotGoogleBOT: boolean) => {
        if (isNotGoogleBOT) {
          try {
            await trigger({
              ...data,
              ...(isHasParam ? { utm_campaign, utm_source, utm_medium, utm_content } : {}),
              provider: 'web'
            })
            navigateToVerifyEmailPage(data)
          } catch (error) {
            catchErrorFromRequest({
              error,
              formAction,
              callbackHandleStatusError422: keys => {
                keys.forEach((session: { field: string; message: string }) => {
                  formAction.setError('email', {
                    type: 'custom',
                    message: [session.message]
                  })
                })
              }
            })
          }
        }
      })
    },
    [isLoading, trigger, isHasParam, utm_campaign, utm_source, utm_medium, utm_content, navigateToVerifyEmailPage]
  )

  return (
    <LayoutHybrid>
      <AppRouteLoading>
        <LayoutLoginRegister>
          <RegisterView isLoadingEmail={isLoading} providers={providers} onFinish={registerCallback} />
        </LayoutLoginRegister>
      </AppRouteLoading>
    </LayoutHybrid>
  )
}

export default RegisterContainer
