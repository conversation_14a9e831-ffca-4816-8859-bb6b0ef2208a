'use client'

import { useRouter } from 'next/navigation'
import { useCallback, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'

import withFeatureSettingProvider from 'src/hoc/with-feature-setting'
import withQueryClientProvider from 'src/hoc/with-query-client-provider'
import { REFERRAL_LIST_URL } from '~/core/constants/url'
import { Divider } from '~/core/ui/Divider'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger, TabsTriggerView } from '~/core/ui/Tabs'
import { accessReferralFeature } from '~/core/utilities/feature-permission'

import useOpenJobsManagement from '~/lib/features/referrals/hooks/use-open-jobs-management'
import useReferralStore from '~/lib/features/referrals/store'
import type { ReferralFormType } from '~/lib/features/referrals/types'
import { JOBS_REFERRAL_TAB, REFERRALS_TAB } from '~/lib/features/referrals/utilities/enum'
import { PLAN_FEATURE_KEYS } from '~/lib/features/settings/plans/utilities/enum'
import useReferralSetting from '~/lib/features/settings/referrals/hooks/useReferralSetting'
import useBrowserTab from '~/lib/hooks/use-browser-tab'
import { useRouterContext } from '~/lib/next/use-router-context'

import MyReferrals from '~/components/CareerHub/[id]/MyReferrals'
import OpenJobs from '~/components/CareerHub/[id]/OpenJobs'
import ReferralModal from '~/components/CareerHub/[id]/ReferralModal'
import LayoutGrid from '~/components/Layout/LayoutGrid'
import FilterReferral from '~/components/Referrals/FilterReferral'
import type { FeatureName } from '~/components/Subscription/subscription'
import { withSubscriptionPlanLockFearture } from '~/components/Subscription/SubscriptionPlan'

const ReferralsContainer = () => {
  const { t } = useTranslation()
  const [openReferralModal, setOpenReferralModal] = useState<boolean>(false)
  const [referralJob, setReferralJob] = useState<ReferralFormType>()
  const { openJobsPaging, filterControl, refetch } = useOpenJobsManagement({})
  const router = useRouter()
  const { searchParams } = useRouterContext()
  const tabs = searchParams?.get('tabs')
  const { dataReferral } = useReferralSetting()
  const referralPortal = dataReferral?.values?.referral_portal

  const refetchMyReferralsList = useReferralStore(state => state.refetchMyReferralsList)
  const reportTabControl = useBrowserTab({
    defaultValue: JOBS_REFERRAL_TAB,
    queryKeyName: 'tabs'
  })
  const openJobsCount = useReferralStore(state => state.openJobCount)
  const onCloseReferralModal = useCallback(() => {
    setReferralJob({})
    setOpenReferralModal(false)
  }, [setOpenReferralModal])
  const callbackOnFinishReferralModal = useCallback(async () => {
    if (tabs === JOBS_REFERRAL_TAB) {
      openJobsPaging?.refetch()
    } else {
      refetchMyReferralsList && refetchMyReferralsList()
    }
  }, [refetchMyReferralsList, openJobsPaging])

  useEffect(() => {
    if (referralPortal?.job_only && reportTabControl.value === REFERRALS_TAB) {
      router.push(REFERRAL_LIST_URL)
    }
  }, [referralPortal?.job_only, reportTabControl.value])
  return (
    <LayoutGrid background="bg-gray-50">
      <Tabs {...reportTabControl} className={'flex h-full flex-col bg-gray-50'}>
        <div className="flex flex-none items-center justify-between bg-white pl-7">
          <div className="pt-5">
            <TabsList size="sm">
              <TabsTrigger value={JOBS_REFERRAL_TAB} size="sm" classNameButton={referralPortal?.job_only ? 'border-none' : ''}>
                <TabsTriggerView
                  size="sm"
                  session={{
                    value: JOBS_REFERRAL_TAB,
                    label: `${t('referrals:job_tab')}`,
                    count: openJobsCount
                  }}
                />
              </TabsTrigger>
              {!referralPortal?.job_only && (
                <TabsTrigger value={REFERRALS_TAB} size="sm">
                  <TabsTriggerView
                    size="sm"
                    session={{
                      value: REFERRALS_TAB,
                      label: `${t('referrals:referral_tab')}`
                    }}
                  />
                </TabsTrigger>
              )}
            </TabsList>
          </div>
          <FilterReferral filterControl={filterControl} setOpenReferralModal={setOpenReferralModal} referral_only={referralPortal?.referral_only} />
        </div>
        <Divider className="flex-none" />
        <TabsContent value={JOBS_REFERRAL_TAB} className="mt-0">
          <OpenJobs filterControl={filterControl} openJobsPaging={openJobsPaging} refetch={refetch} />
        </TabsContent>
        {!referralPortal?.job_only && (
          <TabsContent value={REFERRALS_TAB} className="bg-gray-50 p-4 pt-6">
            <div className="mx-auto flex w-[776px] flex-col">
              <MyReferrals />
            </div>
          </TabsContent>
        )}
      </Tabs>
      <ReferralModal
        openReferralModal={openReferralModal}
        onClose={onCloseReferralModal}
        defaultValue={referralJob}
        callbackOnFinish={callbackOnFinishReferralModal}
      />
    </LayoutGrid>
  )
}

export default withSubscriptionPlanLockFearture(
  withFeatureSettingProvider(
    {
      checkAccessPermission: accessReferralFeature,
      checkUserPermission: () => true
    },
    withQueryClientProvider(ReferralsContainer)
  ),
  PLAN_FEATURE_KEYS.referral as FeatureName
)
