import { useState } from 'react'
import { useTranslation } from 'react-i18next'

import pathConfiguration from 'src/configuration/path'
import withPermissionSettingProvider from 'src/hoc/with-permission-setting'
import withQueryClientProvider from 'src/hoc/with-query-client-provider'
import configuration from '~/configuration'
import { TypographyH1 } from '~/core/ui/Heading'
import IconWrapper from '~/core/ui/IconWrapper'
import { Tooltip } from '~/core/ui/Tooltip'
import { canAccessSetting, PERMISSIONS_LIST } from '~/core/utilities/feature-permission'

import useReportManagement from '~/lib/features/reports/hooks/use-report-management'
import type { IReportCandidateSourceModal } from '~/lib/features/reports/types/candidate-source'
import { CANDIDATE_SOURCE_TAB } from '~/lib/features/reports/utilities/enum'
import useBoundStore from '~/lib/store'

import { withLayoutGrid } from '~/components/Layout/LayoutGrid'
import CandidateSourceReport from '~/components/Reports/candidate-source'
import CandidateSourceOverviewTable from '~/components/Reports/candidate-source/CandidateSourceOverviewTable'
import ReportDetailModal from '~/components/Reports/components/ReportDetailModal'
import ReportFilterExportWrapper from '~/components/Reports/components/ReportFilterExportWrapper'
import ReportLayout from '~/components/Reports/components/ReportLayout'

const ReportCandidateSourceContainer = () => {
  const user = useBoundStore(state => state.user)
  const { t } = useTranslation()
  const { filterControl } = useReportManagement({ user })

  const [reportDetailConfig, setReportDetailConfig] = useState<IReportCandidateSourceModal>({
    open: false
  })

  return (
    <ReportLayout>
      <div>
        <div className="sticky top-0 z-10 mb-6 flex justify-between border-b border-b-gray-100 bg-white py-[15px] pr-6">
          <div className="flex flex-row items-center">
            <TypographyH1 className="mr-2 text-lg font-medium text-gray-900">{t('report:candidateSource:title')}</TypographyH1>

            <Tooltip position="bottom" content={<>{t('report:tool_tip_overview')}</>}>
              <IconWrapper name="HelpCircle" size={14} className="text-gray-400" />
            </Tooltip>
          </div>
          <ReportFilterExportWrapper page={1} limit={configuration.defaultPageSize} tab={CANDIDATE_SOURCE_TAB} filterControl={filterControl} />
        </div>
        <CandidateSourceReport
          tab={CANDIDATE_SOURCE_TAB}
          filterControl={filterControl}
          reportDetailConfig={reportDetailConfig}
          setReportDetailConfig={setReportDetailConfig}
        />
        <CandidateSourceOverviewTable
          filterControl={filterControl}
          reportDetailConfig={reportDetailConfig}
          setReportDetailConfig={setReportDetailConfig}
        />
        {reportDetailConfig?.open ? (
          <ReportDetailModal
            open
            setOpen={value => setReportDetailConfig(value ? { ...reportDetailConfig, open: value } : { open: false })}
            title={reportDetailConfig?.title}
            type={reportDetailConfig?.type}
            mappingData={reportDetailConfig?.mappingData}
            queryReportDetail={reportDetailConfig?.query}
            filterControl={{
              value: {
                ...(filterControl?.value || {}),
                ...(reportDetailConfig?.variablesFilter || {})
              },
              onChange: filterControl?.onChange
            }}
          />
        ) : null}
      </div>
    </ReportLayout>
  )
}

export default withPermissionSettingProvider(
  {
    checkAccessPermission: canAccessSetting,
    keyModule: [PERMISSIONS_LIST.report.keyModule]
  },
  withLayoutGrid(withQueryClientProvider(ReportCandidateSourceContainer))
)
