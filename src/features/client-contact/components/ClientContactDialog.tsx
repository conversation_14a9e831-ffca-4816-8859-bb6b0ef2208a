import Link from 'next/link'
import { useMemo } from 'react'
import { Controller } from 'react-hook-form'
import { Trans, useTranslation } from 'react-i18next'

import { openAlert } from '~/core/ui/AlertDialog'
import { Avatar } from '~/core/ui/Avatar'
import { Badge } from '~/core/ui/Badge'
import { Button } from '~/core/ui/Button'
import { Dialog } from '~/core/ui/Dialog'
import { DynamicImportForm } from '~/core/ui/DynamicImportForm'
import { FormControlItem } from '~/core/ui/FormControlItem'
import { IconButton } from '~/core/ui/IconButton'
import IconWrapper from '~/core/ui/IconWrapper'
import If from '~/core/ui/If'
import { Input } from '~/core/ui/Input'
import { Tooltip } from '~/core/ui/Tooltip'
import { cn } from '~/core/ui/utils'

import type { IJobDetail } from '~/lib/features/jobs/graphql/query-job-detail-mini'

import { createAddContactSchema } from '../schema'
import useClientContact from '../use-client-contact'

enum ContactRowType {
  CLIENT_MEMBER = 'CLIENT_MEMBER',
  CLIENT_INVITATION = 'CLIENT_INVITATION'
}
function toCamelCase(str: string) {
  return str
    .split(' ')
    .map(function (word, index) {
      // If it is the first word make sure to lowercase all the chars.
      if (index == 0) {
        return word.toLowerCase()
      }
      // If it is not the first word only upper case the first char and lowercase the rest.
      return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
    })
    .join('')
}
const emailToName = (email: string) => {
  return email
    ?.split('@')?.[0]
    ?.split('.')
    .map(item => toCamelCase(item))
    .join(' ')
}
const ClientContactDialog = (props: {
  setOpen: (open: boolean) => void
  open: boolean
  openParams?: {
    onContactUpdated?: (contactId?: number, clientInvitationEmail?: string) => Promise<void>
    jobId: number
    companyId: number
  }
  onSubmit: (data: {}) => Promise<void>
  clientInvitations?: IJobDetail['jobsShow']['clientInvitations']
  recommendedClientContacts?: IJobDetail['jobsShow']['recommendedClientContacts']
  clientMembers?: IJobDetail['jobsShow']['clientMembers']
  jobsShow?: IJobDetail['jobsShow']
}) => {
  const { openParams, open, setOpen, clientInvitations, recommendedClientContacts, clientMembers, jobsShow } = props
  const { canActionInviteClient, onInviteContact, onDeleteContact, resendEmail, onDeleteClient } = useClientContact({
    reload: openParams?.onContactUpdated
  })
  const { t } = useTranslation()
  const clientContacts = useMemo(
    () => [
      ...(clientMembers || []).map(item => ({
        ...item,
        type: ContactRowType.CLIENT_MEMBER
      })),
      ...(clientInvitations || []).map(item => ({
        ...item,
        type: ContactRowType.CLIENT_INVITATION
      }))
    ],
    [clientMembers, clientInvitations]
  )
  return (
    <Dialog
      open={open}
      onOpenChange={setOpen}
      isPreventAutoFocusDialog={true}
      size="sm"
      label={`${t('job:detail:client_contact:title')}`}
      description={
        <div className="mb-5">
          <div className="line-clamp-1 text-sm text-gray-900">
            {t('job:clientContact:manageContactAccess')}
            <Link href="#" className="text-primary-400">
              {t('button:learnMore')}
            </Link>
          </div>
        </div>
      }
      headingClassName="tablet:pb-0"
    >
      <div>
        <If condition={canActionInviteClient.create}>
          <DynamicImportForm
            onSubmit={(data, formAction) =>
              openParams
                ? onInviteContact({
                    email: data.email,
                    jobId: openParams.jobId
                  }).then(rs => {
                    formAction.reset()
                    openParams?.onContactUpdated?.()
                    return rs
                  })
                : Promise.reject('openParams not available')
            }
            //   mode="onSubmit"
            defaultValue={useMemo(() => ({ email: '' }), [])}
            schema={useMemo(() => createAddContactSchema(t), [])}
          >
            {({ control, formState }) => (
              <div className="mb-5 items-center">
                <FormControlItem
                  destructive={!!formState.errors?.email}
                  destructiveText={formState.errors && (formState.errors?.email?.message as string)}
                >
                  <Controller
                    control={control}
                    name="email"
                    render={({ field: { onChange, value } }) => (
                      <div className="flex">
                        <div className="flex-1">
                          <Input destructive={!!formState.errors?.email} size="sm" placeholder="<EMAIL>" onChange={onChange} value={value} />
                        </div>
                        <Button
                          isDisabled={!formState.isDirty || !formState.isValid}
                          className="ml-3"
                          isLoading={formState.isSubmitting}
                          size="sm"
                          htmlType="submit"
                          type="primary"
                          label={`${t('common:invite')}`}
                        />
                      </div>
                    )}
                  />
                </FormControlItem>
              </div>
            )}
          </DynamicImportForm>
        </If>
        {clientMembers?.length == 0 && <NoContactComponent />}
        <div>
          {clientContacts?.map(item => {
            const invitation = item as IJobDetail['jobsShow']['clientInvitations'][0]
            const member = item as IJobDetail['jobsShow']['clientMembers'][0]
            const contactName = {
              [ContactRowType.CLIENT_INVITATION]: invitation?.fullName ? invitation?.fullName : emailToName(invitation.email),
              [ContactRowType.CLIENT_MEMBER]: member.fullName
            }[item.type]
            return (
              <div className="mb-3 flex w-full items-center justify-between last:mb-0" key={item.id}>
                <div className="flex-none">
                  {
                    {
                      [ContactRowType.CLIENT_INVITATION]: <Avatar alt={item.email} size="md" color={'#F8EDED'} />,
                      [ContactRowType.CLIENT_MEMBER]: (
                        <Avatar src={member?.avatarVariants?.thumb?.url} alt={member.email} size="md" color={member.defaultColour} />
                      )
                    }[item.type]
                  }
                </div>

                <div className="ml-2 w-[calc(100%-116px)] flex-1">
                  <div className="flex space-x-1.5">
                    <Tooltip
                      classNameAsChild="truncate break-all"
                      content={
                        {
                          [ContactRowType.CLIENT_INVITATION]: invitation?.fullName ? invitation?.fullName : `${invitation.email}`,
                          [ContactRowType.CLIENT_MEMBER]: member.fullName
                        }[item.type]
                      }
                    >
                      <div className="truncate text-sm font-medium break-all text-gray-900">{contactName}</div>
                    </Tooltip>
                    {item.type === ContactRowType.CLIENT_INVITATION && (
                      <Badge radius="circular" color="yellow">
                        {t('label:status:pending')}
                      </Badge>
                    )}
                  </div>

                  <p className="truncate text-xs font-normal break-all text-gray-700">{item.email}</p>
                </div>
                <If condition={canActionInviteClient.create}>
                  <div className="ml-2 flex flex-none space-x-1">
                    {
                      {
                        [ContactRowType.CLIENT_INVITATION]: (
                          <>
                            <If condition={canActionInviteClient.create}>
                              <IconButton
                                onClick={() => {
                                  resendEmail(invitation)
                                }}
                                type="secondary"
                                iconMenus="Forward"
                                size="sm"
                              />
                            </If>
                            <If condition={canActionInviteClient.delete}>
                              <IconButton
                                onClick={() => {
                                  openAlert({
                                    isPreventAutoFocusDialog: false,
                                    className: 'w-[480px]',
                                    title: `${t('job:dialog:delete_title')}`,
                                    description: (
                                      <Trans
                                        i18nKey="job:dialog:delete_description"
                                        values={{
                                          name: invitation.email
                                        }}
                                      >
                                        <span className="font-medium text-gray-900" />
                                      </Trans>
                                    ),
                                    actions: [
                                      {
                                        label: `${t('button:cancel')}`,
                                        type: 'secondary',
                                        size: 'sm'
                                      },
                                      {
                                        isCallAPI: true,
                                        label: `${t('button:remove')}`,
                                        type: 'destructive',
                                        size: 'sm',
                                        onClick: async () => {
                                          await onDeleteContact(parseInt(invitation.id), String(contactName), openParams?.jobId)
                                        }
                                      }
                                    ]
                                  })
                                }}
                                type="secondary-destructive"
                                size="sm"
                                iconMenus="Trash2"
                              />
                            </If>
                          </>
                        ),
                        [ContactRowType.CLIENT_MEMBER]: (
                          <If condition={canActionInviteClient.delete}>
                            <IconButton
                              onClick={() => {
                                openAlert({
                                  isPreventAutoFocusDialog: false,
                                  className: 'w-[480px]',
                                  title: `${t('job:dialog:delete_title')}`,
                                  description: (
                                    <Trans
                                      i18nKey="job:dialog:delete_description"
                                      values={{
                                        name: member.fullName
                                      }}
                                    >
                                      <span className="font-medium text-gray-900" />
                                    </Trans>
                                  ),
                                  actions: [
                                    {
                                      label: `${t('button:cancel')}`,
                                      type: 'secondary',
                                      size: 'sm'
                                    },
                                    {
                                      isCallAPI: true,
                                      label: `${t('button:remove')}`,
                                      type: 'destructive',
                                      size: 'sm',
                                      onClick: async () => {
                                        await onDeleteClient(openParams?.jobId as number, parseInt(member.id.toString()))
                                      }
                                    }
                                  ]
                                })
                              }}
                              type="secondary-destructive"
                              size="sm"
                              iconMenus="Trash2"
                            />
                          </If>
                        )
                      }[item.type]
                    }
                  </div>
                </If>
              </div>
            )
          })}
        </div>
        {(recommendedClientContacts?.length || 0) > 0 && (
          <>
            <div className="mt-5 mb-3 flex items-center">
              <div className="mr-1 text-sm font-medium text-gray-700">{t('job:clientContact:recommendedContacts')}</div>
              <Tooltip classNameAsChild="flex-none" content={t('tooltip:currentContactHaveNotBeenInvited')}>
                <IconWrapper name="HelpCircle" size={14} />
              </Tooltip>
            </div>

            {recommendedClientContacts?.map(item => {
              const recommendedFullName = [item?.permittedFields?.firstName?.value, item?.permittedFields?.lastName?.value]
                .filter(name => !!name)
                .join(' ')
              return (
                <div className="mt-3 flex items-center first:mt-0" key={item.id}>
                  <div className="flex-none">
                    <Avatar src={item.avatarVariants?.thumb?.url} alt={recommendedFullName} size="md" color="#E7E4F8" />
                  </div>
                  <div className="mx-[8px] w-[calc(100%-96px)] flex-1">
                    <div className="flex">
                      <span className="truncate text-sm font-medium break-all text-gray-900">
                        <Tooltip classNameAsChild="truncate break-all" content={recommendedFullName}>
                          {recommendedFullName}
                        </Tooltip>
                      </span>
                    </div>
                    <div className="flex">
                      <Tooltip classNameAsChild="truncate break-all" content={item?.permittedFields?.email?.value}>
                        <p className="truncate text-xs font-normal break-all text-gray-700">{item?.permittedFields?.email?.value}</p>
                      </Tooltip>
                    </div>
                  </div>
                  <If condition={canActionInviteClient.create}>
                    <div className="flex-none">
                      <Button
                        onClick={() => {
                          onInviteContact({
                            jobId: openParams?.jobId,
                            contactId: parseInt(item.id?.toString())
                          })
                        }}
                        size="xs"
                        type="tertiary"
                        label={`${t('common:invite')}`}
                      />
                    </div>
                  </If>
                </div>
              )
            })}
          </>
        )}
      </div>
    </Dialog>
  )
}
export const NoContactComponent = (props: { className?: string }) => {
  const { t } = useTranslation()
  return (
    <div className={cn('mb-5 flex items-center', props.className)}>
      <div className="flex h-[32px] w-[32px] items-center justify-center rounded-full bg-gray-100">
        <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M10.0875 3.66013H12.39C15.1575 3.66013 16.5075 5.13763 16.5 8.16763V11.8201C16.5 14.7151 14.715 16.5001 11.8125 16.5001H6.18C3.2925 16.5001 1.5 14.7151 1.5 11.8126V6.18013C1.5 3.07513 2.88 1.50013 5.6025 1.50013H6.7875C7.48575 1.49263 8.1375 1.81513 8.565 2.36263L9.225 3.24013C9.435 3.50263 9.75 3.66013 10.0875 3.66013ZM5.53125 11.4688H12.4762C12.7838 11.4688 13.0312 11.2137 13.0312 10.9062C13.0312 10.5913 12.7838 10.3438 12.4762 10.3438H5.53125C5.21625 10.3438 4.96875 10.5913 4.96875 10.9062C4.96875 11.2137 5.21625 11.4688 5.53125 11.4688Z"
            fill="#B4BDCB"
          />
        </svg>
      </div>
      <div className="mx-2 text-sm text-gray-700">{t('job:clientContact:noContactsAdded')}</div>
    </div>
  )
}

export default ClientContactDialog
