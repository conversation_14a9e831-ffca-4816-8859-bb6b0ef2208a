import { gql } from 'urql'

import type { CompanyContactType } from '~/lib/features/agency/companies/types/company-detail'

const queryCompanyContactList = gql<
  {
    companyContactsList: {
      collection: CompanyContactType[]
    }
  },
  { companyId: number; limit: number; page: number }
>`
  query ($companyId: Int!, $limit: Int!, $page: Int!) {
    companyContactsList(companyId: $companyId, limit: $limit, page: $page) {
      collection {
        id
        title
        firstName
        lastName
        phoneNumber
        email
      }
    }
  }
`

export default queryCompanyContactList
