import type { TFunction } from 'next-i18next'
import { z } from 'zod'

import { regexEmailValidation } from '~/core/utilities/common'

export const createAddContactSchema = (t: TFunction) =>
  z.object({
    email: z
      .string()
      .refine(value => value.trim() !== '', {
        message: `${t('form:requiredField')}`
      })
      .refine(value => regexEmailValidation.test(value), {
        message: `${t('form:invalid_email')}`
      })
  })
export type AddContactType = z.infer<ReturnType<typeof createAddContactSchema>>
