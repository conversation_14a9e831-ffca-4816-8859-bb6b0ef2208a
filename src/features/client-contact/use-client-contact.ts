import { useCallback, useMemo } from 'react'
import { useTranslation } from 'react-i18next'

import { AGENCY_TENANT } from '~/core/constants/enum'
import { PUBLIC_APP_NAME } from '~/core/constants/env'
import useContextGraphQL from '~/core/middleware/use-context-graphQL'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'
import { adminAndMemberCanAction } from '~/core/utilities/permission'

import QueryAgencyTenantJobDetailClientContact from '~/lib/features/jobs/graphql/agency-query-job-detail-client-contact'
import type { IJobDetail } from '~/lib/features/jobs/graphql/query-job-detail-mini'
import type { IJobDetailParams } from '~/lib/features/jobs/types'
import usePermissionInviteClient from '~/lib/features/permissions/hooks/use-permission-invite-client'
import { removeEmptyField } from '~/lib/features/placements/utilities'
import useHiringPortalSetting from '~/lib/features/settings/company-settings/hooks/useHiringPortalSetting'
import useDetectCompanyWithKind from '~/lib/hooks/use-detect-company-with-kind'
import { useSubmitCommon } from '~/lib/hooks/use-submit-graphql-common'
import { useRouterContext } from '~/lib/next/use-router-context'
import useBoundStore from '~/lib/store'
import useToastStore from '~/lib/store/toast'

import MutationInviteContact from './graphql/mutation-invite-contact'
import MutationCancelInviteMember from './graphql/mutation-invite-contact-cancel'
import MutationDeleteMember from './graphql/mutation-remove-job'
import type { AddContactType } from './schema'

const useClientContact = (props?: { reload?: () => Promise<void> }) => {
  const { reload } = props || {}
  const { setToast } = useToastStore()
  const { params } = useRouterContext()
  const id = params?.id

  const { t } = useTranslation()

  const { currentRole } = useBoundStore()
  const { isCompanyKind } = useDetectCompanyWithKind({ kind: AGENCY_TENANT })
  const { actionInviteClient } = usePermissionInviteClient()
  const { enableHiringPortal } = useHiringPortalSetting()

  const { trigger: inviteMutation } = useSubmitCommon(MutationInviteContact)
  const { trigger: cancelInviteMutation } = useSubmitCommon(MutationCancelInviteMember)
  const { trigger: deleteClient } = useSubmitCommon(MutationDeleteMember)
  const { clientGraphQL } = useContextGraphQL()

  const canActionInviteClient = useMemo(() => {
    if (isCompanyKind && adminAndMemberCanAction(currentRole?.code)) {
      return {
        create: true,
        update: true,
        delete: true,
        owned_update: true,
        owned_delete: true
      }
    }

    return {
      create: enableHiringPortal && actionInviteClient?.create,
      update: enableHiringPortal && actionInviteClient?.update,
      delete: enableHiringPortal && actionInviteClient?.delete,
      owned_update: enableHiringPortal && actionInviteClient?.owned_update,
      owned_delete: enableHiringPortal && actionInviteClient?.owned_delete
    }
  }, [isCompanyKind, currentRole?.code, actionInviteClient, enableHiringPortal])

  const fetchData = useCallback(
    async (paramsJobDetail: IJobDetailParams) => {
      return clientGraphQL
        .query(QueryAgencyTenantJobDetailClientContact, {
          id: Number(id),
          ...paramsJobDetail
        })
        .toPromise()
        .then((result: { error: { graphQLErrors: Array<object> }; data: { jobsShow: IJobDetail['jobsShow'] } }) => {
          if (result.error) {
            catchErrorFromGraphQL({
              error: result.error,
              setToast
            })
            return
          }
          const { jobsShow } = result.data
          if (jobsShow.clientInvitedJob) {
            setToast({
              open: true,
              type: 'success',
              title: `${t('notification:clientContact:successSentTitle')}`,
              description: `${t('notification:clientContact:successSentContentInvitation')}`
            })
          } else {
            setToast({
              open: true,
              type: 'success',
              title: `${t('notification:clientContact:successSentTitle')}`,
              description: `${t('notification:clientContact:successSentContentNewInvitation', { domain: PUBLIC_APP_NAME })}`
            })
          }
        })
    },
    [clientGraphQL, id, setToast, t]
  )

  const onInviteContact = useCallback(
    (data: Partial<AddContactType> & { jobId?: number; contactId?: number }) => {
      return inviteMutation(
        removeEmptyField({
          email: data.email,
          jobId: data.jobId,
          contactId: data.contactId
        })
      ).then(result => {
        if (result.error) {
          catchErrorFromGraphQL({
            error: result.error,
            setToast
          })
          return
        }

        return (
          reload &&
          reload().then(() => {
            fetchData({
              contactId: data.contactId,
              clientInvitationEmail: data.email
            })
          })
        )
      })
    },
    [reload]
  )

  const onDeleteContact = useCallback(
    (id: number, contactName: string, jobId?: number) => {
      return cancelInviteMutation({ id, jobId }).then(() => {
        setToast({
          open: true,
          type: 'success',
          title: `${t('notification:clientContact:contactHasBeenRemoved', {
            contactName
          })}`
        })
        return reload && reload()
      })
    },
    [reload]
  )

  const onDeleteClient = useCallback(
    (jobId: number, clientId: number) => {
      return deleteClient({
        id: jobId,
        clientDeletedMemberId: clientId
      }).then(result => {
        // if (result.error) {
        //   catchErrorFromGraphQL({
        //     error: result.error,
        //     setToast
        //   })
        //   return
        // }
        return reload && reload()
      })
    },
    [reload]
  )

  const resendEmail = useCallback(
    (invite: { id: string; email: string }) => {
      return inviteMutation(
        removeEmptyField({
          invitationId: parseInt(invite.id.toString()),
          email: invite.email,
          resend: true
        })
      ).then(result => {
        if (result.error) {
          catchErrorFromGraphQL({
            error: result.error,
            setToast
          })
          return
        }
        setToast({
          open: true,
          type: 'success',
          title: `${t('notification:clientContact:invitationForEmailHasBeenResent', {
            email: invite.email
          })}`
        })
        return reload && reload()
      })
    },
    [reload]
  )

  return {
    canActionInviteClient,
    onInviteContact,
    onDeleteContact,
    onDeleteClient,
    resendEmail
  }
}
export default useClientContact
