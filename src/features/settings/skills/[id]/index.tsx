import { createContext } from 'react'

import withPermissionSettingProvider from 'src/hoc/with-permission-setting'
import type { IRouterWithID } from '~/core/@types/global'
import type { IDefaultPermissionsList } from '~/core/utilities/feature-permission'
import { canAccessSetting, DEFAULT_PERMISSIONS_LIST, PERMISSIONS_LIST } from '~/core/utilities/feature-permission'

import usePermissionSetting from '~/lib/features/permissions/hooks/use-permission-setting'

import LayoutGridSettings from '~/components/Layout/LayoutGridSettings'
import SkillReportDetail from '~/components/Settings/Skills/SkillReportDetail'

export const SkillsManagementPermissionContext = createContext<IDefaultPermissionsList>(DEFAULT_PERMISSIONS_LIST)

const SkillDetailsContainer = ({ id }: { id: IRouterWithID }) => {
  const { canAccessModule } = usePermissionSetting()
  return (
    <LayoutGridSettings>
      <SkillsManagementPermissionContext.Provider
        value={{
          create: !!canAccessModule,
          update: !!canAccessModule,
          delete: !!canAccessModule
        }}
      >
        <SkillReportDetail id={id} />
      </SkillsManagementPermissionContext.Provider>
    </LayoutGridSettings>
  )
}

export default withPermissionSettingProvider(
  {
    checkAccessPermission: canAccessSetting,
    keyModule: [PERMISSIONS_LIST.tenant_setting.keyModule]
  },
  SkillDetailsContainer
)
