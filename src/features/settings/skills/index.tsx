import { createContext } from 'react'

import withPermissionSettingProvider from 'src/hoc/with-permission-setting'
import type { IDefaultPermissionsList } from '~/core/utilities/feature-permission'
import { canAccessSetting, DEFAULT_PERMISSIONS_LIST, PERMISSIONS_LIST } from '~/core/utilities/feature-permission'

import usePermissionSetting from '~/lib/features/permissions/hooks/use-permission-setting'
import useBoundStore from '~/lib/store'

import LayoutGridSettings from '~/components/Layout/LayoutGridSettings'
import SkillsManagementView from '~/components/Settings/Skills'

export const SkillsManagementPermissionContext = createContext<IDefaultPermissionsList>(DEFAULT_PERMISSIONS_LIST)

const SkillsManagementContainer = () => {
  const { canAccessModule } = usePermissionSetting()
  const { user } = useBoundStore()

  return (
    <LayoutGridSettings>
      <SkillsManagementPermissionContext.Provider
        value={{
          create: !!canAccessModule,
          update: !!canAccessModule,
          delete: !!canAccessModule
        }}
      >
        <SkillsManagementView user={user} />
      </SkillsManagementPermissionContext.Provider>
    </LayoutGridSettings>
  )
}

export default withPermissionSettingProvider(
  {
    checkAccessPermission: canAccessSetting,
    keyModule: [PERMISSIONS_LIST.tenant_setting.keyModule]
  },
  SkillsManagementContainer
)
