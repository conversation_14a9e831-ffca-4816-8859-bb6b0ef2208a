import { createContext } from 'react'

import withPermissionSettingProvider from 'src/hoc/with-permission-setting'
import type { IDefaultPermissionsList } from '~/core/utilities/feature-permission'
import { canAccessSetting, DEFAULT_PERMISSIONS_LIST, PERMISSIONS_LIST } from '~/core/utilities/feature-permission'

import usePermissionSetting from '~/lib/features/permissions/hooks/use-permission-setting'

import LayoutGridSettings from '~/components/Layout/LayoutGridSettings'
import LocationsManagementView from '~/components/Settings/Locations'

export const LocationsManagementPermissionContext = createContext<IDefaultPermissionsList>(DEFAULT_PERMISSIONS_LIST)

const LocationsManagementContainer = () => {
  const { actionLocation } = usePermissionSetting()

  return (
    <LayoutGridSettings>
      <LocationsManagementPermissionContext.Provider
        value={{
          create: !!actionLocation,
          update: !!actionLocation,
          delete: !!actionLocation
        }}
      >
        <LocationsManagementView />
      </LocationsManagementPermissionContext.Provider>
    </LayoutGridSettings>
  )
}

export default withPermissionSettingProvider(
  {
    checkAccessPermission: canAccessSetting,
    keyModule: [PERMISSIONS_LIST.tenant_setting.keyModule]
  },
  LocationsManagementContainer
)
