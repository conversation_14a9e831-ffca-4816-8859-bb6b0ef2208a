import { createContext } from 'react'

import withPermissionSettingProvider from 'src/hoc/with-permission-setting'
import type { IDefaultPermissionsList } from '~/core/utilities/feature-permission'
import { canAccessSetting, DEFAULT_PERMISSIONS_LIST, PERMISSIONS_LIST } from '~/core/utilities/feature-permission'

import usePermissionSetting from '~/lib/features/permissions/hooks/use-permission-setting'

import LayoutGridSettings from '~/components/Layout/LayoutGridSettings'
import ProfileTemplatesManagementView from '~/components/Settings/ProfileTemplates'

export const ProfileTemplatesManagementPermissionContext = createContext<IDefaultPermissionsList>(DEFAULT_PERMISSIONS_LIST)

function ProfileTemplatesManagement() {
  const { actionProfileTemplate } = usePermissionSetting()

  return (
    <LayoutGridSettings>
      <ProfileTemplatesManagementPermissionContext.Provider
        value={{
          create: !!actionProfileTemplate,
          update: !!actionProfileTemplate,
          delete: !!actionProfileTemplate
        }}
      >
        <ProfileTemplatesManagementView />
      </ProfileTemplatesManagementPermissionContext.Provider>
    </LayoutGridSettings>
  )
}

export default withPermissionSettingProvider(
  {
    checkAccessPermission: canAccessSetting,
    keyModule: [PERMISSIONS_LIST.template_setting.keyModule]
  },
  ProfileTemplatesManagement
)
