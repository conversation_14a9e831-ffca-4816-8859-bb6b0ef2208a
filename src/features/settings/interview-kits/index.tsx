import { createContext } from 'react'

import withPermissionSettingProvider from 'src/hoc/with-permission-setting'
import type { IDefaultPermissionsList } from '~/core/utilities/feature-permission'
import { canAccessSetting, DEFAULT_PERMISSIONS_LIST, PERMISSIONS_LIST } from '~/core/utilities/feature-permission'

import usePermissionSetting from '~/lib/features/permissions/hooks/use-permission-setting'

import LayoutGridSettings from '~/components/Layout/LayoutGridSettings'
import InterviewKitsManagementView from '~/components/Settings/InterviewKits'

export const InterviewKitsManagementPermissionContext = createContext<IDefaultPermissionsList>(DEFAULT_PERMISSIONS_LIST)

function InterviewKitsManagement() {
  const { actionIkitTemplate } = usePermissionSetting()

  return (
    <LayoutGridSettings>
      <InterviewKitsManagementPermissionContext.Provider
        value={{
          create: !!actionIkitTemplate,
          update: !!actionIkitTemplate,
          delete: !!actionIkitTemplate
        }}
      >
        <InterviewKitsManagementView />
      </InterviewKitsManagementPermissionContext.Provider>
    </LayoutGridSettings>
  )
}

export default withPermissionSettingProvider(
  {
    checkAccessPermission: canAccessSetting,
    keyModule: [PERMISSIONS_LIST.template_setting.keyModule]
  },
  InterviewKitsManagement
)
