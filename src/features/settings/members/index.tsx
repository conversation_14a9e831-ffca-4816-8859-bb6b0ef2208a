import { createContext } from 'react'

import withPermissionSettingProvider from 'src/hoc/with-permission-setting'
import type { IDefaultPermissionsList } from '~/core/utilities/feature-permission'
import { canAccessSetting, DEFAULT_PERMISSIONS_LIST, PERMISSIONS_LIST } from '~/core/utilities/feature-permission'

import usePermissionSetting from '~/lib/features/permissions/hooks/use-permission-setting'
import useBoundStore from '~/lib/store'

import LayoutGridSettings from '~/components/Layout/LayoutGridSettings'
import MembersManagementView from '~/components/Settings/Members'

export const MembersManagementPermissionContext = createContext<IDefaultPermissionsList>(DEFAULT_PERMISSIONS_LIST)

const MembersManagementContainer = () => {
  const { user } = useBoundStore()
  const { actionUser } = usePermissionSetting()

  return (
    <LayoutGridSettings>
      <MembersManagementPermissionContext.Provider
        value={{
          create: !!actionUser,
          update: !!actionUser,
          delete: !!actionUser
        }}
      >
        <MembersManagementView user={user} />
      </MembersManagementPermissionContext.Provider>
    </LayoutGridSettings>
  )
}

export default withPermissionSettingProvider(
  {
    checkAccessPermission: canAccessSetting,
    keyModule: [PERMISSIONS_LIST.user_management.keyModule]
  },
  MembersManagementContainer
)
