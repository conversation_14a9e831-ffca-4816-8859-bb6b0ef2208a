import { createContext } from 'react'

import withPermissionSettingProvider from 'src/hoc/with-permission-setting'
import type { IDefaultPermissionsList } from '~/core/utilities/feature-permission'
import { canAccessSetting, DEFAULT_PERMISSIONS_LIST, PERMISSIONS_LIST } from '~/core/utilities/feature-permission'

import usePermissionSetting from '~/lib/features/permissions/hooks/use-permission-setting'

import LayoutGridSettings from '~/components/Layout/LayoutGridSettings'
import TagsManagementView from '~/components/Settings/Tags'

export const TagsManagementPermissionContext = createContext<IDefaultPermissionsList>(DEFAULT_PERMISSIONS_LIST)

const TagsManagementContainer = () => {
  const { actionTag } = usePermissionSetting()

  return (
    <LayoutGridSettings>
      <TagsManagementPermissionContext.Provider
        value={{
          create: !!actionTag,
          update: !!actionTag,
          delete: !!actionTag
        }}
      >
        <TagsManagementView />
      </TagsManagementPermissionContext.Provider>
    </LayoutGridSettings>
  )
}

export default withPermissionSettingProvider(
  {
    checkAccessPermission: canAccessSetting,
    keyModule: [PERMISSIONS_LIST.tenant_setting.keyModule]
  },
  TagsManagementContainer
)
