import { useRouter } from 'next/navigation'
import { useMemo } from 'react'

import withPermissionSettingProvider from 'src/hoc/with-permission-setting'
import configuration from '~/configuration'
import If from '~/core/ui/If'
import { canAccessSetting, PERMISSIONS_LIST } from '~/core/utilities/feature-permission'

import useImportingFileStatusHook from '~/lib/features/settings/import/hooks/use-importing-file-status-hook'
import type { ISourceImportType } from '~/lib/features/settings/import/types'
import { ENUM_IMPORT_FILE_STATUS, ENUMS_IMPORT_TYPE } from '~/lib/features/settings/import/utilities/enum'
import { useRouterContext } from '~/lib/next/use-router-context'

import ImportFailedFile from '~/components/Settings/Import/components/ImportFailedFile'
import ImportCompletedFile from '~/components/Settings/Import/ImportCompletedFile'
import ImportCompletedProfileFile from '~/components/Settings/Import/ImportCompletedProfileFile'
import ImportCompletedTenantCourseFile from '~/components/Settings/Import/ImportCompletedTenantCourseFile'
import ImportingFile from '~/components/Settings/Import/ImportingFile'
import ImportLayout from '~/components/Settings/Import/ImportLayout'
import { withSubscriptionPlanLockFearture } from '~/components/Subscription/SubscriptionPlan'

const ImportManagementContainer = ({
  suspend,
  listSourceImportType,
  importType
}: {
  suspend?: boolean
  listSourceImportType: Array<ISourceImportType>
  importType: ISourceImportType
}) => {
  const router = useRouter()
  const { searchParams } = useRouterContext()
  const object_kind = searchParams?.get('object_kind')

  const { importFileStatus, currentStep, nextStep, previousStep, backToFirstStep } = useImportingFileStatusHook({ importType })

  const isCompletedFiles = importFileStatus === ENUM_IMPORT_FILE_STATUS.completed || importFileStatus === ENUM_IMPORT_FILE_STATUS.partial

  return (
    <ImportLayout
      listSourceImportType={listSourceImportType}
      importType={importType}
      suspend={suspend}
      importFileStatus={importFileStatus}
      actionUploadFile={{
        goToUploadFileStep: backToFirstStep,
        nextStep,
        previousStep
      }}
      currentStep={currentStep}
      importDetail={tab => (
        <>
          {importFileStatus === ENUM_IMPORT_FILE_STATUS.in_progress && <ImportingFile />}
          {importFileStatus === ENUM_IMPORT_FILE_STATUS.failed && (
            <ImportFailedFile
              importType={importType}
              onTryAgain={backToFirstStep}
              tab={tab}
              onBackToHistoryTabList={() => router.push(`${configuration.path.settings.import}?tab=history&object_kind=${object_kind}`)}
            />
          )}
          {isCompletedFiles && (
            <>
              <If condition={importType.value === ENUMS_IMPORT_TYPE.jobs}>
                <ImportCompletedFile
                  importType={importType}
                  onTryAgain={backToFirstStep}
                  tab={tab}
                  onBackToHistoryTabList={() => router.push(`${configuration.path.settings.import}?tab=history&object_kind=${object_kind}`)}
                />
              </If>
              <If condition={importType.value === ENUMS_IMPORT_TYPE.candidate}>
                <ImportCompletedProfileFile
                  importType={importType}
                  onTryAgain={backToFirstStep}
                  tab={tab}
                  onBackToHistoryTabList={() => router.push(`${configuration.path.settings.import}?tab=history&object_kind=${object_kind}`)}
                />
              </If>
              <If condition={importType.value === ENUMS_IMPORT_TYPE.course}>
                <ImportCompletedTenantCourseFile
                  importType={importType}
                  onTryAgain={backToFirstStep}
                  tab={tab}
                  onBackToHistoryTabList={() => router.push(`${configuration.path.settings.import}?tab=history&object_kind=${object_kind}`)}
                />
              </If>
            </>
          )}
        </>
      )}
    />
  )
}

const ImportManagementWrapper = (props: any) => {
  const { featureName } = props

  const WrappedComponent = useMemo(
    () =>
      withSubscriptionPlanLockFearture(ImportManagementContainer, featureName, {
        classNameHeightOfView: 'min-h-0 h-full'
      }),
    [featureName]
  )

  return <WrappedComponent {...props} />
}

export default withPermissionSettingProvider(
  {
    checkAccessPermission: canAccessSetting,
    keyModule: [PERMISSIONS_LIST.tenant_setting.keyModule]
  },
  ImportManagementWrapper
)
