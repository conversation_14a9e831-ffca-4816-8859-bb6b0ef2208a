import { useRouter } from 'next/navigation'
import { useState } from 'react'

import withPermissionSettingProvider from 'src/hoc/with-permission-setting'
import configuration from '~/configuration'
import { canAccessSetting, PERMISSIONS_LIST } from '~/core/utilities/feature-permission'

import useImportingFileStatusHook from '~/lib/features/settings/import/hooks/use-importing-file-status-hook'
import { ENUM_IMPORT_FILE_STATUS, IMPORT_COURSES } from '~/lib/features/settings/import/utilities/enum'
import { PLAN_FEATURE_KEYS } from '~/lib/features/settings/plans/utilities/enum'

import ImportFailedFile from '~/components/Settings/Import/components/ImportFailedFile'
import ImportCompletedTenantCourseFile from '~/components/Settings/Import/ImportCompletedTenantCourseFile'
import ImportingFile from '~/components/Settings/Import/ImportingFile'
import ImportLayout from '~/components/Settings/Import/ImportLayout'
import type { FeatureName } from '~/components/Subscription/subscription'
import { withSubscriptionPlanLockFearture } from '~/components/Subscription/SubscriptionPlan'

const ImportCoursesManagementContainer = ({ suspend }: { suspend?: boolean }) => {
  const router = useRouter()
  const [importType, setImportType] = useState(IMPORT_COURSES)

  const { importFileStatus, currentStep, nextStep, previousStep, backToFirstStep } = useImportingFileStatusHook({ importType })

  const isCompletedFiles = importFileStatus === ENUM_IMPORT_FILE_STATUS.completed || importFileStatus === ENUM_IMPORT_FILE_STATUS.partial

  return (
    <ImportLayout
      listSourceImportType={[IMPORT_COURSES]}
      importType={importType}
      suspend={suspend}
      importFileStatus={importFileStatus}
      actionUploadFile={{
        goToUploadFileStep: backToFirstStep,
        nextStep,
        previousStep
      }}
      currentStep={currentStep}
      importDetail={tab => (
        <>
          {importFileStatus === ENUM_IMPORT_FILE_STATUS.in_progress && <ImportingFile />}
          {importFileStatus === ENUM_IMPORT_FILE_STATUS.failed && (
            <ImportFailedFile
              importType={importType}
              onTryAgain={backToFirstStep}
              tab={tab}
              onBackToHistoryTabList={() => router.push(`${configuration.path.settings.import}?tab=history`)}
            />
          )}
          {isCompletedFiles && (
            <ImportCompletedTenantCourseFile
              importType={importType}
              onTryAgain={backToFirstStep}
              tab={tab}
              onBackToHistoryTabList={() => router.push(`${configuration.path.settings.import}?tab=history`)}
            />
          )}
        </>
      )}
    />
  )
}

export default withSubscriptionPlanLockFearture(
  withPermissionSettingProvider(
    {
      checkAccessPermission: canAccessSetting,
      keyModule: [PERMISSIONS_LIST.tenant_setting.keyModule]
    },
    ImportCoursesManagementContainer
  ),
  PLAN_FEATURE_KEYS.learning_management_system as FeatureName,
  {
    classNameHeightOfView: 'min-h-0 h-full'
  }
)
