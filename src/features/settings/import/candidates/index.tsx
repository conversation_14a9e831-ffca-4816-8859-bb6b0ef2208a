import { useRouter } from 'next/navigation'
import { useState } from 'react'

import withPermissionSettingProvider from 'src/hoc/with-permission-setting'
import configuration from '~/configuration'
import { canAccessSetting, PERMISSIONS_LIST } from '~/core/utilities/feature-permission'

import useImportingFileStatusHook from '~/lib/features/settings/import/hooks/use-importing-file-status-hook'
import type { ISourceImportType } from '~/lib/features/settings/import/types'
import { ENUM_IMPORT_FILE_STATUS, IMPORT_CANDIDATES } from '~/lib/features/settings/import/utilities/enum'
import { PLAN_FEATURE_KEYS } from '~/lib/features/settings/plans/utilities/enum'
import { useRouterContext } from '~/lib/next/use-router-context'

import ImportFailedFile from '~/components/Settings/Import/components/ImportFailedFile'
import ImportCompletedProfileFile from '~/components/Settings/Import/ImportCompletedProfileFile'
import ImportingFile from '~/components/Settings/Import/ImportingFile'
import ImportLayout from '~/components/Settings/Import/ImportLayout'
import type { FeatureName } from '~/components/Subscription/subscription'
import { withSubscriptionPlanLockFearture } from '~/components/Subscription/SubscriptionPlan'

const ImportCandidatesManagementContainer = ({ suspend }: { suspend?: boolean }) => {
  const { searchParams } = useRouterContext()
  const router = useRouter()
  const object_kind = searchParams?.get('object_kind')
  const [importType, setImportType] = useState<ISourceImportType>(IMPORT_CANDIDATES)
  const { importFileStatus, currentStep, nextStep, previousStep, backToFirstStep } = useImportingFileStatusHook({ importType })

  const isCompletedFiles = importFileStatus === ENUM_IMPORT_FILE_STATUS.completed || importFileStatus === ENUM_IMPORT_FILE_STATUS.partial

  return (
    <ImportLayout
      listSourceImportType={[IMPORT_CANDIDATES]}
      importType={importType}
      suspend={suspend}
      importFileStatus={importFileStatus}
      actionUploadFile={{
        goToUploadFileStep: backToFirstStep,
        nextStep,
        previousStep
      }}
      currentStep={currentStep}
      importDetail={tab => (
        <>
          {importFileStatus === ENUM_IMPORT_FILE_STATUS.in_progress && <ImportingFile />}
          {importFileStatus === ENUM_IMPORT_FILE_STATUS.failed && (
            <ImportFailedFile
              importType={importType}
              onTryAgain={backToFirstStep}
              tab={tab}
              onBackToHistoryTabList={() => router.push(`${configuration.path.settings.import}?tab=history&object_kind=${object_kind}`)}
            />
          )}
          {isCompletedFiles && (
            <ImportCompletedProfileFile
              importType={importType}
              onTryAgain={backToFirstStep}
              tab={tab}
              onBackToHistoryTabList={() => router.push(`${configuration.path.settings.import}?tab=history&object_kind=${object_kind}`)}
            />
          )}
        </>
      )}
    />
  )
}

export default withSubscriptionPlanLockFearture(
  withPermissionSettingProvider(
    {
      checkAccessPermission: canAccessSetting,
      keyModule: [PERMISSIONS_LIST.tenant_setting.keyModule]
    },
    ImportCandidatesManagementContainer
  ),
  PLAN_FEATURE_KEYS.import_candidate as FeatureName,
  {
    classNameHeightOfView: 'min-h-0 h-full'
  }
)
