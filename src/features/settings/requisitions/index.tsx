import { createContext } from 'react'

import withPermissionSettingProvider from 'src/hoc/with-permission-setting'
import type { IDefaultPermissionsList } from '~/core/utilities/feature-permission'
import { canAccessSetting, DEFAULT_PERMISSIONS_LIST, PERMISSIONS_LIST } from '~/core/utilities/feature-permission'

import usePermissionSetting from '~/lib/features/permissions/hooks/use-permission-setting'

import LayoutGridSettings from '~/components/Layout/LayoutGridSettings'
import RequisitionsSettingManagementView from '~/components/Settings/Requisitions'

export const RequisitionsSettingManagementPermissionContext = createContext<IDefaultPermissionsList>(DEFAULT_PERMISSIONS_LIST)

const RequisitionsManagementContainer = () => {
  const { actionRequisition } = usePermissionSetting()

  return (
    <LayoutGridSettings>
      <RequisitionsSettingManagementPermissionContext.Provider
        value={{
          create: !!actionRequisition,
          update: !!actionRequisition,
          delete: !!actionRequisition
        }}
      >
        <RequisitionsSettingManagementView />
      </RequisitionsSettingManagementPermissionContext.Provider>
    </LayoutGridSettings>
  )
}

export default withPermissionSettingProvider(
  {
    checkAccessPermission: canAccessSetting,
    keyModule: [PERMISSIONS_LIST.tenant_setting.keyModule]
  },
  RequisitionsManagementContainer
)
