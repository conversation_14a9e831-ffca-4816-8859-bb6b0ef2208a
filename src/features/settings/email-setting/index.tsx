import { useCallback } from 'react'
import { useTranslation } from 'react-i18next'

import pathConfiguration from 'src/configuration/path'
import useQueryGraphQL from '~/core/middleware/use-query-graphQL'
import { Container } from '~/core/ui/Container'
import { PageHeaderSimple } from '~/core/ui/PageHeaderSimple'
import { Skeleton } from '~/core/ui/Skeleton'
import { TextButton } from '~/core/ui/TextButton'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'

import MutationUpdateEmailSetting from '~/lib/features/settings/email-setting/graphql/mutation-update-email-setting'
import QueryEmailSignatureSetting from '~/lib/features/settings/email-setting/graphql/query-email-signature-settings'
import type { EmailSignatureFormType } from '~/lib/features/settings/email-setting/types'
import { useSubmitCommon } from '~/lib/hooks/use-submit-graphql-common'
import useBoundStore from '~/lib/store'
import useToastStore from '~/lib/store/toast'

import LayoutGridSettings from '~/components/Layout/LayoutGridSettings'
import EmailSettingForm from '~/components/Settings/EmailSetting/EmailSettingForm'

const EmailSettingContainer = () => {
  const { t } = useTranslation()
  const { setToast } = useToastStore()
  const { trigger: updateEmailSignature, isLoading: isUpdating } = useSubmitCommon(MutationUpdateEmailSetting)
  const { isLoading, data: emailSignatureResponse } = useQueryGraphQL({
    query: QueryEmailSignatureSetting,
    variables: {},
    shouldPause: false
  })
  const onSubmitForm = useCallback(
    (data: EmailSignatureFormType) => {
      if (isUpdating) return Promise.resolve()

      return updateEmailSignature({
        emailSignature: data?.signature
      }).then(result => {
        if (result.error) {
          catchErrorFromGraphQL({
            error: result.error,
            setToast
          })

          return
        }

        return setToast({
          open: true,
          type: 'success',
          title: `${t('notification:saveChanges')}`
        })
      })
    },
    [isUpdating]
  )

  const tabs = [
    {
      value: 'form',
      children:
        !emailSignatureResponse || isLoading ? (
          <>
            {[1, 2, 3, 4, 5, 6].map(index => (
              <div key={index} className="mb-4">
                <Skeleton className="h-4 w-full" />
              </div>
            ))}
          </>
        ) : (
          <EmailSettingForm
            onFinishCallback={onSubmitForm}
            defaultValues={{
              signature: emailSignatureResponse?.userEmailSignatureShow
            }}
          />
        )
    }
  ]
  return (
    <LayoutGridSettings>
      <Container overrideClass="max-w-[656px] pt-6">
        <PageHeaderSimple
          title={`${t('settings:emailSetting:title')}`}
          description={
            <div className="contents">
              {t('settings:emailSetting:description')}
              <TextButton
                className="contents"
                classNameText="font-normal ml-1"
                label={`${t('common:learn_more')}`}
                onClick={() => {
                  window.open(pathConfiguration.helpCenter.emailSettings, '_blank')
                }}
              />
            </div>
          }
          tabs={tabs}
          divideLineHeader={<div className="mb-8 h-px w-full bg-gray-100" />}
        />
      </Container>
    </LayoutGridSettings>
  )
}

export default EmailSettingContainer
