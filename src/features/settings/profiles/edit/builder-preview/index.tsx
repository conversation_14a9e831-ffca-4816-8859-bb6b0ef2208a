'use client'

import { useEffect, useState } from 'react'

import type { CustomFieldViewType } from '~/lib/features/settings/profile-fields/types/custom-field'
import { useResumeStore } from '~/lib/features/settings/profiles/edit/store'

import ResumesPreviewTemplatesView from '~/components/Resumes/PreviewTemplates/ResumesPreviewTemplatesView'

function ResumesBuilderPreviewCVContainer() {
  const [locale, setLocale] = useState('en')
  const [customFieldViewData, setCustomFieldViewData] = useState<CustomFieldViewType[]>([])
  const { resumeData, setResume } = useResumeStore()

  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (event.origin !== window.location.origin) return

      if (event.data.type === 'SET_RESUME') setResume(event.data.payload)
      if (event.data.type === 'SET_CUSTOM_FIELD_VIEW_DATA') setCustomFieldViewData(event.data.payload)
    }

    const resumeData = window.localStorage.getItem('resume')
    if (resumeData) {
      const results = JSON.parse(resumeData)
      setLocale(results?.template?.locale)
      setCustomFieldViewData(results?.template?.customFieldViewData)
      setResume({
        ...results,
        template: undefined
      })
      return
    }

    window.addEventListener('message', handleMessage)
    return () => {
      window.removeEventListener('message', handleMessage)
    }
  }, [])

  return (
    <ResumesPreviewTemplatesView
      isPreview
      resumeData={resumeData}
      customFieldViewData={customFieldViewData}
      locale={locale}
      callbackFile={undefined}
      savingType={undefined}
      setSavingType={undefined}
    />
  )
}

export default ResumesBuilderPreviewCVContainer
