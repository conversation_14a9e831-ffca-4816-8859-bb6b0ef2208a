import { createContext } from 'react'

import withPermissionSettingProvider from 'src/hoc/with-permission-setting'
import type { IDefaultPermissionsList } from '~/core/utilities/feature-permission'
import { canAccessSetting, DEFAULT_PERMISSIONS_LIST, PERMISSIONS_LIST } from '~/core/utilities/feature-permission'

import usePermissionSetting from '~/lib/features/permissions/hooks/use-permission-setting'
import useBoundStore from '~/lib/store'

import LayoutGridSettings from '~/components/Layout/LayoutGridSettings'
import EmailTemplatesManagementView from '~/components/Settings/EmailTemplates'

export const EmailTemplatesManagementPermissionContext = createContext<IDefaultPermissionsList>(DEFAULT_PERMISSIONS_LIST)

const EmailTemplatesManagementContainer = () => {
  const { user } = useBoundStore()
  const { actionEmailTemplate } = usePermissionSetting()

  return (
    <LayoutGridSettings>
      <EmailTemplatesManagementPermissionContext.Provider
        value={{
          create: !!actionEmailTemplate,
          update: !!actionEmailTemplate,
          delete: !!actionEmailTemplate
        }}
      >
        <EmailTemplatesManagementView user={user} />
      </EmailTemplatesManagementPermissionContext.Provider>
    </LayoutGridSettings>
  )
}

export default withPermissionSettingProvider(
  {
    checkAccessPermission: canAccessSetting,
    keyModule: [PERMISSIONS_LIST.template_setting.keyModule]
  },
  EmailTemplatesManagementContainer
)
