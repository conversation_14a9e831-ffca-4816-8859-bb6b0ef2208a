import { createContext } from 'react'

import withPermissionSettingProvider from 'src/hoc/with-permission-setting'
import withQueryClientProvider from 'src/hoc/with-query-client-provider'
import type { IDefaultPermissionsList } from '~/core/utilities/feature-permission'
import { canAccessSetting, DEFAULT_PERMISSIONS_LIST, PERMISSIONS_LIST } from '~/core/utilities/feature-permission'

import usePermissionSetting from '~/lib/features/permissions/hooks/use-permission-setting'

import LayoutGridSettings from '~/components/Layout/LayoutGridSettings'
import CustomFieldsManagementView from '~/components/Settings/CustomFields'

export const CustomFieldsManagementPermissionContext = createContext<IDefaultPermissionsList>(DEFAULT_PERMISSIONS_LIST)

const CustomFieldsManagementContainer = () => {
  const { actionCustomField } = usePermissionSetting()

  return (
    <LayoutGridSettings>
      <CustomFieldsManagementPermissionContext.Provider
        value={{
          create: !!actionCustomField,
          update: !!actionCustomField,
          delete: !!actionCustomField
        }}
      >
        <CustomFieldsManagementView />
      </CustomFieldsManagementPermissionContext.Provider>
    </LayoutGridSettings>
  )
}

export default withPermissionSettingProvider(
  {
    checkAccessPermission: canAccessSetting,
    keyModule: [PERMISSIONS_LIST.tenant_setting.keyModule]
  },
  withQueryClientProvider(CustomFieldsManagementContainer)
)
