import { createContext } from 'react'

import withPermissionSettingProvider from 'src/hoc/with-permission-setting'
import type { IDefaultPermissionsList } from '~/core/utilities/feature-permission'
import { canAccessSetting, DEFAULT_PERMISSIONS_LIST, PERMISSIONS_LIST } from '~/core/utilities/feature-permission'

import usePermissionSetting from '~/lib/features/permissions/hooks/use-permission-setting'
import useBoundStore from '~/lib/store'

import LayoutGridSettings from '~/components/Layout/LayoutGridSettings'
import DepartmentsManagementView from '~/components/Settings/Departments'

export const DepartmentsManagementPermissionContext = createContext<IDefaultPermissionsList>(DEFAULT_PERMISSIONS_LIST)

const DepartmentsManagementContainer = () => {
  const { user } = useBoundStore()
  const { actionDepartment } = usePermissionSetting()

  return (
    <LayoutGridSettings>
      <DepartmentsManagementPermissionContext.Provider
        value={{
          create: !!actionDepartment,
          update: !!actionDepartment,
          delete: !!actionDepartment
        }}
      >
        <DepartmentsManagementView user={user} />
      </DepartmentsManagementPermissionContext.Provider>
    </LayoutGridSettings>
  )
}

export default withPermissionSettingProvider(
  {
    checkAccessPermission: canAccessSetting,
    keyModule: [PERMISSIONS_LIST.tenant_setting.keyModule]
  },
  DepartmentsManagementContainer
)
