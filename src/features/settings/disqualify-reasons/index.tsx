import { createContext } from 'react'

import withPermissionSettingProvider from 'src/hoc/with-permission-setting'
import type { IDefaultPermissionsList } from '~/core/utilities/feature-permission'
import { canAccessSetting, DEFAULT_PERMISSIONS_LIST, PERMISSIONS_LIST } from '~/core/utilities/feature-permission'

import usePermissionSetting from '~/lib/features/permissions/hooks/use-permission-setting'
import useBoundStore from '~/lib/store'

import LayoutGridSettings from '~/components/Layout/LayoutGridSettings'
import DisqualifyReasonsManagementView from '~/components/Settings/DisqualifyReasons'

export const DisqualifyReasonsManagementPermissionContext = createContext<IDefaultPermissionsList>(DEFAULT_PERMISSIONS_LIST)

const DisqualifyReasonsManagementContainer = () => {
  const { user } = useBoundStore()
  const { actionRejectedReason } = usePermissionSetting()

  return (
    <LayoutGridSettings>
      <DisqualifyReasonsManagementPermissionContext.Provider
        value={{
          create: !!actionRejectedReason,
          update: !!actionRejectedReason,
          delete: !!actionRejectedReason
        }}
      >
        <DisqualifyReasonsManagementView user={user} />
      </DisqualifyReasonsManagementPermissionContext.Provider>
    </LayoutGridSettings>
  )
}

export default withPermissionSettingProvider(
  {
    checkAccessPermission: canAccessSetting,
    keyModule: [PERMISSIONS_LIST.tenant_setting.keyModule]
  },
  DisqualifyReasonsManagementContainer
)
