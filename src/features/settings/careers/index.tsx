import { createContext } from 'react'

import withPermissionSettingProvider from 'src/hoc/with-permission-setting'
import type { IDefaultPermissionsList } from '~/core/utilities/feature-permission'
import { canAccessSetting, DEFAULT_PERMISSIONS_LIST, PERMISSIONS_LIST } from '~/core/utilities/feature-permission'

import usePermissionSetting from '~/lib/features/permissions/hooks/use-permission-setting'

import LayoutGridSettings from '~/components/Layout/LayoutGridSettings'
import CareersView from '~/components/Settings/Careers'

export const CareersPermissionContext = createContext<IDefaultPermissionsList>(DEFAULT_PERMISSIONS_LIST)

const CareersSettingContainer = () => {
  const { actionCareer } = usePermissionSetting()

  return (
    <LayoutGridSettings>
      <CareersPermissionContext.Provider
        value={{
          create: !!actionCareer,
          update: !!actionCareer,
          delete: !!actionCareer
        }}
      >
        <CareersView />
      </CareersPermissionContext.Provider>
    </LayoutGridSettings>
  )
}

export default withPermissionSettingProvider(
  {
    checkAccessPermission: canAccessSetting,
    keyModule: [PERMISSIONS_LIST.career_page.keyModule]
  },
  CareersSettingContainer
)
