'use client'

import { useRouter } from 'next/navigation'
import { createContext, useCallback, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'

import withPermissionSettingProvider from 'src/hoc/with-permission-setting'
import withQueryClient<PERSON>rovider from 'src/hoc/with-query-client-provider'
import configuration from '~/configuration'
import useQueryGraphQL from '~/core/middleware/use-query-graphQL'
import { <PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger, TabsTriggerView } from '~/core/ui/Tabs'
import type { IDefaultPermissionsList } from '~/core/utilities/feature-permission'
import { canAccessSetting, DEFAULT_PERMISSIONS_LIST, PERMISSIONS_LIST } from '~/core/utilities/feature-permission'

import type { TenantType } from '~/lib/features/careers/[id]/types'
import MutationUpdateCareerPageTemplate from '~/lib/features/settings/careers/graphql/mutation-update-career-template'
import QueryCareersTemplateShow from '~/lib/features/settings/careers/graphql/query-career-template'
import QueryCareersSetting from '~/lib/features/settings/careers/graphql/query-careers-setting'
import { formatSection, mappingTemplateRespToSectionAndConfigData } from '~/lib/features/settings/careers/mapping/editor-mapping'
import type { SectionsListType } from '~/lib/features/settings/careers/types/editor'
import useWorkSpace from '~/lib/features/settings/workspace/hooks/use-workspace'
import { useSubmitCommon } from '~/lib/hooks/use-submit-graphql-common'
import useLoadingBlockStore from '~/lib/store/loading-block'

import CareerPageSettingTab from '~/components/Settings/Careers/Editor/CareerPageSettingTab'
import CareerPreviewEditor from '~/components/Settings/Careers/Editor/CareerPreviewEditor'
import type { SettingEditorFormType } from '~/components/Settings/Careers/Editor/CustomizeSettingTab'
import CustomizeSettingTab from '~/components/Settings/Careers/Editor/CustomizeSettingTab'

export const CareersPermissionContext = createContext<IDefaultPermissionsList>(DEFAULT_PERMISSIONS_LIST)

const TAB_CAREER_PAGE = 'career_page'
const TAB_SETTING = 'setting'

const CareersSettingEditorContainer = () => {
  const { t } = useTranslation()
  const router = useRouter()
  const { setShowLockApp, setCloseLockApp } = useLoadingBlockStore()
  const [tab, setTab] = useState<string>(TAB_CAREER_PAGE)
  const [sections, setSections] = useState<SectionsListType>({
    lock: [],
    draggable: []
  })

  const { tenantShow } = useWorkSpace({ shouldPause: false })
  const { data: careerTemplate } = useQueryGraphQL({
    query: QueryCareersTemplateShow,
    variables: {},
    shouldPause: false
  })
  const { data: careerSiteSetting } = useQueryGraphQL({
    query: QueryCareersSetting,
    variables: {},
    shouldPause: false
  })

  const { trigger: updateCareerPageTemplate, isLoading } = useSubmitCommon(MutationUpdateCareerPageTemplate)

  const [templateConfig, setTemplateConfig] = useState<SettingEditorFormType>()

  const onUpdateSectionsCareerTemplate = useCallback(
    (sections: SectionsListType, isLockScreen?: boolean) => {
      //only form with Upload Image Component will have this param is true
      isLockScreen && setShowLockApp('')
      const data = {
        ...careerTemplate?.careerTemplatesShow,
        id: Number(careerTemplate?.careerTemplatesShow?.id),
        references: {
          brand_color: templateConfig?.brandColor,
          title_color: templateConfig?.titleColor,
          subtitle_color: templateConfig?.secondaryColor
        },
        sections: formatSection([...(sections?.lock || []), ...(sections?.draggable || [])])
      }

      return updateCareerPageTemplate(data).then(result => {
        isLockScreen && setCloseLockApp()
        return result?.data?.careerPageTemplatesUpdate?.template
      })
    },
    [careerTemplate, templateConfig]
  )

  const onUpdateConfigCareerTemplate = useCallback(
    (newConfigTemplate: SettingEditorFormType) => {
      if (
        (newConfigTemplate?.logo && typeof newConfigTemplate?.logo === 'object') ||
        (newConfigTemplate?.favicon && typeof newConfigTemplate?.favicon === 'object')
      )
        setShowLockApp('')
      const data = {
        id: Number(careerTemplate?.careerTemplatesShow?.id),
        references: {
          brand_color: newConfigTemplate?.brandColor,
          title_color: newConfigTemplate?.titleColor,
          subtitle_color: newConfigTemplate?.secondaryColor
        },
        ...(newConfigTemplate?.logo && typeof newConfigTemplate?.logo === 'object'
          ? {
              logo: newConfigTemplate?.logo
            }
          : {}),
        ...(newConfigTemplate?.favicon && typeof newConfigTemplate?.favicon === 'object'
          ? {
              favicon: newConfigTemplate?.favicon
            }
          : {})
      }

      return updateCareerPageTemplate(data).then(res => {
        const dataRes = res.data?.careerPageTemplatesUpdate?.template
        const logoRes = (dataRes?.setting?.images as Array<{ key: string; file: string }>).find(image => image.key === 'career_site_logo')
        const faviconRes = (dataRes?.setting?.images as Array<{ key: string; file: string }>).find(image => image.key === 'career_site_favicon')
        setTemplateConfig({
          ...newConfigTemplate,
          ...(logoRes?.file ? { logo: logoRes.file } : {}),
          ...(faviconRes?.file ? { favicon: faviconRes.file } : {})
        })
        if (
          (newConfigTemplate?.logo && typeof newConfigTemplate?.logo === 'object') ||
          (newConfigTemplate?.favicon && typeof newConfigTemplate?.favicon === 'object')
        ) {
          setCloseLockApp()
        }
      })
    },
    [careerTemplate, sections]
  )

  const onChangeSettingTab = useCallback(
    (data: SettingEditorFormType) => {
      return onUpdateConfigCareerTemplate(data)
    },
    [sections]
  )

  useEffect(() => {
    if (careerTemplate) {
      const { templateConfig, sections } = mappingTemplateRespToSectionAndConfigData(
        careerTemplate.careerTemplatesShow,
        careerSiteSetting?.tenantSettingsCareerSite
      )

      setTemplateConfig(templateConfig)
      sections && setSections(sections)
    }
  }, [careerTemplate, careerSiteSetting])

  return (
    <div className="grid h-screen grid-cols-[476px_1fr]">
      <div className="flex h-full flex-col overflow-y-hidden border-r border-r-100 pt-[18px]">
        <Tabs value={tab} onValueChange={setTab} className="flex-none border-b border-b-gray-100">
          <TabsList size="sm" isFullWidth={true}>
            <TabsTrigger isFullWidth value={TAB_CAREER_PAGE} size="sm" classNameButton="pb-4" gapSize="sm">
              <TabsTriggerView
                size="sm"
                session={{
                  icon: 'ArrowLeftIcon',
                  iconTooltip: `${t('button:back')}`,
                  iconClassNameAsChild: 'absolute left-[14px] top-4',
                  iconProps: {
                    onClick: () => router.push(configuration?.path?.settings?.careers)
                  },
                  value: TAB_CAREER_PAGE,
                  label: `${t('career_editor:career_page')}`
                }}
              />
            </TabsTrigger>
            <TabsTrigger classNameButton="pb-4" isFullWidth value={TAB_SETTING} size="sm" gapSize="sm">
              <TabsTriggerView
                size="sm"
                session={{
                  value: TAB_SETTING,
                  label: `${t('career_editor:setting')}`
                }}
              />
            </TabsTrigger>
          </TabsList>
        </Tabs>
        <div className="flex-1 overflow-y-auto px-5 pt-4">
          {
            {
              [TAB_CAREER_PAGE]: (
                <CareerPageSettingTab
                  sections={sections}
                  setSections={setSections}
                  careerSiteSetting={careerSiteSetting?.tenantSettingsCareerSite}
                  onUpdateSectionsCareerTemplate={onUpdateSectionsCareerTemplate}
                />
              ),
              [TAB_SETTING]: <CustomizeSettingTab defaultValue={templateConfig} onChangeSetting={onChangeSettingTab} />
            }[tab]
          }
        </div>
      </div>
      <div className="flex h-full flex-col overflow-y-hidden">
        <CareerPreviewEditor
          queryStringParams={{}}
          sections={[...(sections?.lock || []), ...(sections?.draggable || [])]}
          templateConfig={templateConfig}
          tenant={{
            ...(tenantShow as Omit<TenantType, 'openJobsCount'>),
            openJobsCount: 0
          }}
        />
      </div>
    </div>
  )
}

export default withPermissionSettingProvider(
  {
    checkAccessPermission: canAccessSetting,
    keyModule: [PERMISSIONS_LIST.career_page.keyModule]
  },
  withQueryClientProvider(CareersSettingEditorContainer)
)
