import { createContext } from 'react'

import withPermissionSettingProvider from 'src/hoc/with-permission-setting'
import { AGENCY_TENANT } from '~/core/constants/enum'
import type { IDefaultPermissionsList } from '~/core/utilities/feature-permission'
import { canAccessSetting, DEFAULT_PERMISSIONS_LIST, PERMISSIONS_LIST } from '~/core/utilities/feature-permission'

import usePermissionSetting from '~/lib/features/permissions/hooks/use-permission-setting'
import useDetectCompanyWithKind from '~/lib/hooks/use-detect-company-with-kind'
import useBoundStore from '~/lib/store'

import LayoutGridSettings from '~/components/Layout/LayoutGridSettings'
import HiringPipelineManagementView from '~/components/Settings/HiringPipelines'

export const HiringPipelineManagementPermissionContext = createContext<IDefaultPermissionsList>(DEFAULT_PERMISSIONS_LIST)

function HiringPipelinesManagement() {
  const { user } = useBoundStore()
  const { actionPipelineTemplate } = usePermissionSetting()
  const { isCompanyKind } = useDetectCompanyWithKind({ kind: AGENCY_TENANT })

  return (
    <LayoutGridSettings>
      <HiringPipelineManagementPermissionContext.Provider
        value={{
          create: !!actionPipelineTemplate,
          update: !!actionPipelineTemplate,
          delete: !!actionPipelineTemplate
        }}
      >
        <HiringPipelineManagementView user={user} />
      </HiringPipelineManagementPermissionContext.Provider>
    </LayoutGridSettings>
  )
}

export default withPermissionSettingProvider(
  {
    checkAccessPermission: canAccessSetting,
    keyModule: [PERMISSIONS_LIST.template_setting.keyModule]
  },
  HiringPipelinesManagement
)
