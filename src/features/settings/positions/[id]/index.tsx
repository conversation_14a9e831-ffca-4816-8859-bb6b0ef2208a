'use client'

import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { createContext, useCallback, useEffect } from 'react'
import { Trans, useTranslation } from 'react-i18next'

import withPermissionSettingProvider from 'src/hoc/with-permission-setting'
import configuration from '~/configuration'
import type { ISelectOption } from '~/core/@types/global'
import { PUBLIC_APP_NAME } from '~/core/constants/env'
import useQueryGraphQL from '~/core/middleware/use-query-graphQL'
import { openAlert } from '~/core/ui/AlertDialog'
import { Avatar } from '~/core/ui/Avatar'
import { Button } from '~/core/ui/Button'
import ComboboxSelect from '~/core/ui/ComboboxSelect'
import type { IDotColorProps } from '~/core/ui/Dot'
import { TypographyH5 } from '~/core/ui/Heading'
import { Skeleton } from '~/core/ui/Skeleton'
import { SuggestionChips } from '~/core/ui/SuggestionChips'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, TabsTriggerView } from '~/core/ui/Tabs'
import { TypographyText } from '~/core/ui/Text'
import { Tooltip } from '~/core/ui/Tooltip'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'
import type { IDefaultPermissionsList } from '~/core/utilities/feature-permission'
import { canAccessSetting, DEFAULT_PERMISSIONS_LIST, PERMISSIONS_LIST } from '~/core/utilities/feature-permission'
import { defaultFormatDate, fullFormatDate } from '~/core/utilities/format-date'

import usePermissionSetting from '~/lib/features/permissions/hooks/use-permission-setting'
import QueryShowTenantPosition from '~/lib/features/settings/positions/graphql/query-show-position'
import QueryChangeStatusPositionMutation from '~/lib/features/settings/positions/graphql/submit-change-status-position-mutation'
import QueryDeletePositionMutation from '~/lib/features/settings/positions/graphql/submit-delete-position-mutation'
import usePositionOptions from '~/lib/features/settings/positions/hooks/use-position-options'
import type { POSITION_STATUS_ENUM } from '~/lib/features/settings/positions/utilities/enum'
import { POSITION_DOT_STATUS } from '~/lib/features/settings/positions/utilities/enum'
import useBrowserTab from '~/lib/hooks/use-browser-tab'
import { useSubmitCommon } from '~/lib/hooks/use-submit-graphql-common'
import { useRouterContext } from '~/lib/next/use-router-context'
import useToastStore from '~/lib/store/toast'

import AppHeadMetaTags from '~/components/AppHeadMetaTags'
import LayoutGridSettings from '~/components/Layout/LayoutGridSettings'
import PositionDetailsTab from '~/components/Settings/Positions/PositionDetailsTab'
import SkeletonContainer from '~/components/Skeleton'

export const PositionsManagementPermissionContext = createContext<IDefaultPermissionsList>(DEFAULT_PERMISSIONS_LIST)

const PositionDetailsManagementContainer = () => {
  const { canAccessModule } = usePermissionSetting()
  const { setToast } = useToastStore()
  const router = useRouter()
  const { params } = useRouterContext()
  const id = params?.id
  const { t } = useTranslation()
  const { optionsStatusPosition } = usePositionOptions()
  const { trigger: triggerFetchDetail, data } = useQueryGraphQL({
    query: QueryShowTenantPosition,
    variables: { id: Number(id) },
    shouldPause: true
  })
  const { trigger: deletePosition, isLoading: isLoadingDelete } = useSubmitCommon(QueryDeletePositionMutation)
  const { trigger: triggerChangeStatus, isLoading } = useSubmitCommon(QueryChangeStatusPositionMutation)
  useEffect(() => {
    if (id) {
      triggerFetchDetail()
    }
  }, [id, triggerFetchDetail])

  const deletePositionCallback = useCallback(async () => {
    if (isLoadingDelete) {
      return false
    }
    return await deletePosition({
      id: Number(data?.tenantPositionsShow?.id)
    }).then(result => {
      if (result.error) {
        catchErrorFromGraphQL({
          error: result.error,
          page: configuration.path.settings.positions,
          setToast
        })

        return false
      }
      const { positionsDelete } = result.data
      if (positionsDelete.success) {
        setToast({
          open: true,
          type: 'success',
          title: t('notification:positionDeleted')
        })
      }

      return router.push(configuration.path.settings.positions)
    })
  }, [data, isLoadingDelete, deletePosition, setToast, t])

  const showAlertConfirmDeletePosition = () => {
    openAlert({
      className: 'w-[480px]',
      title: `${t('settings:positions:removePositionAlert:deleteTitle')}`,
      description: data?.tenantPositionsShow?.id ? (
        <Trans
          i18nKey={'settings:positions:removePositionAlert:deleteDescription'}
          values={{
            name: data?.tenantPositionsShow?.name
          }}
        >
          <span className="font-medium text-gray-900" />
        </Trans>
      ) : (
        ''
      ),
      actions: [
        {
          label: t('button:cancel') || '',
          type: 'secondary',
          size: 'sm'
        },
        {
          isCallAPI: true,
          label: t('button:delete') || '',
          type: 'destructive',
          size: 'sm',
          onClick: async () => await deletePositionCallback()
        }
      ]
    })
  }

  const showAlertCanNotDeletePosition = () => {
    openAlert({
      className: 'w-[480px]',
      title: `${t('settings:positions:removePositionAlert:canNotDeleteTitle')}`,
      description: (
        <Trans i18nKey={'settings:positions:removePositionAlert:canNotDeleteDescription'}>
          <span className="font-medium text-gray-900" />
        </Trans>
      ),
      actions: [
        {},
        {
          label: t('button:close') || '',
          type: 'primary',
          size: 'sm'
        }
      ]
    })
  }

  const changeStatusCallback = useCallback(
    async (status: string) => {
      if (isLoading) {
        return
      }

      triggerChangeStatus({
        id: Number(id),
        status
      }).then(result => {
        if (result.error) {
          return catchErrorFromGraphQL({
            error: result.error,
            page: configuration.path.settings.positions,
            setToast
          })
        }

        const { positionsUpdate } = result.data

        if (positionsUpdate.position.id) {
          triggerFetchDetail()
          setToast({
            open: true,
            type: 'success',
            title: t('notification:positionUpdated')
          })
        }

        return
      })
    },
    [isLoading, triggerChangeStatus, id, setToast, triggerFetchDetail, t]
  )

  const tabControl = useBrowserTab({
    defaultValue: 'details',
    queryKeyName: 'tabs',
    excludeQueryKeysName: ['id']
  })

  const status = data?.tenantPositionsShow?.status as POSITION_STATUS_ENUM

  return (
    <>
      <AppHeadMetaTags
        title={`${t(`common:seo:settingPositionDetail`, {
          positionDetail: String(data?.tenantPositionsShow?.name || 'Position Detail'),
          PUBLIC_APP_NAME
        })}`}
      />

      <LayoutGridSettings>
        <PositionsManagementPermissionContext.Provider
          value={{
            create: !!canAccessModule,
            update: !!canAccessModule,
            delete: !!canAccessModule
          }}
        >
          <>
            <Tabs {...tabControl} className="flex h-full flex-col overflow-hidden">
              <div className="flex flex-none justify-between border-b border-b-gray-100 bg-white px-6 pt-3">
                <div className="flex flex-col space-y-3 pr-[40px]">
                  <SkeletonContainer
                    showMoreLabel={`${t('common:infinity:showMore')}`}
                    useLoading={false}
                    isFirstLoading={data?.tenantPositionsShow === undefined}
                    renderCustomSkeleton={<Skeleton className="h-[26px] w-full rounded-full" />}
                  >
                    <div className="flex min-h-[26px] items-center space-x-3">
                      <TypographyH5>{data?.tenantPositionsShow.name}</TypographyH5>
                    </div>
                  </SkeletonContainer>

                  <TabsList size="sm">
                    <TabsTrigger size="sm" value="details">
                      <TabsTriggerView
                        size="sm"
                        session={{
                          value: 'details',
                          label: `${t('label:tab:details')}`
                        }}
                      />
                    </TabsTrigger>
                  </TabsList>
                </div>
                <div className="flex flex-col items-end space-y-3.5">
                  <div className="flex space-x-2">
                    <div className="flex">
                      <ComboboxSelect
                        closeOnSelect
                        placeholder={`${t('settings:positions:allStatus')}`}
                        dropdownMenuClassName="w-[126px]! right-0"
                        buttonClassName="min-w-[88px]"
                        menuOptionAlign="end"
                        value={
                          !!status
                            ? ({
                                value: status,
                                dot: POSITION_DOT_STATUS(status || 'gray') as IDotColorProps,
                                supportingObj: {
                                  name: {
                                    active: t('settings:positions:positionStatus:active'),
                                    archived: t('settings:positions:positionStatus:archived'),
                                    draft: t('settings:positions:positionStatus:draft')
                                  }[status as POSITION_STATUS_ENUM]
                                }
                              } as ISelectOption)
                            : undefined
                        }
                        size="sm"
                        isClearable={false}
                        isSearchable={false}
                        isLoading={isLoading}
                        onChange={(option: any) => {
                          changeStatusCallback(option?.value as string)
                        }}
                        configSelectOption={{
                          supportingText: ['supportingObj'],
                          dot: true
                        }}
                        options={optionsStatusPosition}
                      />
                      <Link href={configuration.path.settings.positionsEdit(Number(data?.tenantPositionsShow?.id))}>
                        <Tooltip mode="icon" align="center" position="bottom" content={`${t('button:edit')}`}>
                          <Button className="ml-2" size="xs" iconMenus="Edit3" type="tertiary" label={`${t('button:edit')}`} />
                        </Tooltip>
                      </Link>
                      <Tooltip mode="icon" align="center" position="bottom" content={`${t('button:delete')}`}>
                        <Button
                          className="ml-2"
                          size="xs"
                          iconMenus="Trash2"
                          type="destructive"
                          onClick={() => {
                            data?.tenantPositionsShow?.linkedToProfiles ? showAlertCanNotDeletePosition() : showAlertConfirmDeletePosition()
                          }}
                        />
                      </Tooltip>
                    </div>
                  </div>
                  <div className="flex w-max flex-row items-center">
                    <div className="flex">
                      <div className="flex items-center">
                        <TypographyText className="mr-2 text-xs text-gray-600">{t('label:createBy')}</TypographyText>
                        <Avatar
                          color={data?.tenantPositionsShow?.createdBy.defaultColour}
                          size="sm"
                          alt={data?.tenantPositionsShow?.createdBy.fullName}
                          src={data?.tenantPositionsShow?.createdBy?.avatarVariants?.thumb?.url}
                        />
                        {data?.tenantPositionsShow?.createdAt ? (
                          <div className="ml-2">
                            <Tooltip content={fullFormatDate(new Date(data?.tenantPositionsShow?.createdAt))}>
                              <TypographyText className="text-xs whitespace-nowrap text-gray-600">
                                {defaultFormatDate(new Date(data?.tenantPositionsShow?.createdAt))}
                              </TypographyText>
                            </Tooltip>
                          </div>
                        ) : null}
                      </div>
                      {data?.tenantPositionsShow?.createdAt !== data?.tenantPositionsShow?.updatedAt ? (
                        <div className="ml-3 flex items-center">
                          <span className="mr-3 inline-block h-1 w-1 flex-none rounded-full bg-gray-400"></span>
                          <TypographyText className="mr-2 text-xs text-gray-600">{t('label:updateBy')}</TypographyText>
                          <Avatar
                            color={data?.tenantPositionsShow?.updatedBy.defaultColour}
                            size="sm"
                            alt={data?.tenantPositionsShow?.updatedBy.fullName}
                            src={data?.tenantPositionsShow?.updatedBy?.avatarVariants?.thumb?.url}
                          />
                          {data?.tenantPositionsShow?.updatedAt ? (
                            <div className="ml-2">
                              <Tooltip content={fullFormatDate(new Date(data?.tenantPositionsShow?.updatedAt))}>
                                <TypographyText className="text-xs whitespace-nowrap text-gray-600">
                                  {defaultFormatDate(new Date(data?.tenantPositionsShow?.updatedAt))}
                                </TypographyText>
                              </Tooltip>
                            </div>
                          ) : null}
                        </div>
                      ) : null}
                    </div>
                  </div>
                </div>
              </div>

              <TabsContent value="details" className="overflow-y-auto">
                <PositionDetailsTab
                  title={t('job:detail:summaryInfo:skills')}
                  iconMenus="StretchHorizontal"
                  showEmpty={(data?.tenantPositionsShow?.skills || [])?.length === 0}
                  description={data?.tenantPositionsShow?.description}
                >
                  <SuggestionChips
                    size="sm"
                    source={
                      data?.tenantPositionsShow?.skills?.map((skill: any) => ({
                        label: skill,
                        maxLength: 30
                      })) || []
                    }
                    type="default"
                  />
                </PositionDetailsTab>
              </TabsContent>
            </Tabs>
          </>
        </PositionsManagementPermissionContext.Provider>
      </LayoutGridSettings>
    </>
  )
}

export default withPermissionSettingProvider(
  {
    checkAccessPermission: canAccessSetting,
    keyModule: [PERMISSIONS_LIST.tenant_setting.keyModule]
  },
  PositionDetailsManagementContainer
)
