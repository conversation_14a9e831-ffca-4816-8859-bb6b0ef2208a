'use client'

import { createContext } from 'react'

import withPermissionSettingProvider from 'src/hoc/with-permission-setting'
import type { IDefaultPermissionsList } from '~/core/utilities/feature-permission'
import { canAccessSetting, DEFAULT_PERMISSIONS_LIST, PERMISSIONS_LIST } from '~/core/utilities/feature-permission'

import usePermissionSetting from '~/lib/features/permissions/hooks/use-permission-setting'

import LayoutGridSettings from '~/components/Layout/LayoutGridSettings'
import PositionCreate from '~/components/Settings/Positions/PositionCreate'

export const PositionsManagementPermissionContext = createContext<IDefaultPermissionsList>(DEFAULT_PERMISSIONS_LIST)

const PositionCreateManagementContainer = () => {
  const { canAccessModule } = usePermissionSetting()

  return (
    <LayoutGridSettings>
      <PositionsManagementPermissionContext.Provider
        value={{
          create: !!canAccessModule,
          update: !!canAccessModule,
          delete: !!canAccessModule
        }}
      >
        <PositionCreate />
      </PositionsManagementPermissionContext.Provider>
    </LayoutGridSettings>
  )
}

export default withPermissionSettingProvider(
  {
    checkAccessPermission: canAccessSetting,
    keyModule: [PERMISSIONS_LIST.tenant_setting.keyModule]
  },
  PositionCreateManagementContainer
)
