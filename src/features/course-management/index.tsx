'use client'

import { useCallback, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'

import configuration from '~/configuration'
import type { IFormAction } from '~/core/@types/global'
import useContextGraphQL from '~/core/middleware/use-context-graphQL'
import { Badge } from '~/core/ui/Badge'
import { Dialog } from '~/core/ui/Dialog'
import { IDataTableInfinityOrdering } from '~/core/ui/TableInfinityOrdering'
import type { IPagePagination } from '~/core/ui/TablePagination'
import { cn } from '~/core/ui/utils'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'

import usePaginationGraphPage from '~/lib/hooks/use-pagination-graph-page'
import { useSubmitCommon } from '~/lib/hooks/use-submit-graphql-common'
import useBoundStore from '~/lib/store'
import useToastStore from '~/lib/store/toast'

import BulkActions from '~/components/CoursesManagement/BulkActions/BulkActions'
import { useClassBasedTopSpace } from '~/components/Subscription/TopSpace'

import CourseForm from '../../components/CoursesManagement/CourseForm'
import CourseListingTable from '../../components/CoursesManagement/CourseListingTable'
import FilterCourseManagement from '../../components/CoursesManagement/FilterCourseManagement'
import type { FeatureName } from '../../components/Subscription/subscription'
import { withSubscriptionPlanLockFearture } from '../../components/Subscription/SubscriptionPlan'
import Empty from '../../core/ui/Empty'
import { canAccessSetting, PERMISSIONS_LIST } from '../../core/utilities/feature-permission'
import withPermissionSettingProvider from '../../hoc/with-permission-setting'
import MutationCourseCreate from '../../lib/features/course-management/graphql/mutation-course-create'
import QueryDeleteCourseMutation from '../../lib/features/course-management/graphql/mutation-course-delete'
import MutationCourseEdit from '../../lib/features/course-management/graphql/mutation-course-edit'
import QueryCoursesManagement from '../../lib/features/course-management/graphql/query-course-management-list'
import useCourseStatuses from '../../lib/features/course-management/hooks/use-course-statuses'
import {
  mappingCourseAddGraphQL,
  mappingCourseEditGraphQL,
  mappingCourseForm,
  mappingCourseListingGraphQL
} from '../../lib/features/course-management/mapping/course-mapping'
import type {
  CourseManagementForm,
  CourseManagementType,
  CourseSortingType,
  ICourseManagementFilter
} from '../../lib/features/course-management/types'
import { COURSE_STATUS_ENUM, DESC_SORTING } from '../../lib/features/course-management/utilities/enum'
import { PLAN_FEATURE_KEYS } from '../../lib/features/settings/plans/utilities/enum'

const DEFAULT_FILTER = {
  page: 1
}

const CourseManagementContainer = () => {
  const { clientGraphQL } = useContextGraphQL()
  const { t } = useTranslation()
  const { optionsStatusCourse } = useCourseStatuses()
  const { setRefetchMyList, refetchMyList, refetchMyDelete, setRefetchMyDelete, bulkSelectedAll, bulkValues } = useBoundStore()
  const { setToast } = useToastStore()

  const [filter, changeFilter] = useState<ICourseManagementFilter | undefined>(DEFAULT_FILTER)
  const [isDefaultFilters, setIsDefaultFilters] = useState<boolean>(true)
  const [selectedCourse, setSelectedCourse] = useState<CourseManagementType>()
  const [count, setCount] = useState(0)
  const [openCourse, setOpenCourse] = useState<boolean>(false)

  const [sorting, setSorting] = useState<CourseSortingType>({
    createdAt: DESC_SORTING
  })
  const [queryKey, setQueryKey] = useState<ICourseManagementFilter>({
    statuses: optionsStatusCourse.filter(statusCourse => [COURSE_STATUS_ENUM.active].includes(statusCourse.value))
  })

  const topSpace = useClassBasedTopSpace({
    34: 'h-full',
    default: 'h-screen'
  })

  const { data, isFetching, refetch, fetchPagination, forceChangeCurrentPage } = usePaginationGraphPage({
    queryDocumentNode: QueryCoursesManagement,
    queryKey: 'course-management-listing',
    filter: mappingCourseListingGraphQL({
      ...queryKey,
      sorting,
      limit: configuration.defaultPageSize
    })
  })

  const handleChangeInput = (filter: ICourseManagementFilter | CourseSortingType) => {
    setTimeout(() => {
      setQueryKey(queryPrev => ({
        ...queryPrev,
        ...filter,
        sorting: sorting,
        page: 1
      }))
    }, 0)
  }

  const { trigger: triggerCourseCreate, isLoading: loadingCourseCreate } = useSubmitCommon(MutationCourseCreate)

  const { trigger: triggerCourseUpdate, isLoading: loadingCourseUpdate } = useSubmitCommon(MutationCourseEdit)

  const { trigger: triggerDelete, isLoading: isLoadingDelete } = useSubmitCommon(QueryDeleteCourseMutation)

  const editCourseCallback = (course: CourseManagementType) => {
    setSelectedCourse(course)
    setOpenCourse(true)
    return Promise.resolve()
  }

  const createCourseCallback = () => {
    setSelectedCourse(undefined)
    setOpenCourse(true)
    return Promise.resolve()
  }

  const onSubmitAddForm = useCallback(
    async (data: CourseManagementForm, formAction?: IFormAction) => {
      if (loadingCourseCreate) {
        return
      }
      triggerCourseCreate(mappingCourseAddGraphQL(data)).then(result => {
        if (result.error) {
          return catchErrorFromGraphQL({
            error: result.error,
            setToast,
            callbackHandleStatusError422: keys => {
              keys.forEach(session => {
                if (session.field && formAction?.control._fields[session.field]) {
                  formAction?.setError(session.field, {
                    type: 'custom',
                    message: session.message
                  })
                }
              })
            }
          })
        }

        const { tenantCoursesCreate } = result.data
        if (tenantCoursesCreate.tenantCourse) {
          setToast({
            open: true,
            type: 'success',
            title: `${t('course:detail:notification:createCourse')}`
          })
          setOpenCourse(false)
          refetch()
        }
        return
      })
    },
    [loadingCourseCreate, triggerCourseCreate, setToast, t, refetch]
  )

  const onSubmitUpdateForm = useCallback(
    async (data: CourseManagementForm, formAction?: IFormAction) => {
      if (loadingCourseUpdate || !selectedCourse?.id) return

      const mappedData = mappingCourseEditGraphQL({
        ...data,
        id: selectedCourse.id
      })

      const result = await triggerCourseUpdate(mappedData)

      if (result.error) {
        return catchErrorFromGraphQL({
          error: result.error,
          setToast,
          callbackHandleStatusError422: keys => {
            keys.forEach(session => {
              if (session.field && formAction?.control._fields[session.field]) {
                formAction?.setError(session.field, {
                  type: 'custom',
                  message: session.message
                })
              }
            })
          }
        })
      }

      const updatedCourse = result.data?.tenantCoursesUpdate?.tenantCourse
      if (updatedCourse) {
        setToast({
          open: true,
          type: 'success',
          title: t('course:detail:notification:updateCourseSuccess')
        })
        setOpenCourse(false)
        refetch()
      }
      return true
    },
    [loadingCourseUpdate, selectedCourse, triggerCourseUpdate, refetch, setToast, t]
  )

  const deleteCourseCallback = useCallback(
    async (course: CourseManagementType) => {
      if (isLoadingDelete) {
        return
      }

      triggerDelete({
        id: Number(course.id)
      }).then(async result => {
        if (result.error) {
          return catchErrorFromGraphQL({
            error: result.error,
            page: configuration.path.courseManagement.list,
            setToast
          })
        }

        const { tenantCoursesDelete } = result.data
        if (tenantCoursesDelete.success) {
          setToast({
            open: true,
            type: 'success',
            title: t('course:detail:notification:deleteCourse')
          })
          refetch()
        }

        return
      })
    },
    [isLoadingDelete, triggerDelete, setToast, t, refetch]
  )
  const isAllFiltersEmpty = (filters: ICourseManagementFilter | undefined): boolean => {
    return (
      (filters?.levels === undefined || filters.levels.length === 0) &&
      filters?.pricing === undefined &&
      (filters?.providers === undefined || filters.providers.length === 0) &&
      (filters?.statuses === undefined || filters.statuses.length === 0)
    )
  }
  useEffect(() => {
    if (setCount !== undefined && data?.meta?.totalRowCount !== undefined) {
      setCount(data?.meta?.totalRowCount)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data?.meta?.totalRowCount])

  useEffect(() => {
    setIsDefaultFilters(isAllFiltersEmpty(filter))
    if (filter) {
      handleChangeInput(filter)
    }
  }, [filter])

  useEffect(() => {
    if (refetchMyList) {
      refetch()
      setRefetchMyList(false)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [refetchMyList])

  useEffect(() => {
    if (refetchMyDelete) {
      refetch()
      setRefetchMyDelete(false)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [refetchMyDelete])

  return (
    <>
      <div className={cn('flex flex-col', topSpace)}>
        <div className="z-10 flex h-[56px] flex-none items-center justify-between px-6 py-4">
          <div className="flex items-center">
            <p className="mr-2 text-lg font-medium text-gray-900 dark:text-gray-200">{t('course:table:heading')}</p>
            {count > 0 ? (
              <Badge radius="circular" size="md">
                {count}
              </Badge>
            ) : null}
          </div>
          <FilterCourseManagement filter={queryKey} changeFilter={changeFilter} setOpenCreateCourse={() => createCourseCallback()} />
        </div>

        {data?.meta.totalRowCount === 0 && filter?.isFilterTouched ? (
          <div style={{ minHeight: 'calc(100vh - 170px)' }} className="flex flex-1 items-center">
            <Empty
              type="empty-search"
              title={t('course:table:emptySearch:title') || ''}
              description={t('course:table:emptySearch:description') || ''}
            />
          </div>
        ) : (
          <div className={cn('flex-1 pl-6')}>
            <CourseListingTable
              queryKey={queryKey}
              isDefaultFilters={isDefaultFilters}
              // globalFilter={globalFilter}
              // setGlobalFilter={setGlobalFilter}
              // fetchPreviousPage={fetchPreviousPage}
              classNameEmpty="-ml-6"
              isFetching={isFetching}
              setOpenCreateCourse={() => createCourseCallback()}
              data={data}
              fetcher={{
                fetchPagination,
                forceChangeCurrentPage
              }}
              sorting={sorting}
              setSorting={setSorting}
              isLoadingDelete={isLoadingDelete}
              deleteCourseCallback={deleteCourseCallback}
              editCourseCallback={editCourseCallback}
              classNameTable={bulkSelectedAll || bulkValues?.length ? 'pb-[55px]' : ''}
            />
          </div>
        )}
        <Dialog
          className="min-w-[680px]"
          open={openCourse}
          isPreventAutoFocusDialog
          label={selectedCourse ? `${t('course:titles:edit')}` : `${t('course:titles:create')}`}
          onOpenChange={setOpenCourse}
        >
          <CourseForm
            onClickCancelButton={() => setOpenCourse(false)}
            onSubmit={selectedCourse ? onSubmitUpdateForm : onSubmitAddForm}
            loadingCourseCreate={loadingCourseCreate}
            loadingCourseUpdate={loadingCourseUpdate}
            defaultValue={mappingCourseForm(selectedCourse || {}) as CourseManagementForm}
          />
        </Dialog>
        <BulkActions courses={data as IPagePagination} queryOptions={{ filter: queryKey || {}, sorting }} />
      </div>
    </>
  )
}

export default withSubscriptionPlanLockFearture(
  withPermissionSettingProvider(
    {
      checkAccessPermission: canAccessSetting,
      keyModule: [PERMISSIONS_LIST.course_management.keyModule]
    },
    CourseManagementContainer
  ),
  PLAN_FEATURE_KEYS.learning_management_system as FeatureName
)
