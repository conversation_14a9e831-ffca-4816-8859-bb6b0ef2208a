import type { FC } from 'react'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'

import withFeatureSettingProvider from 'src/hoc/with-feature-setting'
import withPermissionSettingProvider from 'src/hoc/with-permission-setting'
import { Badge } from '~/core/ui/Badge'
import Empty from '~/core/ui/Empty'
import { cn } from '~/core/ui/utils'
import { accessRequisitionFeature, canAccessSetting, PERMISSIONS_LIST } from '~/core/utilities/feature-permission'

import { DESC_SORTING } from '~/lib/features/candidates/utilities/enum'
import QueryRequisitionList from '~/lib/features/requisitions/graphql/query-requisition-list'
import type { IFilterRequisition } from '~/lib/features/requisitions/types/management-page-type'
import { PLAN_FEATURE_KEYS } from '~/lib/features/settings/plans/utilities/enum'
import usePaginationGraphPage from '~/lib/hooks/use-pagination-graph-page'

import { withLayoutGrid } from '~/components/Layout/LayoutGrid'
import FilterRequisitionManagement from '~/components/Requisitions/FilterRequisitionManagement'
import RequisitionTable from '~/components/Requisitions/RequisitionTable'
import type { FeatureName } from '~/components/Subscription/subscription'
import { withSubscriptionPlanLockFearture } from '~/components/Subscription/SubscriptionPlan'
import { useClassBasedTopSpace } from '~/components/Subscription/TopSpace'

const DEFAULT_FILTER = {}

const RequisitionContainer: FC = () => {
  const { t } = useTranslation()
  const [filter, changeFilter] = useState<IFilterRequisition | undefined>(DEFAULT_FILTER)
  const [sorting, setSorting] = useState<{
    createdAt?: string
  }>({
    createdAt: DESC_SORTING
  })

  const { data, refetch, fetchPagination, forceChangeCurrentPage, isFetching } = usePaginationGraphPage({
    queryDocumentNode: QueryRequisitionList,
    queryKey: 'my-requisitions-management',
    filter: {
      ...(filter || {}),
      sorting,
      ...(!!filter?.status && filter?.status?.length > 0 ? { status: filter.status.map(item => String(item.value)) } : {}),
      departmentIds: filter?.departmentIds?.length ? filter.departmentIds?.map(item => parseInt(item.value)) : undefined,
      ...(!!filter?.ownerId?.value ? { ownerId: Number(filter.ownerId?.value) } : {})
    }
  })

  const topSpace = useClassBasedTopSpace({
    34: 'h-full',
    default: 'h-screen'
  })

  return (
    <div className={cn('flex flex-col', topSpace)}>
      <div className="flex h-[56px] flex-none items-center justify-between px-6 py-4">
        <div className="flex items-center">
          <p className="mr-2 text-lg font-medium text-gray-900 dark:text-gray-200">{t('requisitions:management:title')}</p>
          {data?.meta?.totalRowCount && data?.meta?.totalRowCount > 0 ? (
            <Badge radius="circular" size="md">
              {data?.meta.totalRowCount}
            </Badge>
          ) : null}
        </div>
        <FilterRequisitionManagement filter={filter} changeFilter={changeFilter} />
      </div>

      {data?.meta?.totalRowCount === 0 && filter?.isFilterTouched ? (
        <div className="flex flex-1 items-center">
          <Empty
            type="empty-search"
            title={t('job:emptyContent:emptyTitleNoSearchFound') || ''}
            description={t('job:emptyContent:emptyDescriptionNoSearchFound') || ''}
            buttonTitle={t('button:clearFilter') || ''}
            onClick={() => changeFilter(DEFAULT_FILTER)}
          />
        </div>
      ) : (
        <div className="flex-1 pl-6">
          <RequisitionTable
            filter={filter}
            data={data}
            fetcher={{
              fetchPagination,
              forceChangeCurrentPage
            }}
            isFetching={isFetching}
            refetch={refetch}
            sorting={sorting}
            setSorting={setSorting}
          />
        </div>
      )}
    </div>
  )
}

export default withSubscriptionPlanLockFearture(
  withFeatureSettingProvider(
    {
      checkAccessPermission: accessRequisitionFeature,
      checkUserPermission: () => true
    },
    withPermissionSettingProvider(
      {
        checkAccessPermission: canAccessSetting,
        keyModule: [PERMISSIONS_LIST.manage_requisition.keyModule, PERMISSIONS_LIST.request_requisition.keyModule]
      },
      withLayoutGrid(RequisitionContainer)
    )
  ),
  PLAN_FEATURE_KEYS.requisition as FeatureName
)
