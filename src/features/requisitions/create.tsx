import withFeatureSettingProvider from 'src/hoc/with-feature-setting'
import withPermissionSettingProvider from 'src/hoc/with-permission-setting'
import { accessRequisitionFeature, canAccessSetting, PERMISSIONS_LIST } from '~/core/utilities/feature-permission'

import { PLAN_FEATURE_KEYS } from '~/lib/features/settings/plans/utilities/enum'

import LayoutGrid from '~/components/Layout/LayoutGrid'
import RequisitionCreateView from '~/components/Requisitions/RequisitionCreateView'
import type { FeatureName } from '~/components/Subscription/subscription'
import { withSubscriptionPlanLockFearture } from '~/components/Subscription/SubscriptionPlan'

const RequisitionCreateManagementContainer = () => {
  return (
    <LayoutGrid>
      <RequisitionCreateView />
    </LayoutGrid>
  )
}

export default withSubscriptionPlanLockFearture(
  withFeatureSettingProvider(
    {
      checkAccessPermission: accessRequisitionFeature,
      checkUserPermission: () => true
    },
    withPermissionSettingProvider(
      {
        checkAccessPermission: canAccessSetting,
        keyModule: [PERMISSIONS_LIST.manage_requisition.keyModule, PERMISSIONS_LIST.request_requisition.keyModule]
      },
      RequisitionCreateManagementContainer
    )
  ),
  PLAN_FEATURE_KEYS.requisition as FeatureName
)
