'use client'

import type { FC } from 'react'
import { useCallback, useEffect, useMemo, useState } from 'react'

import { useInfinityGraphPage } from '~/lib/features/jobs/hooks/use-infinity-graph-page'
import QueryPublicRequisitionHistory from '~/lib/features/requisitions/graphql/query-public-requisition-history-list'
import usePublicRequisitionDetailHook from '~/lib/features/requisitions/hooks/use-public-requisition-detail-hook'
import { useRouterContext } from '~/lib/next/use-router-context'

import RequisitionPublicSkeleton from '~/components/RequisitionPublicDetail/RequisitionPublicSkeleton'
import RequisitionView from '~/components/RequisitionPublicDetail/RequisitionView'
import type { RejectRequisitionForm } from '~/components/Requisitions/components/RejectRequisitionModal'
import RejectRequisitionModal from '~/components/Requisitions/components/RejectRequisitionModal'

const RequisitionPublicDetailContainer: FC = () => {
  const { params, searchParams } = useRouterContext()
  const id = params?.id
  const userToken = params?.userToken
  const [openRejectModal, setOpenRejectModal] = useState<boolean>(false)
  const { requisitionDetail, fetchRequisitionDetail, updatingStatus, updateRequisitionStatus } = usePublicRequisitionDetailHook()
  const activityInfinityData = useInfinityGraphPage({
    queryDocumentNote: useMemo(() => QueryPublicRequisitionHistory, []),
    getVariable: useCallback(
      page => ({
        requisitionUuid: String(id),
        userToken: String(userToken),
        limit: 10,
        page
      }),
      [id, userToken]
    ),
    getPageAttribute: (_lastGroup, groups) => ({
      totalCount: _lastGroup?.publicRequisitionActivitiesList?.metadata?.totalCount,
      pageLength: Number(groups?.[0]?.publicRequisitionActivitiesList?.collection?.length)
    }),

    queryKey: ['requisition-activity-list', id as string, userToken as string]
  })

  const onSubmitRejectForm = useCallback(
    (data: RejectRequisitionForm) => {
      updateRequisitionStatus({
        uuid: String(id),
        userToken: String(userToken),
        status: 'rejected',
        rejectReason: data.rejectReason
      }).then(() => {
        activityInfinityData.refetch()
      })
      return Promise.resolve()
    },
    [searchParams, activityInfinityData]
  )
  useEffect(() => {
    if (id && userToken)
      fetchRequisitionDetail({
        uuid: String(id),
        userToken: String(userToken)
      })
  }, [searchParams])

  return !id || !userToken || !requisitionDetail ? (
    <RequisitionPublicSkeleton />
  ) : (
    <>
      <RequisitionView
        requisition={requisitionDetail}
        activityData={activityInfinityData}
        disableButton={updatingStatus}
        onClickRejectRequisition={() => setOpenRejectModal(true)}
        onClickApproveRequisition={() =>
          updateRequisitionStatus({
            uuid: String(id),
            userToken: String(userToken),
            status: 'approved'
          }).then(() => {
            activityInfinityData.refetch()
          })
        }
      />
      <RejectRequisitionModal
        open={openRejectModal}
        setOpen={setOpenRejectModal}
        requisitionName={requisitionDetail?.name}
        onFinish={onSubmitRejectForm}
      />
    </>
  )
}

export default RequisitionPublicDetailContainer
