import withFeatureSettingProvider from 'src/hoc/with-feature-setting'
import withPermissionSettingProvider from 'src/hoc/with-permission-setting'
import { accessRequisitionFeature, canAccessSetting, PERMISSIONS_LIST } from '~/core/utilities/feature-permission'

import { PLAN_FEATURE_KEYS } from '~/lib/features/settings/plans/utilities/enum'

import LayoutGrid from '~/components/Layout/LayoutGrid'
import RequisitionEditView from '~/components/Requisitions/RequisitionEditView'
import type { FeatureName } from '~/components/Subscription/subscription'
import { withSubscriptionPlanLockFearture } from '~/components/Subscription/SubscriptionPlan'

const RequisitionEditManagementContainer = () => {
  return (
    <LayoutGrid>
      <RequisitionEditView />
    </LayoutGrid>
  )
}

export default withSubscriptionPlanLockFearture(
  withFeatureSettingProvider(
    {
      checkAccessPermission: accessRequisitionFeature,
      checkUserPermission: () => true
    },
    withPermissionSettingProvider(
      {
        checkAccessPermission: canAccessSetting,
        keyModule: [PERMISSIONS_LIST.manage_requisition.keyModule, PERMISSIONS_LIST.request_requisition.keyModule]
      },
      RequisitionEditManagementContainer
    )
  ),
  PLAN_FEATURE_KEYS.requisition as FeatureName
)
