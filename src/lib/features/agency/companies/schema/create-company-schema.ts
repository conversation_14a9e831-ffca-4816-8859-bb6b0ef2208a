import type { TFunction } from 'next-i18next'
import { z } from 'zod'

const schemaCreateCompany = (t: TFunction) => {
  return z.object({
    name: z
      .string()
      .trim()
      .min(1, {
        message: `${t('form:requiredField')}`
      })
      .max(100, {
        message: `${t('form:field_max_number_required', { number: 100 })}`
      }),

    domain: z.string().trim().optional()
  })
}

export default schemaCreateCompany
