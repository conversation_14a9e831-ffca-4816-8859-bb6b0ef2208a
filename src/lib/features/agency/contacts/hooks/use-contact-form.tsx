import { useTranslation } from 'react-i18next'

import configuration from '~/configuration'
import type { IResponseContextResult } from '~/core/middleware/use-context-graphQL'
import useContextGraphQL from '~/core/middleware/use-context-graphQL'
import type { IPromiseSearchOption } from '~/core/ui/Select'

import QueryCompaniesList from '../../companies/graphql/query-companies-list'
import type { CompanyItemListingType } from '../../companies/types/company-detail'

const useContactForm = () => {
  const { t } = useTranslation()
  const { clientGraphQL } = useContextGraphQL()
  const fetchCompanyListOption = (params = {} as IPromiseSearchOption) =>
    new Promise<any>(resolve => {
      return clientGraphQL
        .query(QueryCompaniesList, {
          ...params
        })
        .toPromise()
        .then((result: IResponseContextResult<CompanyItemListingType>) => {
          if (result.error) {
            resolve({
              metadata: {
                totalCount: configuration.defaultAsyncLoadingOptions
              },
              collection: []
            })
          }

          const { companiesList } = result.data
          const collection = companiesList?.collection || []
          const metadata = companiesList?.metadata || { totalCount: 0 }

          const cloneData = collection.map((item: CompanyItemListingType) => {
            const locationLists = item?.permittedFields?.companyLocations?.value || []
            const labelLocation =
              locationLists.length > 1
                ? `${locationLists.length} ${t('label:locations')}`
                : locationLists.length === 1
                  ? [locationLists[0]?.state, locationLists[0]?.country].filter(item => !!item).join(', ')
                  : '-'

            return {
              value: item.id,
              logoVariants: item.logoVariants,
              avatarVariants: item.logoVariants,
              supportingObj: {
                name: item?.permittedFields?.name?.value,
                description: labelLocation
              }
            }
          })
          return resolve({ metadata, collection: cloneData })
        })
    })

  return {
    fetchCompanyListOption
  }
}
export default useContactForm
