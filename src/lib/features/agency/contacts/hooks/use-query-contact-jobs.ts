'use client'

import { useEffect, useMemo } from 'react'

import configuration from '~/configuration'
import { useGraphQLRequest } from '~/core/hooks/use-graphQL'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'

import { useInfinityQuerySearch } from '~/lib/hooks/use-infinity-query-search'
import useBoundStore from '~/lib/store'

import QueryContactsJobsList from '../graphql/query-contact-jobs-list'
import type { ContactJobResponseType } from '../types'

export function useQueryContactJobs({
  variables
}: {
  variables: {
    contactId: number
    page: number
    limit: number
    status?: string[]
  }
}) {
  const { user } = useBoundStore()
  const clientGraphQL = useGraphQLRequest({ language: user?.language })

  const fetchData = async (params = {}) => {
    return clientGraphQL
      .query(QueryContactsJobsList, {
        ...params
      })
      .toPromise()
      .then(result => {
        if (result.error) {
          catchErrorFromGraphQL({
            error: result.error
          })

          return {
            data: [],
            meta: {
              totalRowCount: 0,
              pageSize: configuration.defaultPageSize
            }
          }
        }
        const collection = result.data?.contactJobsList?.collection || []
        const metadata = result.data?.contactJobsList?.metadata
        return {
          data: collection,
          meta: {
            totalRowCount: metadata?.totalCount,
            pageSize: configuration.defaultPageSize
          }
        }
      })
  }
  const { data, fetchNextPage, refetch, hasNextPage, isFetching, isLoading, updateLocalRecord } = useInfinityQuerySearch<ContactJobResponseType>({
    configuration,
    fetchData,
    queryKey: variables
  })
  useEffect(() => {
    refetch()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [variables.contactId])

  return {
    trigger: refetch,
    isLoading,
    fetchNextPage,
    hasNextPage,
    isFetching,
    data: useMemo(() => data?.pages.reduce<Array<ContactJobResponseType>>((all, page) => [...all, ...page.data], []), [data]),
    totalRowCount: data?.pages?.[0]?.meta?.totalRowCount,
    updateLocalRecord
  }
}
