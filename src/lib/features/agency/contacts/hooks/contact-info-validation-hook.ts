import { useRef } from 'react'
import type { <PERSON>P<PERSON>, UseFormTrigger } from 'react-hook-form'

import type { IContactDetailType } from '../types'

const useContactInfoValidationHook = () => {
  const touchingFieldRef = useRef<FieldPath<IContactDetailType>>(undefined)
  const submitPartialField = (
    fieldName: FieldPath<IContactDetailType>,
    validate: UseFormTrigger<IContactDetailType>,
    submit?: () => Promise<any>
  ) => {
    if (touchingFieldRef) {
      touchingFieldRef.current = fieldName
    }

    return validate(fieldName).then(test => {
      if (test) {
        return submit
          ? submit().then(result => {
              return test && !!result
            })
          : Promise.resolve(false)
      }
      return Promise.resolve(false)
    })
  }

  return {
    touchingFieldRef,
    submitPartialField
  }
}

export default useContactInfoValidationHook
