import type { TFunction } from 'i18next'
import { z } from 'zod'

import { regexEmailValidation } from '~/core/utilities/common'

export const schemaContactForm = (t: TFunction) => {
  return z.object({
    firstName: z
      .string()
      .trim()
      .min(1, { message: `${t('form:requiredField')}` })
      .max(100, {
        message: `${t('form:field_max_number_required', { number: 100 })}`
      }),
    lastName: z
      .string()
      .trim()
      .max(100, {
        message: `${t('form:field_max_number_required', { number: 100 })}`
      })
      .nullish()
      .optional(),
    email: z
      .string()
      .trim()
      .refine(value => value.trim() !== '', {
        message: `${t('form:requiredField')}`
      })
      .refine(value => regexEmailValidation.test(value), {
        message: `${t('form:invalid_email')}`
      }),
    phoneNumber: z
      .string()
      .trim()
      .max(15, {
        message: `${t('form:field_max_number_required', { number: 15 })}`
      })
      .optional()
      .nullable(),
    countryCode: z.string().optional(),
    title: z
      .string()
      .trim()
      .max(100, {
        message: `${t('form:field_max_number_required', { number: 100 })}`
      })
      .optional()
      .nullable(),
    companyIds: z.object(
      {
        id: z.any(),
        supportingObj: z.any(),
        value: z.any()
      },
      {
        required_error: `${t('form:requiredField')}`
      }
    )
  })
}
