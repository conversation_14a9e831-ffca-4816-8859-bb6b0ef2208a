import type { TFunction } from 'i18next'
import { z } from 'zod'

export const schemaUpdateContactName = (t: TFunction) => {
  return z.object({
    firstName: z
      .string()
      .trim()
      .min(1, { message: `${t('form:requiredField')}` })
      .max(100, {
        message: `${t('form:field_max_number_required', { number: 100 })}`
      }),
    lastName: z
      .string()
      .trim()
      .max(100, {
        message: `${t('form:field_max_number_required', { number: 100 })}`
      })
      .nullish()
      .optional()
  })
}
