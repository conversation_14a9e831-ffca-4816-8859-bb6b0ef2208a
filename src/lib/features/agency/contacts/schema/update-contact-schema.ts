import type { TFunction } from 'i18next'
import { z } from 'zod'

import { regexEmailValidation } from '~/core/utilities/common'

import { schemaCustomField } from '~/lib/features/settings/profile-fields/schema/custom-field-schema'

export const schemaUpdateContact = (t: TFunction) => {
  return z
    .object({
      email: z
        .string()
        .trim()
        .refine(value => value.trim() !== '', {
          message: `${t('form:requiredField')}`
        })
        .refine(value => regexEmailValidation.test(value), {
          message: `${t('form:invalid_email')}`
        }),
      avatar: z.any(),
      phoneNumber: z
        .string()
        .trim()
        .max(15, {
          message: `${t('form:field_max_number_required', { number: 15 })}`
        })
        .optional()
        .nullable(),
      title: z
        .string()
        .max(100, {
          message: `${t('form:field_max_number_required', { number: 100 })}`
        })
        .optional(),
      links: z
        .object({
          links: z.array(z.string()).nullish(),
          _destroy: z.boolean()
        })
        .optional()
    })
    .and(schemaCustomField(t))
}
