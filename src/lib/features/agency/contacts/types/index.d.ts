import type { ILogoAndAvatarVariants } from '~/core/@types/global'
import type { ISelectOption } from '~/core/ui/Select'

import type { CustomFieldFormType, CustomFieldResponseItem } from '~/lib/features/settings/profile-fields/types/custom-field'

import type { ICompanyPermittedFields } from '../../companies/types'

export interface IContactFilter {
  jobId?: { value?: number }
  stageTypeId?: { value?: number }
  page?: number
  search?: string
  isFilterTouched?: boolean
  companyIds?: ISelectOption
  status?: ISelectOption
  statusDescription?: string
}
export interface IContactType {
  id: string
  tenantId: number
  firstName: string
  lastName: string
  title: string
  email: string
  phoneNumber: string
  contactableId: number
  contactableType: string
  state: string
  status: string
  statusDescription: string
  companies?: {
    id: string
    permittedFields?: ICompanyPermittedFields
    viewableCompany?: boolean
    viewableJob?: boolean
  }[]
  createdAt: string
  updatedAt: string
  avatarVariants: ILogoAndAvatarVariants
  permittedFields?: IContactPermittedFields
}
export type IContactDetailType = {
  id?: number
  avatar?: Blob
  firstName?: string
  lastName?: string
  fullName?: string
  email?: string
  avatarVariants?: ILogoAndAvatarVariants
  phoneNumber?: string
  title?: string
  state?: string
  status?: string
  statusDescription?: string
  links?: { links?: Array<string>; _destroy?: boolean }
  createdAt?: string
  updatedAt?: string
  contactableId?: number
  contactableType?: string
  companies?: {
    id: number
    name: string
    logoVariants: ILogoAndAvatarVariants
    links: { links?: Array<string>; _destroy?: boolean }
    domain: string
  }
  countryCode?: string
} & CustomFieldFormType

export type IContactDetailResponseType = Partial<{
  id: number
  firstName: string
  lastName: string
  email: string
  avatarVariants: ILogoAndAvatarVariants
  phoneNumber: string
  title: string
  state: string
  status: string
  statusDescription: string
  links: { [key: string]: string[] }
  createdAt: string
  updatedAt: string
  createdBy: {
    fullName: string
    defaultColour: srting
    avatarVariants: ILogoAndAvatarVariants
  }
  updatedBy: {
    fullName: string
    defaultColour: string
    avatarVariants: ILogoAndAvatarVariants
  }
  contactableId: number
  contactableType: string
  companies: {
    id: number
    name: string
    logoVariants: ILogoAndAvatarVariants
    links: { links?: Array<string>; _destroy?: boolean }
    domain: string
    viewableCompany: boolean
  }
  permittedFields?: IContactPermittedFields
  customFields?: CustomFieldResponseItem[]
}>
export type ICompanyContactDetailType = {
  id: number
  permittedFields?: ICompanyPermittedFields
  logoVariants: ILogoAndAvatarVariants
  viewableCompany: boolean
}

export type ContactJobResponseType = {
  id: string
  title: string
  jobLocations?: Array<{
    id: string
    state: string
    country: string
  }>
  company: {
    id: string
    permittedFields?: ICompanyPermittedFields
    viewableCompany: boolean
  }
  viewableJob: boolean
  status: string
  statusDescription: string
  openedAt: string
}
export interface IContactPermittedFields {
  email: {
    value?: string
    visibility_changeable?: boolean
  }
  firstName: {
    value?: string
    visibility_changeable?: boolean
  }
  lastName: {
    value?: string
    visibility_changeable?: boolean
  }
  phoneNumber: {
    value?: string
    visibility_changeable?: boolean
  }
  title: {
    value?: string
    visibility_changeable?: boolean
  }
  links: {
    value?: { [key: string]: string[] }
    roles?: string[]
    visibility_changeable?: boolean
    client_user_visibility?: boolean
  }
}
