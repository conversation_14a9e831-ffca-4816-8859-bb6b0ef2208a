import type { ISelectOption } from '~/core/ui/Select'
import { uuidV4 } from '~/core/ui/utils'

import { formatInitialValueCustomField } from '~/lib/features/settings/profile-fields/mapping/custom-field-mapping'

import type { IContactDetailResponseType, IContactDetailType } from '../types'

export const mappingContactDetailData = ({
  data,
  companySizeOptions
}: {
  data?: IContactDetailResponseType
  companySizeOptions?: ISelectOption[]
}): IContactDetailType => {
  const permittedFields = data?.permittedFields
  const fullNameContact = [permittedFields?.firstName?.value || '', permittedFields?.lastName?.value || ''].filter(name => !!name).join(' ')
  return {
    ...data,
    fullName: fullNameContact,
    firstName: permittedFields?.firstName?.value,
    lastName: permittedFields?.lastName?.value,
    customFields: formatInitialValueCustomField(data?.customFields),
    email: permittedFields?.email?.value,
    links: permittedFields?.links?.value,
    phoneNumber: permittedFields?.phoneNumber?.value,
    title: permittedFields?.title?.value
  }
}
export const formatUpdateContactData = ({
  contactId,
  submittingField,
  data
}: {
  contactId: number
  submittingField?: keyof IContactDetailType
  data: IContactDetailType
}): IContactDetailType => {
  return {
    id: contactId,

    ...(!!data.email && submittingField === 'email'
      ? {
          email: data.email
        }
      : {}),
    ...(submittingField === 'phoneNumber'
      ? {
          phoneNumber: !!data.phoneNumber && data.phoneNumber !== data.countryCode ? data.phoneNumber : ''
        }
      : {}),

    ...(submittingField === 'title' ? { title: data.title || '' } : {}),
    ...(!!data.links
      ? {
          links: {
            links: data.links?.links || [],
            ...data.links,
            _destroy: !!data.links?._destroy
          }
        }
      : {}),
    ...(!!data?.avatar && submittingField === 'avatar'
      ? {
          avatar: new File([new Blob([data.avatar || ''])], `avatar-${uuidV4()}.jpeg`)
        }
      : {})
  }
}
