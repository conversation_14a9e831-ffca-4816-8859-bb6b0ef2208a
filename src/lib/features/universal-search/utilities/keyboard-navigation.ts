import type { FlatResultItem } from '../types'

export const scrollToItem = (modalContent: Element, index: number, totalItems: number) => {
  const item = document.getElementById(`search-item-${index}`)
  if (!item) return

  const itemRect = item.getBoundingClientRect()
  const containerRect = modalContent.getBoundingClientRect()

  if (index === 0) {
    modalContent.scrollTo({ top: 0, behavior: 'smooth' })
  } else if (index === totalItems - 1) {
    modalContent.scrollTo({
      top: modalContent.scrollHeight,
      behavior: 'smooth'
    })
  } else {
    const isItemBelow = itemRect.bottom > containerRect.bottom
    const isItemAbove = itemRect.top < containerRect.top

    if (isItemBelow) {
      modalContent.scrollTo({
        top: modalContent.scrollTop + (itemRect.bottom - containerRect.bottom),
        behavior: 'smooth'
      })
    } else if (isItemAbove) {
      modalContent.scrollTo({
        top: modalContent.scrollTop + (itemRect.top - containerRect.top),
        behavior: 'smooth'
      })
    }
  }
}

export const handleKeyboardNavigation = (
  e: React.KeyboardEvent,
  modalContent: Element | null,
  flatResults: FlatResultItem[],
  selectedIndex: number,
  setSelectedIndex: (index: number) => void,
  onClose: () => void,
  onSelect: (item: FlatResultItem) => void,
  setIsKeyboardNavigation: (value: boolean) => void
) => {
  if (e.key === 'Escape') onClose()
  if (!modalContent || !flatResults.length) return
  if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
    setIsKeyboardNavigation(true)
  }

  switch (e.key) {
    case 'ArrowDown':
      e.preventDefault()
      if (selectedIndex === flatResults.length - 1) {
        setSelectedIndex(0)
        scrollToItem(modalContent, 0, flatResults.length)
      } else {
        const nextIndex = selectedIndex + 1
        setSelectedIndex(nextIndex)
        scrollToItem(modalContent, nextIndex, flatResults.length)
      }
      break

    case 'ArrowUp':
      e.preventDefault()
      if (selectedIndex === 0) {
        const lastIndex = flatResults.length - 1
        setSelectedIndex(lastIndex)
        scrollToItem(modalContent, lastIndex, flatResults.length)
      } else {
        const prevIndex = selectedIndex - 1
        setSelectedIndex(prevIndex)
        scrollToItem(modalContent, prevIndex, flatResults.length)
      }
      break

    case 'Enter':
      if (flatResults[selectedIndex]) {
        onSelect(flatResults[selectedIndex])
      }
      break

    case 'Escape':
      onClose()
      break
  }
}
