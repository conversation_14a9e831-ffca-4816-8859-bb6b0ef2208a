'use client'

import { use<PERSON>allback, useEffect, useMemo, useState } from 'react'

import useContextGraphQL from '~/core/middleware/use-context-graphQL'
import { debounce } from '~/core/ui/utils'

import QueryUniversalSearch from '../graphql/query-universal-search'
import type { SearchResponse, UniversalResult } from '../types'

const useUniversalSearch = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [results, setResults] = useState<UniversalResult>()
  const [selectedIndex, setSelectedIndex] = useState(0)
  const [isLoading, setIsLoading] = useState(false)
  const { clientGraphQL } = useContextGraphQL()
  const [isKeyboardNavigation, setIsKeyboardNavigation] = useState(false)
  // useEffect(() => {
  //   // Thêm class no-scroll vào body khi component mount
  //   document.body.style.overflow = 'hidden'
  //   // Cleanup: remove class khi component unmount
  //   return () => {
  //     document.body.style.overflow = 'unset'
  //   }
  // }, [])

  const flatResults = useMemo(() => {
    if (!results) return []

    return Object.entries(results).flatMap(([sectionKey, section]) =>
      section.map((item, index) => ({
        ...item,
        sectionKey
      }))
    )
  }, [results])

  const fetchSearchResults = async (search: string): Promise<UniversalResult | null> => {
    try {
      const result: SearchResponse = await clientGraphQL.query(QueryUniversalSearch, { search }).toPromise()

      if (result.error) {
        return null
      }

      return result.data.universalSearch
    } catch (error) {
      return null
    }
  }

  const handleSearch = async (search: string) => {
    if (search.length < 3) return setResults(undefined)

    setIsLoading(true)
    try {
      const searchResults = await fetchSearchResults(search)
      setResults(searchResults || { jobs: [], profiles: [], companies: [] })
    } finally {
      setIsLoading(false)
    }
  }

  const debouncedSearch = useCallback(debounce(handleSearch, 300), [])

  return {
    searchTerm,
    setSearchTerm,
    results,
    flatResults,
    selectedIndex,
    setSelectedIndex,
    isLoading,
    debouncedSearch,
    setIsKeyboardNavigation,
    isKeyboardNavigation
  }
}

export default useUniversalSearch
