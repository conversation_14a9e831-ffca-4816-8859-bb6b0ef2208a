import type { TFunction } from 'i18next'
import { z } from 'zod'

const schemaDisqualifyReasonsForm = (t: TFunction) => {
  return z.object({
    label: z
      .string()
      .trim()
      .min(1, {
        message: `${t('form:requiredField')}`
      })
      .max(250, {
        message: `${t('form:field_max_number_required', { number: 250 })}`
      }),
    rejectedKind: z.string().min(1, {
      message: `${t('form:requiredField')}`
    })
  })
}

export default schemaDisqualifyReasonsForm
