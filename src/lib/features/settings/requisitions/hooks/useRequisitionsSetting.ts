'use client'

import { useCallback, useEffect, useState } from 'react'

import useContextGraphQL from '~/core/middleware/use-context-graphQL'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'

import QueryTenantRequisitionSettingShow from '../graphql/query-tenant-requisitions-setting'
import type { IRequisitionsSettingType } from '../types'

const useRequisitionSetting = ({ suspend }: { suspend?: boolean } = {}) => {
  const { clientGraphQL } = useContextGraphQL()
  const [dataRequisitions, setDataRequisitions] = useState<IRequisitionsSettingType>()

  const fetchRequisitionSettingShow = useCallback(() => {
    return clientGraphQL
      .query(QueryTenantRequisitionSettingShow)
      .then((result: { error: { graphQLErrors: Array<object> }; data: { tenantRequisitionSettingShow: IRequisitionsSettingType } }) => {
        if (result.error) {
          return catchErrorFromGraphQL({
            error: result.error
          })
        }
        const { tenantRequisitionSettingShow } = result.data || []
        setDataRequisitions(tenantRequisitionSettingShow)
        return
      })
  }, [clientGraphQL])

  useEffect(() => {
    !suspend && fetchRequisitionSettingShow()
  }, [suspend])

  return { dataRequisitions, fetchRequisitionSettingShow }
}
export default useRequisitionSetting
