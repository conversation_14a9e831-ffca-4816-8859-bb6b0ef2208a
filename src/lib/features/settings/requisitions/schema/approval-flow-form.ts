import type { TFunction } from 'i18next'
import { z } from 'zod'

const schemaApprovalFlowForm = (t: TFunction) => {
  return z.object({
    name: z
      .string({
        required_error: `${t('form:requiredField')}`
      })
      .min(1, {
        message: `${t('form:requiredField')}`
      })
      .max(250, {
        message: `${t('form:field_max_number_required', { number: 250 })}`
      })
      .refine(value => value.trim() !== '', {
        message: `${t('form:requiredField')}`
      }),
    approvalFlow: z.object({
      data: z
        .array(
          z.object({
            id: z.string(),
            minimumApproval: z.string(),
            approvers: z.array(
              z.object({
                value: z.string()
              })
            )
          })
        )
        .refine(
          data => {
            if (data.some(item => item.approvers.length === 0)) {
              return false
            }
            return true
          },
          {
            message: `${t('form:please_select_at_least_one_approver')}`
          }
        )
        .refine(
          data => {
            if (data.length === 0) {
              return false
            }
            return true
          },
          {
            message: `${t('form:requiredField')}`
          }
        ),
      dataDeleted: z
        .array(
          z.object({
            id: z.string(),
            minimumApproval: z.string(),
            approvers: z.array(
              z.object({
                value: z.string()
              })
            )
          })
        )
        .optional()
    })
  })
}

export default schemaApprovalFlowForm
