import type { ISelectOption } from '~/core/ui/Select'

export type IRequisitionsSettingType = {
  id: string
  values: {
    enabling: boolean
  }
}

export type IApprovalFlowItemType = {
  id: string
  approvers: ISelectOption[]
  minimumApproval: string
  _destroy?: boolean
}

export type IApprovalFlowType = {
  name?: string
  approvalFlow?: {
    data?: IApprovalFlowItemType[]
    dataDeleted?: IApprovalFlowItemType[]
  }
}
