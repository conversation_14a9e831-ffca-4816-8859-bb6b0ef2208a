import type { TFunction } from 'i18next'
import { z } from 'zod'

const schemaTurnOnSecuritySettingForm = (t: TFunction) => {
  return z.object({
    clientId: z
      .string({
        required_error: `${t('form:requiredField')}`
      })
      .min(1, {
        message: `${t('form:requiredField')}`
      })
      .max(250, {
        message: `${t('form:field_max_number_required', { number: 250 })}`
      })
      .refine(value => value.trim() !== '', {
        message: `${t('form:requiredField')}`
      }),
    tenantId: z
      .string({
        required_error: `${t('form:requiredField')}`
      })
      .min(1, {
        message: `${t('form:requiredField')}`
      })
      .max(250, {
        message: `${t('form:field_max_number_required', { number: 250 })}`
      })
      .refine(value => value.trim() !== '', {
        message: `${t('form:requiredField')}`
      }),
    clientSecret: z
      .string({
        required_error: `${t('form:requiredField')}`
      })
      .min(1, {
        message: `${t('form:requiredField')}`
      })
      .max(250, {
        message: `${t('form:field_max_number_required', { number: 250 })}`
      })
      .refine(value => value.trim() !== '', {
        message: `${t('form:requiredField')}`
      })
  })
}

const schemaUpdateSecuritySettingForm = (t: TFunction) => {
  return z.object({
    clientSecret: z
      .string({
        required_error: `${t('form:requiredField')}`
      })
      .min(1, {
        message: `${t('form:requiredField')}`
      })
      .max(250, {
        message: `${t('form:field_max_number_required', { number: 250 })}`
      })
      .refine(value => value.trim() !== '', {
        message: `${t('form:requiredField')}`
      })
  })
}

const schemaUpdateIPs = (t: TFunction) => {
  return z.object({
    whitelistIps: z.array(
      z.object({
        value: z.string(),
        supportingObj: z.object({ name: z.string() })
      })
    )
  })
}
export { schemaTurnOnSecuritySettingForm, schemaUpdateSecuritySettingForm, schemaUpdateIPs }
