import type { ISelectOption } from '~/core/ui/Select'

import { IJobStages } from '~/lib/features/jobs/types'

export interface ISecuritySettingSettingFormParam {
  enabling: boolean
  provider: string
  appEnabling?: boolean
  tenantId?: string
  clientId?: string
  clientSecret?: string
  redirectUri?: string
  idleTime?: number
}

export interface ISecuritySetting {
  idleTime?: number
  enabling?: boolean
  ssoApps?: {
    [key: string]: {
      appEnabling?: boolean
      tenantId?: string
      clientId?: string
      clientSecret?: string
      redirectUri?: string
    }
  }
  whitelist_ips?: Array[string]
}

export interface ISecurityAddIPsFormParam {
  whitelistIps?: Array<ISelectOption>
}
