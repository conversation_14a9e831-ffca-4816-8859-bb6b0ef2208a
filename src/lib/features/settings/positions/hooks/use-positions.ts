import { useTranslation } from 'react-i18next'

import configuration from '~/configuration'
import type { IPromiseSearchOption } from '~/core/@types/global'
import type { IResponseContextResult } from '~/core/middleware/use-context-graphQL'
import useContextGraphQL from '~/core/middleware/use-context-graphQL'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'

import QuerySkillsPosition from '../graphql/query-skills-position'
import type { SkillsType } from '../type'

const usePositons = () => {
  const { t, i18n } = useTranslation()
  const { clientGraphQL } = useContextGraphQL()

  const promiseSkillsOptions = (params = {} as IPromiseSearchOption) =>
    new Promise<any>(resolve => {
      clientGraphQL
        .query(QuerySkillsPosition, params)
        .toPromise()
        .then((result: IResponseContextResult<SkillsType>) => {
          if (result.error) {
            catchErrorFromGraphQL({
              error: result.error
            })
            resolve({
              metadata: {
                totalCount: configuration.defaultAsyncLoadingOptions
              },
              collection: []
            })
          }

          const { skillsList } = result?.data
          const collection = skillsList?.collection || []
          const metadata = skillsList?.metadata || { totalCount: 0 }

          const cloneData = collection.map((item: SkillsType) => {
            let skill: {
              value: string
              supportingObj: { name: string; description?: string }
            } = {
              value: item.name,
              supportingObj: {
                name: item.name
              }
            }
            if (item?.similar && item?.similar.length > 0) {
              const equivalentSkills = item?.similar
                .filter(skill => skill.toLowerCase().trim().includes(params.search.toLowerCase().trim()))
                .join(', ')
              skill = {
                ...skill,
                supportingObj: {
                  ...skill.supportingObj,
                  description: equivalentSkills ? equivalentSkills : ''
                }
              }
            }

            return skill
          })

          return resolve({ metadata, collection: cloneData })
        })
    })

  return {
    promiseSkillsOptions
  }
}

export default usePositons
