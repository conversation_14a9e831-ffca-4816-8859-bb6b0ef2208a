import { useTranslation } from 'react-i18next'

import type { IDotColorProps } from '~/core/ui/Dot'

import { POSITION_DOT_STATUS, POSITION_STATUS_ENUM } from '../utilities/enum'

const usePositionOptions = () => {
  const { t } = useTranslation()
  const optionsStatusPosition = [
    {
      value: POSITION_STATUS_ENUM.active,
      supportingObj: {
        name: `${t(`settings:positions.positionStatus.${POSITION_STATUS_ENUM.active}`)}`
      },
      dot: POSITION_DOT_STATUS(POSITION_STATUS_ENUM.active) as IDotColorProps
    },
    {
      value: POSITION_STATUS_ENUM.draft,
      supportingObj: {
        name: `${t(`settings:positions.positionStatus.${POSITION_STATUS_ENUM.draft}`)}`
      },
      dot: POSITION_DOT_STATUS(POSITION_STATUS_ENUM.draft) as IDotColorProps
    },
    {
      value: POSITION_STATUS_ENUM.archived,
      supportingObj: {
        name: `${t(`settings:positions.positionStatus.${POSITION_STATUS_ENUM.archived}`)}`
      },
      dot: POSITION_DOT_STATUS(POSITION_STATUS_ENUM.archived) as IDotColorProps
    }
  ]
  return {
    optionsStatusPosition
  }
}

export default usePositionOptions
