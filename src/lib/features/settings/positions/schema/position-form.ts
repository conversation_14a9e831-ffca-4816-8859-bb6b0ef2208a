import type { TFunction } from 'i18next'
import { z } from 'zod'

import { removeHTMLTags } from '~/core/utilities/common'

const schemaPositionForm = (t: TFunction) => {
  return z.object({
    name: z
      .string({
        required_error: `${t('form:requiredField')}`
      })
      .trim()
      .min(1, {
        message: `${t('form:requiredField')}`
      })
      .max(100, {
        message: `${t('form:field_max_number_required', { number: 100 })}`
      }),
    description: z
      .string()
      .nullish()
      .optional()
      .refine(
        description => {
          return removeHTMLTags(description || '').length <= 10000
        },
        {
          message: `${t('form:field_max_number_required', { number: 10000 })}`
        }
      ),

    skills: z
      .array(z.string())
      .min(1, {
        message: `${t('form:requiredField')}`
      })
      .nullish()
      .refine(value => !!value, {
        message: `${t('form:requiredField')}`
      }),
    status: z.string().optional()
  })
}
export default schemaPositionForm
