import { DEFAULT_PAGE_SIZE } from '~/core/constants/enum'
import type { ISelectOption } from '~/core/ui/Select'

import type { QueryPositionProps } from '../type'

export const mappingPositionSearchGraphQL = (pageParam: QueryPositionProps) => ({
  ...pageParam,
  limit: DEFAULT_PAGE_SIZE,
  search: pageParam.search || '',
  statuses: pageParam.statuses?.length ? pageParam.statuses.map((item: ISelectOption) => String(item.value)) : []
})
