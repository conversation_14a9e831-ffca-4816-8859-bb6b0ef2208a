import type { IPositionAIWriter, IPositionForm } from '../type'

export const mappingGenerateDescGraphQL = (position: IPositionForm, textGenerate: string, valueAIWriter: IPositionAIWriter) => {
  return {
    title: position.name,
    description: textGenerate,
    toneType: valueAIWriter.toneType,
    skills: (position.skills || []).length > 0 ? position.skills || [] : undefined,
    language: valueAIWriter.language
  }
}
