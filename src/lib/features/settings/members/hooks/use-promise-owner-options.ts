import { useState } from 'react'

import configuration from '~/configuration'
import type { IPromiseSearchOption } from '~/core/@types/global'
import { AGENCY_TENANT } from '~/core/constants/enum'
import type { IResponseContextResult } from '~/core/middleware/use-context-graphQL'
import useContextGraphQL from '~/core/middleware/use-context-graphQL'

import useDetectCompanyWithKind from '~/lib/hooks/use-detect-company-with-kind'

import QueryTenantMembers from '../graphql/query-tenant-members'
import QueryTenantMembersAgency from '../graphql/query-tenant-members-agency'
import type { IMember } from '../types'

const usePromiseOwnerOptions = (configParams?: {
  jobOwnable?: boolean
  requisitionApprovers?: boolean
  userReqPermisisons?: boolean
  avatarSize?: string
  disabledOption?: Array<string>
  tooltipDisableOption?: string
}) => {
  const { clientGraphQL } = useContextGraphQL()
  const { isCompanyKind } = useDetectCompanyWithKind({ kind: AGENCY_TENANT })

  const [filter, setFilter] = useState<{
    jobOwnable?: boolean
    recruiterIds?: number[]
    hiringMemberIds?: number[]
    avatarSize?: string
  }>({
    jobOwnable: true,
    recruiterIds: undefined,
    hiringMemberIds: undefined,
    avatarSize: undefined
  })

  const promiseMembersOwnerOptions = (params = {} as IPromiseSearchOption) =>
    new Promise<any>(resolve => {
      return clientGraphQL
        .query(isCompanyKind ? QueryTenantMembersAgency : QueryTenantMembers, {
          ...params,
          ...(configParams ? configParams : filter)
        })
        .toPromise()
        .then((result: IResponseContextResult<IMember>) => {
          if (result.error) {
            resolve({
              metadata: {
                totalCount: configuration.defaultAsyncLoadingOptions
              },
              collection: []
            })
          }

          const { tenantMembers } = result.data
          const collection = tenantMembers?.collection || []
          const metadata = tenantMembers?.metadata || { totalCount: 0 }

          const cloneData = collection.map((item: IMember) => {
            return {
              value: item.id,
              avatar: item.avatarVariants?.thumb?.url,
              avatarVariants: item.avatarVariants,
              ...(configParams?.avatarSize
                ? {
                    avatarSize: configParams?.avatarSize
                  }
                : filter?.avatarSize
                  ? {
                      avatarSize: filter?.avatarSize
                    }
                  : {}),
              ...(configParams?.tooltipDisableOption
                ? {
                    tooltipDisabled: configParams.tooltipDisableOption
                  }
                : {}),
              supportingObj: {
                name: item.fullName,
                description: item.email,
                defaultColour: item.defaultColour,
                helpName: item.roles?.[0]?.name
              },
              ...(configParams?.disabledOption && item.id
                ? {
                    disabled: configParams.disabledOption.includes(item.id)
                  }
                : {})
            }
          })

          return resolve({ metadata, collection: cloneData })
        })
    })
  return { filter, setFilter, promiseMembersOwnerOptions }
}
export default usePromiseOwnerOptions
