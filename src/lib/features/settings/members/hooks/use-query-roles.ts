import type { AnyVariables, TypedDocumentNode } from 'urql'

import useQueryGraphQL from '~/core/middleware/use-query-graphQL'
import { capitalizeFirstLetter } from '~/core/utilities/common'

export function useQueryRoles({
  query,
  variables,
  shouldPause = true
}: {
  query: TypedDocumentNode<
    {
      roles: {
        collection: Array<{
          id: string
          name: string
          code: string
        }>
        metadata: {
          totalCount: number
        }
      }
    },
    AnyVariables
  >
  variables: object
  shouldPause?: boolean
}) {
  const {
    trigger,
    isLoading,
    data: response,
    error
  } = useQueryGraphQL({
    query,
    variables,
    shouldPause
  })
  const data = response?.roles?.collection || []

  return {
    trigger,
    isLoading,
    data: data.map(item => ({
      id: String(item.code),
      value: String(item.id),
      supportingObj: {
        name: capitalizeFirstLetter(String(item.name))
      }
    })),
    error
  }
}
