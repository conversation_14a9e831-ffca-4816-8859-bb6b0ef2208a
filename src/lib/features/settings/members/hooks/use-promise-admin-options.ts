import configuration from '~/configuration'
import type { IPromiseSearchOption } from '~/core/@types/global'
import type { IResponseContextResult } from '~/core/middleware/use-context-graphQL'
import useContextGraphQL from '~/core/middleware/use-context-graphQL'

import QueryTenantMembersWithOnlyAdmin from '../graphql/query-tenant-with-only-admin'
import type { IMember } from '../types'

const usePromiseAdminOptions = () => {
  const { clientGraphQL } = useContextGraphQL()
  const promiseMembersOnlyAdminOptions = (params = {} as IPromiseSearchOption) =>
    new Promise<any>(resolve => {
      return clientGraphQL
        .query(QueryTenantMembersWithOnlyAdmin, {
          onlyAdmins: true,
          excludeOwner: true,
          ...params
        })
        .toPromise()
        .then((result: IResponseContextResult<IMember>) => {
          if (result.error) {
            resolve({
              metadata: {
                totalCount: configuration.defaultAsyncLoadingOptions
              },
              collection: []
            })
          }

          const { tenantMembers } = result.data
          const collection = tenantMembers?.collection || []
          const metadata = tenantMembers?.metadata || { totalCount: 0 }

          const cloneData = collection.map((item: IMember) => {
            return {
              value: item.id,
              avatar: item.avatarVariants?.thumb?.url,
              avatarVariants: item.avatarVariants,
              supportingObj: {
                name: item.fullName,
                description: item.email,
                defaultColour: item.defaultColour,
                helpName: item.roles?.[0]?.name
              }
            }
          })

          return resolve({ metadata, collection: cloneData })
        })
    })
  return { promiseMembersOnlyAdminOptions }
}
export default usePromiseAdminOptions
