import configuration from '~/configuration'
import type { IPromiseSearchOption } from '~/core/@types/global'
import { AGENCY_TENANT } from '~/core/constants/enum'
import type { IResponseContextResult } from '~/core/middleware/use-context-graphQL'
import useContextGraphQL from '~/core/middleware/use-context-graphQL'

import useDetectCompanyWithKind from '~/lib/hooks/use-detect-company-with-kind'

import QueryTenantMembers from '../graphql/query-tenant-members'
import QueryTenantMembersAgency from '../graphql/query-tenant-members-agency'
import type { IMember } from '../types'

const usePromiseOwnerOptionsFetchAll = () => {
  const { clientGraphQL } = useContextGraphQL()
  const { isCompanyKind } = useDetectCompanyWithKind({ kind: AGENCY_TENANT })

  const promiseMembersOwnerOptionsFetchAll = (params = {} as IPromiseSearchOption) =>
    new Promise<any>(resolve => {
      clientGraphQL
        .query(isCompanyKind ? QueryTenantMembersAgency : QueryTenantMembers, {
          ...params
        })
        .toPromise()
        .then((result: IResponseContextResult<IMember>) => {
          if (result.error) {
            resolve({
              metadata: {
                totalCount: configuration.defaultAsyncLoadingOptions
              },
              collection: []
            })
          }

          const { tenantMembers } = result.data
          const collection = tenantMembers?.collection || []
          const metadata = tenantMembers?.metadata || { totalCount: 0 }

          const cloneData = collection.map((item: IMember) => {
            return {
              value: item.id,
              avatar: item.avatarVariants?.thumb?.url,
              avatarVariants: item.avatarVariants,
              supportingObj: {
                name: item.fullName,
                description: item.email,
                defaultColour: item.defaultColour
              }
            }
          })

          return resolve({ metadata, collection: cloneData })
        })
    })
  return promiseMembersOwnerOptionsFetchAll
}
export default usePromiseOwnerOptionsFetchAll
