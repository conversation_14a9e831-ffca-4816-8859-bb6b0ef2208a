import { gql } from 'urql'

import type { ILogoAndAvatarVariants } from '~/core/@types/global'

const QueryHiringMembersFilterByJob = gql<
  {
    hiringMembersFilterByJobList: {
      collection: {
        id: number
        email: string
        fullName: string
        avatarVariants?: ILogoAndAvatarVariants
        defaultColour: string
        roles: {
          id: number
          name: string
        }
      }[]
      metadata: {
        totalCount: number
      }
    }
  },
  { limit?: number; page?: number; search?: string }
>`
  query ($limit: Int!, $page: Int!, $search: String, $jobIds: [Int!]) {
    hiringMembersFilterByJobList(limit: $limit, page: $page, search: $search, jobIds: $jobIds) {
      collection {
        id
        email
        fullName
        avatarVariants
        defaultColour
        roles {
          id
          name
        }
      }
      metadata {
        totalCount
      }
    }
  }
`

export default QueryHiringMembersFilterByJob
