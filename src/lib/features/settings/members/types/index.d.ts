import type { ILogoAndAvatarVariants } from '~/core/@types/global'
import type { ISelectOption } from '~/core/ui/Select'

import type { ITenantPermissionSettingType } from '~/lib/store/permission-setting-slice'

export interface IinviteMemberForm {
  email?: string
  tenantId?: string
  roleId?: string
  userPermissions?: ITenantPermissionSettingType[]
  locationIds?: { value: string }[]
  teamDepartmentSelectAll?: boolean
  teamDepartmentIds?: { value: string }[]
}
export interface IinviteMultiMemberForm {
  email?: ISelectOption[]
  tenantId?: string
  roleId?: string
  userPermissions?: ITenantPermissionSettingType[]
  locationIds?: { value: string }[]
  teamDepartmentSelectAll?: boolean
  teamDepartmentIds?: { value: string }[]
}

export interface IMember {
  id?: string
  email?: string
  fullName?: string
  avatarVariants?: ILogoAndAvatarVariants
  defaultColour?: string
  ownTenant?: boolean
  roles?: Array<{ id: string; name: string; code: string }>
  role?: string
  roleName?: string
  roleCode?: string
  userTenants?: { openJobsCount: number }[]
  userPermissions?: ITenantPermissionSettingType[]
  departments?: Array<{
    id: string
    name: string
    subordinates: { id: string; name: string }[]
    parent: { id: string; name: string }
  }>
  locations?: Array<{ id: string; name: string }>
  allDepartments?: boolean
}

export interface IPendingMember {
  id?: string
  email?: string
  roleId?: string
  ownTenant?: boolean
  role?: { id: string; name: string; code: string }
  defaultColour?: string
}

export interface IResendMember {
  email?: string
  roleId?: string
}

export interface IDeleteMember {
  id?: string
  email?: string
  fullName?: string
}

export interface IUpdateMember {
  id?: string
  roleId?: string
  fullName?: string
  defaultColour?: string
  roleName?: string
  userPermissions?: ITenantPermissionSettingType[]
  locationIds?: { value: string }[]
  teamDepartmentSelectAll?: boolean
  teamDepartmentIds?: { value: string }[]
}

export interface IUpdateCHUBMember {
  id?: string
  teamDepartmentSelectAll?: boolean
  teamDepartmentIds?: { value: string }[]
}

export interface ICountMember {
  members?: number
  pendingMembers?: number
  claims?: number
}

export interface ITransferOwner {
  owner: { value: string }
}

export interface IMemberFilterList {
  membersList: {
    collection: IMember[]
    metadata: {
      totalCount: number
    }
  }
}
