import type { TFunction } from 'i18next'
import { z } from 'zod'

const schemaInviteEditForm = (t: TFunction) => {
  return z.object({
    roleId: z
      .string({
        required_error: `${t('form:requiredField')}`
      })
      .min(1, { message: `${t('form:requiredField')}` }),
    locationIds: z
      .array(
        z.object({
          value: z.string(),
          supportingObj: z.object({ name: z.string() })
        })
      )
      .optional(),
    teamDepartmentIds: z
      .array(
        z.object({
          value: z.string(),
          supportingObj: z.object({ name: z.string() }),
          subordinates: z.any()
        })
      )
      .optional(),
    teamDepartmentSelectAll: z.boolean().optional(),
    userPermissions: z.any()
  })
}

export default schemaInviteEditForm
