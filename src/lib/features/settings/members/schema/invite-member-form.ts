import type { TFunction } from 'i18next'
import { z } from 'zod'

import { regexEmailValidation } from '~/core/utilities/common'

const schemaInviteForm = (t: TFunction) => {
  return z.object({
    email: z
      .string()
      .refine(value => value.trim() !== '', {
        message: `${t('form:requiredField')}`
      })
      .refine(value => regexEmailValidation.test(value), {
        message: `${t('form:invalid_email')}`
      }),
    roleId: z
      .string({
        required_error: `${t('form:requiredField')}`
      })
      .min(1, { message: `${t('form:requiredField')}` }),
    teamDepartmentIds: z
      .array(
        z.object({
          value: z.string(),
          supportingObj: z.object({ name: z.string() }),
          subordinates: z.any()
        })
      )
      .optional(),
    teamDepartmentSelectAll: z.boolean().optional(),
    userPermissions: z.any()
  })
}

export default schemaInviteForm
