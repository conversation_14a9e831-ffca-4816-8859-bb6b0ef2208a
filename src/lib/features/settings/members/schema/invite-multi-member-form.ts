import type { TFunction } from 'i18next'
import { z } from 'zod'

import { regexEmailValidation } from '~/core/utilities/common'

import { findInvalidEmails } from '../utilities/common'

const schemaMultiInviteForm = (t: TFunction) => {
  return z.object({
    email: z.array(
      z.object({
        value: z.string().superRefine((data, ctx) => {
          if (findInvalidEmails(data).length > 1) {
            ctx.addIssue({
              code: 'custom',
              message: `${t('form:invalidMultiEmail', {
                number: findInvalidEmails(data).length,
                value: findInvalidEmails(data)
              })}`
            })
          } else if (findInvalidEmails(data).length === 1) {
            ctx.addIssue({
              code: 'custom',
              message: `${t('form:invalid_email')}`
            })
          }
        })
      }),
      { required_error: `${t('form:requiredField')}` }
    ),
    roleId: z
      .string({
        required_error: `${t('form:requiredField')}`
      })
      .min(1, { message: `${t('form:requiredField')}` }),
    teamDepartmentIds: z
      .array(
        z.object({
          value: z.string(),
          supportingObj: z.object({ name: z.string() }),
          subordinates: z.any()
        })
      )
      .optional(),
    teamDepartmentSelectAll: z.boolean().optional(),
    userPermissions: z.any()
  })
}

export default schemaMultiInviteForm
