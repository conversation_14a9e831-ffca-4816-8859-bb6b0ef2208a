import type { TFunction } from 'i18next'
import { z } from 'zod'

const schemaTransferOwnerForm = (t: TFunction) => {
  return z.object({
    owner: z
      .object(
        {
          value: z.string(),
          supportingObj: z.object({
            name: z.string()
          })
        },
        { required_error: `${t('form:requiredField')}` }
      )
      .nullish()
      .refine(obj => !!obj?.value, {
        message: `${t('form:requiredField')}`
      })
  })
}

export default schemaTransferOwnerForm
