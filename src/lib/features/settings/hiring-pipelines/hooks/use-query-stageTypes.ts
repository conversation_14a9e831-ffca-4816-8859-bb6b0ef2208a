import type { AnyVariables, TypedDocumentNode } from 'urql'

import useQueryGraphQL from '~/core/middleware/use-query-graphQL'

export function useQueryStageTypes({
  query,
  variables
}: {
  query: TypedDocumentNode<
    {
      stageTypes: {
        collection: Array<{
          id: string
          label: string
        }>
        metadata: {
          totalCount: number
        }
      }
    },
    AnyVariables
  >
  variables: object
}) {
  const {
    trigger,
    isLoading,
    data: response,
    error
  } = useQueryGraphQL({
    query,
    variables
  })

  const data = response?.stageTypes

  return {
    trigger,
    isLoading,
    data,
    error
  }
}
