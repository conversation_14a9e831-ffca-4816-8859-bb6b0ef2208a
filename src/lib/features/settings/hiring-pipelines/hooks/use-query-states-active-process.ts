import type { AnyVariables, TypedDocumentNode } from 'urql'

import useQueryGraphQL from '~/core/middleware/use-query-graphQL'

export function useQueryStagesActiveProcess({
  query,
  variables
}: {
  query: TypedDocumentNode<
    {
      activeProcessStageTypes: {
        collection: Array<{
          id: string
          name: string
        }>
        metadata: {
          totalCount: number
        }
      }
    },
    AnyVariables
  >
  variables: object
}) {
  const {
    trigger,
    isLoading,
    data: response,
    error
  } = useQueryGraphQL({
    query,
    variables
  })

  const data = response?.activeProcessStageTypes

  return {
    trigger,
    isLoading,
    data,
    error
  }
}
