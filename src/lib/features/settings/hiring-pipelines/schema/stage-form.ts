import type { TFunction } from 'i18next'
import { z } from 'zod'

export const schemaStageHiresForm = (t: TFunction) => {
  return z.object({
    stage: z
      .string({
        required_error: `${t('form:requiredField')}`
      })
      .min(1, {
        message: `${t('form:requiredField')}`
      })
      .max(25, {
        message: `${t('form:field_max_number_required', { number: 25 })}`
      })
      .refine(value => value.trim() !== '', {
        message: `${t('form:requiredField')}`
      }),
    clientShared: z.boolean().optional()
  })
}

const schemaStageForm = (t: TFunction) => {
  return z.object({
    stage: z
      .string({
        required_error: `${t('form:requiredField')}`
      })
      .min(1, {
        message: `${t('form:requiredField')}`
      })
      .max(25, {
        message: `${t('form:field_max_number_required', { number: 25 })}`
      })
      .refine(value => value.trim() !== '', {
        message: `${t('form:requiredField')}`
      }),
    stageType: z.object({ value: z.string() }),
    clientShared: z.boolean().optional()
  })
}

export default schemaStageForm
