import type { ISelectOption } from '~/core/ui/Select'

export interface IStageItem {
  id?: string
  stage?: string
  clientShared?: boolean
  stageType?: ISelectOption
  locked?: boolean
  _destroy?: boolean
}

export interface IPipelineItem {
  index: number
  disabled: boolean
  name: string
  value: string
  clientShared?: boolean
  toolTipLabel: string
  stages: IStageItem[]
  stagesDeleted: IStageItem[]
  updateable?: boolean
  stageLabel?: string
  stageGroup?: string
  _destroy?: boolean
  stageType?: {
    id: string
  }
  id?: string
}

export interface IPipelineForm {
  id?: string
  name?: string
  default?: boolean
  pipelineStages?: IPipelineItem[]
}

export interface IPipelineTemplate {
  id?: string
  name?: string
  updatedAt?: string
  createdAt?: string
  default?: boolean
  pipelineStages?: IPipelineItem[]
}

export type IOperationType = 'add' | 'reorder'
export type IPlaceViewPipeline = 'jobList' | 'jobDetail'
