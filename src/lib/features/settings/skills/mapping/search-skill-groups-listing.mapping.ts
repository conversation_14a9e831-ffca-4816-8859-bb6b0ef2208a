import { DEFAULT_PAGE_SIZE } from '~/core/constants/enum'
import type { ISelectOption } from '~/core/ui/Select'

import type { QuerySkillGroupProps } from '../types'

export const mappingSkillSearchGraphQL = (pageParam: QuerySkillGroupProps) => ({
  ...pageParam,
  limit: DEFAULT_PAGE_SIZE,
  search: pageParam.search || '',
  parentIds: pageParam.parentIds?.length ? pageParam.parentIds.map((item: ISelectOption) => Number(item.value)) : [],
  excludeParents: true
})

export const mappingGroupSearchGraphQL = (pageParam: QuerySkillGroupProps) => ({
  ...pageParam,
  limit: DEFAULT_PAGE_SIZE,
  search: pageParam.search || '',
  excludeParents: pageParam.excludeParents
})
