import type { ISkill, ISkillForm } from '../types'

const mappingCreateSkillsGroupForm = ({ data }: { data: ISkillForm }) => {
  return {
    name: data.name,
    isParent: true
  }
}

const mappingUpdateSkillsGroupForm = ({ data, dataSKill }: { data?: ISkill; dataSKill: ISkillForm }) => {
  return {
    ids: [Number(data?.id)],
    name: dataSKill.name
  }
}

const mappingCreateSkillsForm = ({ data }: { data: ISkillForm }) => {
  return {
    name: data.name,
    isParent: false,
    parentId: Number(data?.group?.value),
    description: data.description,
    similar: (data.similar || []).map(item => item.value)
  }
}

const mappingUpdateSkillsForm = ({ defaultValue, dataForm, isMove }: { defaultValue?: ISkill; dataForm: ISkillForm; isMove: boolean }) => {
  if (!isMove) {
    const params: {
      ids: Array<number>
      name: string
      parentId: number | null
      description: string | undefined
      similar?: Array<string>
    } = {
      ids: [Number(defaultValue?.id)],
      name: dataForm.name,
      parentId: dataForm.group?.value ? Number(dataForm.group?.value) : null,
      description: dataForm.description,
      similar: (dataForm.similar || []).map(item => item.value)
    }

    return params
  }

  return {
    ids: [Number(defaultValue?.id)],
    parentId: dataForm.group?.value ? Number(dataForm.group?.value) : null
  }
}

const mappingDeleteSkillsForm = ({
  bulkValues,
  search,
  parentIds,
  bulkSelectedAll = false
}: {
  bulkValues?: string[]
  search?: string
  parentIds?: number[]
  bulkSelectedAll: boolean | undefined
}) => {
  return {
    ids: bulkValues?.map(id => Number(id)),
    search: search || '',
    group: false,
    parentIds: parentIds?.map(id => Number(id)),
    selectAll: bulkSelectedAll
  }
}

const mappingMergeSkillsForm = ({ bulkValues, dataSkill }: { bulkValues?: string[]; dataSkill: ISkill }) => {
  const ids = bulkValues?.filter(item => item !== dataSkill?.group?.value)

  return {
    ids: ids?.map(id => Number(id)),
    targetId: Number(dataSkill?.group?.value)
  }
}

const mappingMoveSkillsForm = ({
  bulkValues,
  search,
  dataSkill,
  bulkSelectedAll = false,
  parentIds
}: {
  bulkValues?: string[]
  search?: string
  dataSkill: ISkill
  bulkSelectedAll: boolean | undefined
  parentIds?: number[]
}) => {
  return {
    ids: bulkValues?.map(id => Number(id)),
    search: search || '',
    parentId: dataSkill.group?.value ? Number(dataSkill.group?.value) : null,
    selectAll: bulkSelectedAll,
    parentIds: parentIds?.map(id => Number(id))
  }
}

const mappingAISuggestionSkillsForm = ({
  bulkValues,
  bulkSelectedAll = false,
  search,
  parentIds
}: {
  bulkValues?: string[]
  bulkSelectedAll: boolean | undefined
  search?: string
  parentIds: number[]
}) => {
  return {
    ids: bulkValues?.map(id => Number(id)),
    search: search || '',
    selectAll: bulkSelectedAll,
    parentIds: parentIds?.map(id => Number(id))
  }
}

export {
  mappingCreateSkillsGroupForm,
  mappingUpdateSkillsGroupForm,
  mappingCreateSkillsForm,
  mappingUpdateSkillsForm,
  mappingDeleteSkillsForm,
  mappingMergeSkillsForm,
  mappingMoveSkillsForm,
  mappingAISuggestionSkillsForm
}
