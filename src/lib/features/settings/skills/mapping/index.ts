import type { ISkill } from '../types'

const mappingSkillForm = (defaultValue?: ISkill) => {
  return {
    ...defaultValue,
    name: String(defaultValue?.name || ''),
    similar: (defaultValue?.similar || []).map(item => ({
      value: item,
      supportingObj: {
        name: item
      }
    }))
  }
}

const mappingMoveSkillForm = (defaultValue?: ISkill) => {
  return {
    ...defaultValue,
    name: String(defaultValue?.name || ''),
    group: null
  }
}

const mappingSkillGroupForm = (defaultValue?: ISkill) => {
  return {
    ...defaultValue,
    name: String(defaultValue?.name || '')
  }
}

export { mappingSkillGroupForm, mappingMoveSkillForm, mappingSkillForm }
