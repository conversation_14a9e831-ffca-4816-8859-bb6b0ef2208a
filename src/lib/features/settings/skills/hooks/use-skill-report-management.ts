import { useMemo, useState } from 'react'

import type { IUserInformation } from '~/core/@types/global'
import { TAB_MEMBERS } from '~/core/constants/enum'
import { changeTimezone } from '~/core/utilities/format-date'

import type { ISkillReportManagementFilter } from '../types'

const useSkillReportManagement = ({ user }: { user: IUserInformation }) => {
  const [filterValue, onChangeFilter] = useState<undefined | ISkillReportManagementFilter>(() => {
    if (!user?.timezone) return {}

    const toDay = new Date()
    return {
      dateRange: {
        from: changeTimezone({
          date: new Date(toDay.setDate(toDay.getDate() - 29)),
          timezone: user.timezone
        }),
        to: changeTimezone({
          date: new Date(),
          timezone: user.timezone
        })
      }
    }
  })

  return {
    filterControl: useMemo(
      () => ({
        value: filterValue,
        onChange: onChangeFilter
      }),
      [filterValue]
    ),
    action: {}
  }
}

export default useSkillReportManagement
