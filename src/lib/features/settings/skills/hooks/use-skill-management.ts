import configuration from '~/configuration'
import type { IResponseContextResult } from '~/core/middleware/use-context-graphQL'
import useContextGraphQL from '~/core/middleware/use-context-graphQL'
import useQueryGraphQL from '~/core/middleware/use-query-graphQL'

import { useSubmitCommon } from '~/lib/hooks/use-submit-graphql-common'

import QueryTenantSkillSettingShow from '../graphql/query-skill-settings'
import QueryTenantSkill from '../graphql/query-tenant-skill'
import QueryUpdateSkillSettingMutation from '../graphql/submit-update-skill-setting'

const useSkillManagement = () => {
  const { clientGraphQL } = useContextGraphQL()

  const { data: skillSettingsData, trigger: getSkillSettings } = useQueryGraphQL({
    query: QueryTenantSkillSettingShow,
    variables: {}
  })

  const { trigger: triggerUpdateSkillSetting, isLoading: isUpdating } = useSubmitCommon(QueryUpdateSkillSettingMutation)

  const promiseGroupOptions = (params = {}) =>
    clientGraphQL
      .query(QueryTenantSkill, { ...params })
      .toPromise()
      .then((result: IResponseContextResult<any>) => {
        if (result.error) {
          return {
            metadata: {
              totalCount: configuration.defaultAsyncLoadingOptions
            },
            collection: []
          }
        }

        const { systemParentSkillsList } = result.data
        const collection = systemParentSkillsList?.collection || []
        const metadata = systemParentSkillsList?.metadata || { totalCount: 0 }

        const cloneData = collection.map(item => {
          return {
            value: item.id,
            supportingObj: {
              name: item.name
            }
          }
        })

        return { metadata, collection: cloneData }
      })

  return {
    promiseGroupOptions,
    getSkillSettings,
    skillSettingsData,
    triggerUpdateSkillSetting,
    isUpdating
  }
}
export default useSkillManagement
