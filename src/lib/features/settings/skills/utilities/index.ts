import { differenceInDays, differenceInHours, differenceInMonths, differenceInYears, endOfDay, startOfDay } from 'date-fns'
import type { TFunction } from 'next-i18next'

import { changeTimezone, dayStandardFormatDate, monthFormatDate, yearFormatDate } from '~/core/utilities/format-date'

import { addTzToDate } from '~/lib/features/calendar/utilities/helper-schedule-interview'

import type { ISkillReportManagementFilter, ISkillReportOvertime } from '../types'

export const mappingSkillReportFilter = ({ id, filter, timezone }: { id: number; filter?: ISkillReportManagementFilter; timezone: string }) => {
  const detectFilterDateRange = ({
    dateRange
  }: {
    dateRange?: {
      from?: string | Date
      to?: string | Date
    }
  }) => {
    if (dateRange?.from && dateRange?.to) {
      return {
        dateRange: undefined,
        fromDatetime: addTzToDate(String(startOfDay(new Date(dateRange.from))), timezone),
        toDatetime: addTzToDate(String(endOfDay(new Date(dateRange.to))), timezone)
      }
    }

    const toDay = new Date()
    return {
      dateRange: undefined,
      fromDatetime: addTzToDate(
        String(
          startOfDay(
            changeTimezone({
              date: new Date(toDay.setDate(toDay.getDate() - 365)),
              timezone: timezone
            })
          )
        ),
        timezone
      ),
      toDatetime: addTzToDate(
        String(
          endOfDay(
            changeTimezone({
              date: new Date(),
              timezone: timezone
            })
          )
        ),
        timezone
      )
    }
  }

  return {
    ...filter,
    ...detectFilterDateRange({ dateRange: filter?.dateRange }),
    id,
    isFilterTouched: undefined
  }
}

export const mappingSkillReportMetricData = ({
  data
}: {
  data: {
    [key: string]: number
  }
}) => {
  if (Object.keys(data).length === 0) return undefined

  const calculatePercent = ({ currentNumber, previousNumber }: { currentNumber: number; previousNumber?: number }) => {
    if (!previousNumber) return undefined

    const differ = Math.abs(currentNumber - previousNumber)
    return {
      increase: currentNumber > previousNumber,
      value: Math.round((differ / previousNumber) * 100)
    }
  }

  return {
    profiles: {
      currentData: Number(data.profiles_count || 0),
      previousData: data.previous_profiles_count,
      compare: calculatePercent({
        currentNumber: Number(data.profiles_count || 0),
        previousNumber: data.previous_profiles_count
      })
    },
    jobs: {
      currentData: Number(data.jobs_count || 0),
      previousData: data.previous_jobs_count,
      compare: calculatePercent({
        currentNumber: Number(data.jobs_count || 0),
        previousNumber: data.previous_jobs_count
      })
    },
    hiredJobs: {
      currentData: Number(data.hired_jobs_count || 0),
      previousData: data.previous_hired_jobs_count,
      compare: calculatePercent({
        currentNumber: Number(data.hired_jobs_count || 0),
        previousNumber: data.previous_hired_jobs_count
      })
    }
  }
}

export const mappingSkillReportLineData = ({ data = [], t }: { data: Array<ISkillReportOvertime>; t: TFunction }) => {
  let mappings: any = []
  let previousProfilesCount = 0
  let previousJobsCount = 0

  if (data && data.length) {
    data.forEach(
      (item: {
        from_date: string
        group: string
        groupped_date: string
        summary: { profiles_count: number; jobs_count: number }
        to_date: string
      }) => {
        previousProfilesCount += item.summary?.profiles_count || 0
        previousJobsCount += item.summary?.jobs_count || 0
      }
    )

    mappings = [
      {
        id: t('settings:skills:report:overtimeReport:jobsTitle'),
        color: '#61D2AA',
        data: data.map(
          (item: {
            from_date: string
            group: string
            groupped_date: string
            summary: { profiles_count: number; jobs_count: number }
            to_date: string
          }) => ({
            label: item.groupped_date,
            groupType: item.group,
            fromDate: item.from_date,
            toDate: item.to_date,
            x: item.groupped_date,
            y: item.summary?.jobs_count || 0
          })
        )
      },
      {
        id: t('settings:skills:report:overtimeReport:profilesTitle'),
        color: '#5B8DFF',
        data: data.map(
          (item: {
            from_date: string
            group: string
            groupped_date: string
            summary: { profiles_count: number; jobs_count: number }
            to_date: string
          }) => ({
            label: item.groupped_date,
            groupType: item.group,
            fromDate: item.from_date,
            toDate: item.to_date,
            x: item.groupped_date,
            y: item.summary?.profiles_count || 0
          })
        )
      }
    ]
  }

  if (previousProfilesCount == 0 && previousJobsCount == 0) {
    return undefined
  }

  return mappings
}

export const mappingSkillReportBarData = ({ data = [], t, timezone }: { data: Array<ISkillReportOvertime>; t: TFunction; timezone: string }) => {
  const mappings: any = []
  let previousProfilesCount = 0
  let previousJobsCount = 0

  if (data && data.length) {
    data.forEach(
      (
        item: {
          from_date: string
          group: string
          groupped_date: string
          summary: { profiles_count: number; jobs_count: number }
          to_date: string
        },
        index: number
      ) => {
        const profilesCount = previousProfilesCount + item.summary?.profiles_count || 0
        const jobsCount = previousJobsCount + item.summary?.jobs_count || 0
        const currentDateByTimeZone = new Date(
          new Date().toLocaleString('en-US', {
            timeZone: timezone
          })
        )
        const fromDateByTimeZone = new Date(
          new Date(item.from_date).toLocaleString('en-US', {
            timeZone: timezone
          })
        )
        const toDateByTimeZone = new Date(
          new Date(item.to_date).toLocaleString('en-US', {
            timeZone: timezone
          })
        )
        const formatDateByTimeZone = differenceInHours(toDateByTimeZone, fromDateByTimeZone) <= 24 ? toDateByTimeZone : fromDateByTimeZone

        const distanceMonth = differenceInMonths(
          new Date(Number(yearFormatDate(currentDateByTimeZone)), Number(monthFormatDate(currentDateByTimeZone)), 1),
          new Date(Number(yearFormatDate(formatDateByTimeZone)), Number(monthFormatDate(formatDateByTimeZone)), 1)
        )
        const distanceYear = differenceInYears(
          new Date(Number(yearFormatDate(currentDateByTimeZone)), Number(monthFormatDate(currentDateByTimeZone)), 1),
          new Date(Number(yearFormatDate(formatDateByTimeZone)), Number(monthFormatDate(formatDateByTimeZone)), 1)
        )
        const distanceDay = differenceInDays(
          new Date(
            Number(yearFormatDate(currentDateByTimeZone)),
            Number(monthFormatDate(currentDateByTimeZone)),
            Number(dayStandardFormatDate(currentDateByTimeZone))
          ),
          new Date(
            Number(yearFormatDate(formatDateByTimeZone)),
            Number(monthFormatDate(formatDateByTimeZone)),
            Number(dayStandardFormatDate(formatDateByTimeZone))
          )
        )

        mappings.push({
          id: index,
          label: item.groupped_date,
          groupType: item.group,
          fromDate: item.from_date,
          toDate: item.to_date,
          profiles: distanceMonth >= 0 && distanceYear >= 0 && distanceDay >= 0 ? profilesCount : 0,
          profilesColor: 'hsl(221.71deg 100% 67.84%)',
          jobs: distanceMonth >= 0 && distanceYear >= 0 && distanceDay >= 0 ? jobsCount : 0,
          jobsColor: 'hsl(158.76deg 55.67% 60.2%)'
        })

        if (distanceMonth >= 0 && distanceYear >= 0 && distanceDay >= 0) {
          previousProfilesCount += item.summary?.profiles_count || 0
          previousJobsCount += item.summary?.jobs_count || 0
        }
      }
    )
  }

  if (previousProfilesCount == 0 && previousJobsCount == 0) {
    return undefined
  }

  return mappings
}
