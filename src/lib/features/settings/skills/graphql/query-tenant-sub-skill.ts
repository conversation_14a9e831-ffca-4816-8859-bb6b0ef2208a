import { gql } from 'urql'

import type { ISkill } from '../types'

const QueryTenantSubSkill = gql<
  {
    systemSkillsList: {
      collection: Array<ISkill>
      metadata: { totalCount: number }
    }
  },
  {}
>`
  query ($limit: Int, $page: Int, $search: String, $parentId: Int, $parentIds: [Int!], $excludeParents: Boolean, $sorting: JSON) {
    systemSkillsList(
      limit: $limit
      page: $page
      search: $search
      parentId: $parentId
      parentIds: $parentIds
      excludeParents: $excludeParents
      sorting: $sorting
    ) {
      collection {
        id
        name
        enabled
        key
        profilesCount
        jobsCount
        updatedAt
        groupTotalCount
        groupProfilesCount
        groupJobsCount
        description
        similar
        equivalentSkills
        parent {
          name
          id
        }
      }
      metadata {
        totalCount
        extras
      }
    }
  }
`

export default QueryTenantSubSkill
