import { gql } from 'urql'

import type { IReportDetailApplication, IReportDetailFilter } from '~/lib/features/reports/types'

const QuerySkillsShowHiredJobsSummaryDetails = gql<
  {
    skillsShowHiredJobsSummaryDetails: {
      data: Array<IReportDetailApplication>
      metadata: {
        totalCount: number
        currentPage: number
      }
    }
  },
  IReportDetailFilter
>`
  query ($id: Int!, $fromDatetime: ISO8601DateTime!, $toDatetime: ISO8601DateTime!, $page: Int, $limit: Int) {
    skillsShowHiredJobsSummaryDetails(id: $id, fromDatetime: $fromDatetime, toDatetime: $toDatetime, page: $page, limit: $limit) {
      collection {
        id
        title
        applicantsCount
        createdAt
        openedAt
        archivedAt
        firstHiredDate
        statusDescription
        status
        currentUserAccessible
        jobLocations {
          address
          city
          state
          country
        }
        department {
          name
        }
        company {
          permittedFields
        }
      }

      metadata {
        totalCount
        currentPage
      }
    }
  }
`

export default QuerySkillsShowHiredJobsSummaryDetails
