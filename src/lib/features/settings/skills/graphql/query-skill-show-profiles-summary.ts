import { gql } from 'urql'

import type { ISkillReportDetailProfile, ISkillReportManagementFilter } from '~/lib/features/settings/skills/types'

const QuerySkillsShowProfilesSummaryDetails = gql<
  {
    skillsShowProfilesSummaryDetails: {
      data: Array<ISkillReportDetailProfile>
      metadata: {
        totalCount: number
        currentPage: number
      }
    }
  },
  ISkillReportManagementFilter
>`
  query ($id: Int!, $fromDatetime: ISO8601DateTime!, $toDatetime: ISO8601DateTime!, $page: Int, $limit: Int) {
    skillsShowProfilesSummaryDetails(id: $id, fromDatetime: $fromDatetime, toDatetime: $toDatetime, page: $page, limit: $limit) {
      collection {
        id
        fullName
        avatarVariants
        sourcedDescription
        applicants {
          id
          rejectedReasonLabel
          flagNew
          status
          job {
            id
            title
            status
            statusDescription
            currentUserAccessible
          }
          jobStage {
            stageLabel
            stageTypeId
          }
        }
      }

      metadata {
        totalCount
        currentPage
      }
    }
  }
`

export default QuerySkillsShowProfilesSummaryDetails
