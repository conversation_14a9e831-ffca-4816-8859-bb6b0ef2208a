import { gql } from 'urql'

import type { ISkill } from '../types'

const QueryTenantSkill = gql<
  {
    systemParentSkillsList: {
      collection: Array<ISkill>
      metadata: { totalCount: number }
    }
  },
  {}
>`
  query ($limit: Int, $page: Int, $search: String) {
    systemParentSkillsList(limit: $limit, page: $page, search: $search) {
      collection {
        id
        name
        enabled
        key
        updatedAt
      }
      metadata {
        totalCount
      }
    }
  }
`

export default QueryTenantSkill
