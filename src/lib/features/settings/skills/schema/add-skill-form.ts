import type { TFunction } from 'i18next'
import { z } from 'zod'

import { removeHTMLTags } from '~/core/utilities/common'

const schemaAddSkillForm = (t: TFunction) => {
  return z.object({
    name: z
      .string()
      .trim()
      .min(1, {
        message: `${t('form:requiredField')}`
      })
      .max(30, {
        message: `${t('form:field_max_number_required', { number: 30 })}`
      }),
    group: z
      .object({
        id: z.string().optional(),
        value: z.string().optional(),
        supportingObj: z
          .object({
            name: z.string()
          })
          .optional()
      })
      .nullish()
      .refine(obj => !!obj?.value, {
        message: `${t('form:requiredField')}`
      }),
    description: z.nullable(
      z
        .string()
        .refine(async content => removeHTMLTags(content || '').length <= 1000, {
          message: `${t('form:field_max_number_required', {
            number: 1000
          })}`
        })
        .optional()
    ),
    similar: z
      .array(
        z
          .object({
            id: z.string().optional(),
            value: z.string().optional(),
            supportingObj: z
              .object({
                name: z.string()
              })
              .optional()
          })
          .nullish()
          .refine(obj => !!obj?.value, {
            message: `${t('form:requiredField')}`
          })
      )
      .optional()
  })
}

export default schemaAddSkillForm
