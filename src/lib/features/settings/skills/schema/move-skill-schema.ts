import type { TFunction } from 'i18next'
import { z } from 'zod'

const schemaMoveSkillForm = (t: TFunction) => {
  return z.object({
    group: z
      .object({
        value: z.string(),
        supportingObj: z.object({
          name: z.string()
        })
      })
      .nullish()
      .refine(obj => !!obj?.value, {
        message: `${t('form:requiredField')}`
      })
  })
}

export default schemaMoveSkillForm
