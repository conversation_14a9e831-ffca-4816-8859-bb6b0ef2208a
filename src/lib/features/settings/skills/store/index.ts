import { create } from 'zustand'

import type { BulkActionSlice } from './bulk-action-slice'
import { createBulkActionSlice } from './bulk-action-slice'
import type { SkillSettingsSlice } from './skill-settings-slice'
import { createSkillSettingsSlice } from './skill-settings-slice'

const useSkillsStore = create<BulkActionSlice & SkillSettingsSlice>()((...a) => ({
  ...createBulkActionSlice(...a),
  ...createSkillSettingsSlice(...a)
}))

export default useSkillsStore
