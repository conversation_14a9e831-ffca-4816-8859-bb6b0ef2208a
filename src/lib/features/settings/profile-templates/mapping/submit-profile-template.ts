import { PROFILE_TEMPLATE_PREVIEW_STYLE } from '../../profiles/edit/utilities/enum'
import type { IProfileTemplate, IProfileTemplateForm, IProfileTemplateMetrics } from '../types'
import { PROFILE_FIELD_PERMISSIONS } from '../utilities'

export const mappingSubmitDataProfileTemplate = (data: IProfileTemplateForm) => {
  return {
    id: data?.id ? Number(data.id) : undefined,
    name: data.name,
    guideline: data.guideline || '',
    default: data.default,
    templateNameEnabling: !!data.templateNameEnabling,
    dateEnabling: !!data.dateEnabling,
    profileIdEnabling: !!data.profileIdEnabling,
    fullnameEnabling: !!data.fullnameEnabling,
    emailEnabling: !!data.emailEnabling,
    phoneNumberEnabling: !!data.phoneNumberEnabling,
    avatarEnabling: !!data.avatarEnabling,
    logoEnabling: !!data.logoEnabling,
    watermarkEnabling: !!data.watermarkEnabling,
    templateStyle: data.templateStyle,
    cvSections: (data.cvSections || []).map((item, index) => {
      return {
        id: item.id ? String(item.id) : undefined,
        setting_id: item.setting_id ? String(item.setting_id) : undefined,
        name: item.name,
        index: String(index),
        custom_related_fields: item.customRelatedFields || {},
        custom_fields: (item.custom_fields || []).map((s, subIndex) => {
          return {
            id: s.id ? String(s.id) : undefined,
            custom_related_fields: s.customRelatedFields || {},
            setting_id: s.setting_id ? String(s.setting_id) : undefined,
            custom_field_setting_id: s.custom_field_setting_id ? String(s.custom_field_setting_id) : undefined,
            index: String(subIndex)
          }
        })
      }
    })
  }
}

export const mappingCreateDataProfileTemplate = () => {
  return {
    id: undefined,
    name: undefined,
    guideline: undefined,
    default: false,
    templateNameEnabling: true,
    dateEnabling: true,
    profileIdEnabling: true,
    fullnameEnabling: true,
    emailEnabling: true,
    phoneNumberEnabling: true,
    avatarEnabling: true,
    logoEnabling: true,
    watermarkEnabling: false,
    templateStyle: PROFILE_TEMPLATE_PREVIEW_STYLE.default,
    cvSections: []
  }
}

export const mappingEditDataProfileTemplate = (data?: IProfileTemplate) => {
  if (!data) return undefined

  return {
    id: data.id,
    name: data.name,
    guideline: data.guideline || '',
    default: data.default,
    templateNameEnabling: !!data.templateNameEnabling,
    dateEnabling: !!data.dateEnabling,
    profileIdEnabling: !!data.profileIdEnabling,
    fullnameEnabling: !!data.fullnameEnabling,
    emailEnabling: !!data.emailEnabling,
    phoneNumberEnabling: !!data.phoneNumberEnabling,
    avatarEnabling: !!data.avatarEnabling,
    logoEnabling: !!data.logoEnabling,
    watermarkEnabling: !!data.watermarkEnabling,
    templateStyle: data.templateStyle,
    cvSections: (data.cvTemplateSections || [])
      .sort((a: IProfileTemplateMetrics, b: IProfileTemplateMetrics) => Number(a.index) - Number(b.index))
      .map((item, index) => {
        return {
          id: item.id ? String(item.id) : undefined,
          lockedSection: item?.setting?.key === PROFILE_FIELD_PERMISSIONS,
          field: item?.setting?.field,
          setting_id: Number(item.settingId) >= 0 ? String(item.settingId) : undefined,
          name: item.name,
          index: index,
          customRelatedFields: item.customRelatedFields || {},
          custom_fields: (item.cvTemplateCustomFields || [])
            .filter(s => !(!s.setting?.field && !s.customFieldSetting))
            ?.sort((a: IProfileTemplateMetrics, b: IProfileTemplateMetrics) => Number(a.index) - Number(b.index))
            .map((s, subIndex) => {
              return {
                id: s.id ? String(s.id) : undefined,
                setting_id: Number(item.settingId) >= 0 ? String(s.settingId) : undefined,
                custom_field_setting_id: s.customFieldSetting?.id ? String(s.customFieldSetting?.id) : undefined,
                index: subIndex,
                name: s.customFieldSetting?.id ? s.customFieldSetting?.fieldName : s.setting?.name,
                field: s.setting?.field,
                customRelatedFields: s.customRelatedFields || {}
              }
            })
        }
      })
  }
}
