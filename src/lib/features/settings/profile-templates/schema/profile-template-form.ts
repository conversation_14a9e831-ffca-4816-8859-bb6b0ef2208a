import type { TFunction } from 'i18next'
import { z } from 'zod'

import { removeHTMLTags } from '~/core/utilities/common'

const schemaProfileTemplateForm = (t: TFunction) => {
  return z.object({
    id: z.string().optional(),
    name: z
      .string({
        required_error: `${t('form:requiredField')}`
      })
      .min(1, {
        message: `${t('form:requiredField')}`
      })
      .max(50, {
        message: `${t('form:field_max_number_required', { number: 50 })}`
      })
      .refine(value => value.trim() !== '', {
        message: `${t('form:requiredField')}`
      }),
    guideline: z
      .string()
      .refine(async content => removeHTMLTags(content || '').length <= 5000, {
        message: `${t('form:field_max_number_required', {
          number: 5000
        })}`
      })
      .optional(),
    default: z.boolean().optional(),
    templateStyle: z.string(),
    templateNameEnabling: z.boolean().optional(),
    fullnameEnabling: z.boolean().optional(),
    emailEnabling: z.boolean().optional(),
    phoneNumberEnabling: z.boolean().optional(),
    dateEnabling: z.boolean().optional(),
    profileIdEnabling: z.boolean().optional(),
    avatarEnabling: z.boolean().optional(),
    logoEnabling: z.boolean().optional(),
    watermarkEnabling: z.boolean().optional(),
    cvSections: z
      .array(
        z
          .object({
            id: z.string().optional(),
            setting_id: z.string().optional(),
            name: z
              .string({
                required_error: `${t('form:field_input_section_required')}`
              })
              .min(1, {
                message: `${t('form:field_input_section_required')}`
              })
              .max(250, {
                message: `${t('form:field_max_number_required', {
                  number: 250
                })}`
              })
              .refine(value => value.trim() !== '', {
                message: `${t('form:field_input_section_required')}`
              }),
            custom_fields: z.array(
              z.object({
                id: z.string().optional(),
                setting_id: z.string(),
                custom_field_setting_id: z.string().optional(),
                name: z.string().optional(),
                customRelatedFields: z.record(z.string(), z.boolean()).nullish().optional()
              })
            ),
            customRelatedFields: z.record(z.string(), z.boolean()).nullish().optional()
          })
          .optional()
          .refine(
            cvSection => {
              const settingId = cvSection?.setting_id
              const checkSettingId = !settingId || Number(settingId) == 0 || settingId === 'null'
              if (checkSettingId && cvSection?.custom_fields.length === 0) {
                return false
              }

              return true
            },
            {
              message: `${t('form:pleaseSelectAtLeastOneField')}`
            }
          )
      )
      .min(1, {
        message: `${t('form:pleaseSelectAtLeastOneSection')}`
      })
  })
}

export default schemaProfileTemplateForm
