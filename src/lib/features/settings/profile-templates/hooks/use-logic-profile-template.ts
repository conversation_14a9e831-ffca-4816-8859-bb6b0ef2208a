import { useTranslation } from 'react-i18next'

import configuration from '~/configuration'
import { DEFAULT_PAGE_SIZE } from '~/core/constants/enum'
import type { IResponseContextResult } from '~/core/middleware/use-context-graphQL'
import useContextGraphQL from '~/core/middleware/use-context-graphQL'
import useQueryGraphQL from '~/core/middleware/use-query-graphQL'
import type { IPromiseSearchOption, ISelectOption } from '~/core/ui/Select'

import QueryProfileTemplatesFieldList from '../graphql/query-profile-templates-field'
import QueryProfileTemplatesSectionList from '../graphql/query-profile-templates-section'
import type { IProfileCustomField } from '../types'
import { PROFILE_CUSTOM_FIELD_PERMISSIONS } from '../utilities'

interface A {
  sectionSettingIds?: Array<number>
  fieldSettingIds?: Array<number>
  fieldCustomFieldSettingIds?: Array<number>
}

export function useLogicProfileTemplates(props: A) {
  const { sectionSettingIds, fieldSettingIds, fieldCustomFieldSettingIds } = props
  const { clientGraphQL } = useContextGraphQL()
  const { t } = useTranslation()

  const {
    trigger: fetchSectionList,
    isLoading: isLoadingSectionList,
    data: dataSectionList
  } = useQueryGraphQL({
    query: QueryProfileTemplatesSectionList,
    variables: {
      limit: 3456,
      page: 1,
      search: '',
      settingIds: sectionSettingIds
    },
    shouldPause: true
  })

  const {
    trigger: fetchFieldList,
    isLoading: isLoadingFieldList,
    data: dataFieldList
  } = useQueryGraphQL({
    query: QueryProfileTemplatesFieldList,
    variables: {
      limit: 3456,
      page: 1,
      search: '',
      settingIds: fieldSettingIds,
      customFieldSettingIds: fieldCustomFieldSettingIds
    },
    shouldPause: true
  })

  const formatFieldList = (collection: IProfileCustomField[]) => {
    const cloneData = collection.map((item: IProfileCustomField) => {
      if (item.key === PROFILE_CUSTOM_FIELD_PERMISSIONS) {
        return (item.customFieldSettings || []).map(i => {
          if (!(i.visibleToEmployeeProfile && i.visibility)) return undefined

          return {
            id: item.id,
            value: i.id,
            supportingObj: {
              name: i.fieldName
            }
          }
        })
      }

      return {
        id: undefined,
        value: item.id,
        field: item.field,
        supportingObj: {
          name: item.name
        }
      }
    })

    const flatData = cloneData.flat().filter(item => item) || []
    return flatData as ISelectOption[]
  }

  const promiseFieldListOptions = (params = {} as IPromiseSearchOption) =>
    new Promise<any>(resolve => {
      clientGraphQL
        .query(QueryProfileTemplatesFieldList, {
          ...params,
          limit: DEFAULT_PAGE_SIZE
        })
        .toPromise()
        .then((result: IResponseContextResult<IProfileCustomField>) => {
          if (result.error) {
            resolve({
              metadata: {
                totalCount: configuration.defaultPageSize
              },
              collection: []
            })
          }

          const { profileCustomFieldsList } = result?.data
          const collection = profileCustomFieldsList?.collection || []
          const metadata = profileCustomFieldsList?.metadata || {
            totalCount: 0
          }

          const cloneData = formatFieldList(collection)
          return resolve({ metadata, collection: cloneData })
        })
    })

  return {
    sectionFields: {
      fetchSectionList,
      isLoadingSectionList,
      dataSectionList
    },
    fieldFields: {
      fetchFieldList,
      isLoadingFieldList,
      dataFieldList
    },
    formatFieldList,
    promiseFieldListOptions
  }
}
