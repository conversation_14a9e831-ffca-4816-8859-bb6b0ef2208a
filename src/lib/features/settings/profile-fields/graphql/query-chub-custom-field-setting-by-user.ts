import { gql } from 'urql'

import type { CustomFieldResponseItem } from '../types/custom-field'

const QueryCHubCustomFieldSettingByUser = gql<
  {
    customFieldSettingsByUserList: {
      collection: Array<CustomFieldResponseItem>
      metadata: {
        totalCount: number
      }
    }
  },
  {
    limit?: number
    page?: number
    objectKind: string
  }
>`
  query ($limit: Int, $page: Int, $employeeId: Int, $objectKind: ChubCustomFieldObjectKind!) {
    customFieldSettingsByUserList(limit: $limit, page: $page, employeeId: $employeeId, objectKind: $objectKind) {
      collection {
        id
        fieldName
        objectKind
        fieldKind
        index
        roleIds
        selectOptions
        visibility
        clientUserVisibility
        careerSiteVisibility
        visibleToEmployeeProfile
      }
      metadata {
        totalCount
      }
    }
  }
`

export default QueryCHubCustomFieldSettingByUser
