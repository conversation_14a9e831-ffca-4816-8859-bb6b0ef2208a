import { gql } from 'urql'

import type { CustomFieldResponseItem } from '../types/custom-field'

const QueryCustomFieldSettingByUser = gql<
  {
    customFieldSettingsByUserList: {
      collection: Array<CustomFieldResponseItem>
      metadata: {
        totalCount: number
      }
    }
  },
  {
    limit?: number
    page?: number
    employeeId?: number
    objectKind: string
  }
>`
  query ($limit: Int, $page: Int, $objectKind: CustomFieldObjectKind!, $employeeId: Int) {
    customFieldSettingsByUserList(limit: $limit, page: $page, objectKind: $objectKind, employeeId: $employeeId) {
      collection {
        id
        fieldName
        objectKind
        fieldKind
        index
        roleIds
        selectOptions
        visibility
        visibleToEmployeeProfile
        careerSiteVisibility
      }
      metadata {
        totalCount
      }
    }
  }
`

export default QueryCustomFieldSettingByUser
