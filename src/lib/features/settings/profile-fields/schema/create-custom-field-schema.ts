import type { TFunction } from 'next-i18next'
import { z } from 'zod'

export const schemaCreateCustomFields = (t: TFunction) => {
  return z
    .object({
      id: z.string().optional(),
      index: z.number().optional(),
      objectKind: z.string().min(1, {
        message: `${t('form:requiredField')}`
      }),
      fieldName: z
        .string()
        .min(1, {
          message: `${t('form:requiredField')}`
        })
        .max(30, {
          message: `${t('form:field_max_number_required', { number: 30 })}`
        }),
      roleIds: z.array(z.number()).nullish().optional(),
      fieldKind: z.string().min(1, {
        message: `${t('form:requiredField')}`
      }),
      selectOptions: z
        .array(
          z.object({
            key: z.string().optional(),
            description: z
              .string()
              .max(100, {
                message: `${t('form:field_max_number_required', {
                  number: 100
                })}`
              })
              .optional(),
            index: z.number()
          })
        )
        .optional()
    })
    .refine(
      data => {
        if (data.objectKind === 'profile' && data.roleIds) {
          return !((data.roleIds || []).length <= 0)
        }
        return true
      },
      {
        message: `${t('form:requiredField')}`,
        path: ['roleIds']
      }
    )
    .superRefine((data, ctx) => {
      if (data.fieldKind === 'array' || data.fieldKind === 'multiple') {
        data.selectOptions?.forEach((fi, index) => {
          if (fi.description === '')
            ctx.addIssue({
              code: 'custom',
              message: `${t('form:requiredField')}`,
              path: [`selectOptions-${index}`]
            })
          else if (fi.description && fi.description.length > 100)
            ctx.addIssue({
              code: 'custom',
              message: `${t('form:field_max_number_required', {
                number: 100
              })}`,
              path: [`selectOptions-${index}`]
            })
        })
      }
    })
}
