import type { ISelectOption } from '~/core/ui/Select'

import type { CustomFieldComponentType } from '~/components/CustomField'

/* Formatted Data get from API */
export type CustomFieldResponseItem = {
  id: number
  customSettingId: number
  fieldName: string
  objectKind: string
  fieldKind: string
  index: number
  roleIds: Array<number>
  value?: string
  selectedOptionKeys?: Array<string>
  selectOptions: Array<{
    key: string
    index: number
    value: string
  }>
  visibility?: boolean
  visibleToEmployeeProfile?: boolean
  clientUserVisibility?: boolean
  careerSiteVisibility?: boolean
}

/* Formatted Data use inside Component/Form */
export type CustomFieldFormType = {
  customFields?: {
    [key: string]: {
      id: string | number

      label?: string
      type: string

      value?: string | number
      objectKind?: string
      index: number
      roleIds?: Array<number>
      selectOptions?: Array<ISelectOption>
      selectedOptionKeys?: Array<string>
    }
  }
}

export type CustomFieldViewType = {
  id: number | string
  index: number
  type: CustomFieldComponentType['type']
  name: string
  label?: string
  selectOptions?: Array<ISelectOption>
  roleIds?: Array<number>
  visibility?: boolean
  visibleToEmployeeProfile?: boolean
  clientUserVisibility?: boolean
  careerSiteVisibility?: boolean
}
