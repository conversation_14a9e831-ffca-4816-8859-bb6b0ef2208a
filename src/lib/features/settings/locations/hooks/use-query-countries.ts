import type { AnyVariables, TypedDocumentNode } from 'urql'

import useQueryGraphQL from '~/core/middleware/use-query-graphQL'

import { mappingPublicCountriesListToSelect } from '../mapping/public-countries-list-mapping'

export function useQueryCountries({
  query,
  variables,
  shouldPause = true
}: {
  query: TypedDocumentNode<
    {
      publicCountriesList: {
        collection: Array<{
          id: string
          name: string
          code: string
        }>
        metadata: {
          totalCount: number
        }
      }
    },
    AnyVariables
  >
  variables: object
  shouldPause?: boolean
}) {
  const {
    trigger,
    isLoading,
    data: response,
    error
  } = useQueryGraphQL({
    query,
    variables,
    shouldPause
  })
  const data = response?.publicCountriesList.collection || []

  return {
    trigger,
    isLoading,
    countries: response?.publicCountriesList.collection,
    data: mappingPublicCountriesListToSelect(data),
    error
  }
}
