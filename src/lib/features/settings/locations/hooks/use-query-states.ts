import type { AnyVariables, TypedDocumentNode } from 'urql'

import useQueryGraphQL from '~/core/middleware/use-query-graphQL'

import { mappingPublicStatesListToSelect } from '../mapping/public-states-list-mapping'

export function useQueryStates({
  query,
  variables,
  shouldPause = true
}: {
  query: TypedDocumentNode<
    {
      publicStatesList: {
        collection: Array<{
          id: string
          name: string
        }>
        metadata: {
          totalCount: number
        }
      }
    },
    AnyVariables
  >
  variables: object
  shouldPause?: boolean
}) {
  const {
    trigger,
    isLoading,
    data: response,
    error
  } = useQueryGraphQL({
    query,
    variables,
    shouldPause
  })
  const data = response?.publicStatesList.collection || []

  return {
    trigger,
    isLoading,
    data: mappingPublicStatesListToSelect(data),
    error
  }
}
