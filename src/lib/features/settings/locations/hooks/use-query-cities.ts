import type { AnyVariables, TypedDocumentNode } from 'urql'

import useQueryGraphQL from '~/core/middleware/use-query-graphQL'

import { mappingPublicCitiesListToSelect } from '../mapping/public-cities-list-mapping'

export function useQueryCities({
  query,
  variables,
  shouldPause = true
}: {
  query: TypedDocumentNode<
    {
      publicCitiesList: {
        collection: Array<{
          id: string
          name: string
        }>
        metadata: {
          totalCount: number
        }
      }
    },
    AnyVariables
  >
  variables: object
  shouldPause?: boolean
}) {
  const {
    trigger,
    isLoading,
    data: response,
    error
  } = useQueryGraphQL({
    query,
    variables,
    shouldPause
  })
  const data = response?.publicCitiesList.collection || []

  return {
    trigger,
    isLoading,
    data: mappingPublicCitiesListToSelect(data),
    error
  }
}
