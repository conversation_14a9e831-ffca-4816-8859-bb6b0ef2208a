import type { TFunction } from 'i18next'
import { z } from 'zod'

import type { ILocation } from '../types'

const schemaDeleteLocationForm = (t: TFunction, dataLocation: ILocation) => {
  return (dataLocation?.jobLocationsCount || 0) > 0
    ? z.object({
        transferLocationId: z.object(
          {
            id: z.string().optional(),
            value: z.string().optional(),
            supportingObj: z.object({
              name: z.string().optional()
            })
          },
          {
            required_error: `${t('form:requiredField')}`
          }
        )
      })
    : z.object({
        transferLocationId: z
          .object({
            id: z.string(),
            value: z.string(),
            supportingObj: z.object({
              name: z.string()
            })
          })
          .nullish()
          .optional()
      })
}

export default schemaDeleteLocationForm
