import type { TFunction } from 'i18next'
import { z } from 'zod'

const schemaAddLocationForm = (t: TFunction) => {
  return z.object({
    id: z.any().optional(),
    name: z
      .string()
      .trim()
      .min(1, {
        message: `${t('form:requiredField')}`
      })
      .max(250, {
        message: `${t('form:field_max_number_required', { number: 250 })}`
      }),
    address: z
      .string()
      .max(250, {
        message: `${t('form:field_max_number_required', { number: 250 })}`
      })
      .optional(),
    country: z.object(
      {
        id: z.any(),
        supportingObj: z.any(),
        value: z.any()
      },
      {
        required_error: `${t('form:requiredField')}`
      }
    ),
    state: z.object(
      {
        id: z.any(),
        supportingObj: z.any(),
        value: z.any()
      },
      {
        required_error: `${t('form:requiredField')}`
      }
    ),
    countryCode: z.string().optional(),
    city: z.nullable(z.string()).optional(),
    zipCode: z.nullable(z.string()).optional(),
    headquarter: z.boolean().optional(),
    countryStateId: z.number().optional()
  })
}

export default schemaAddLocationForm
