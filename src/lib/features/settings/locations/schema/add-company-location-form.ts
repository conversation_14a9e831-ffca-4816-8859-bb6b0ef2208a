import type { TFunction } from 'next-i18next'
import { z } from 'zod'

const schemaAddCompanyLocationForm = (t: TFunction) => {
  return z.object({
    id: z.any().optional(),
    address: z
      .nullable(
        z.string().max(250, {
          message: `${t('form:field_max_number_required', { number: 250 })}`
        })
      )
      .optional(),
    country: z
      .object(
        {
          id: z.any(),
          supportingObj: z.any(),
          value: z.any()
        },
        {
          required_error: `${t('form:requiredField')}`
        }
      )
      .nullish()
      .refine(obj => !!obj?.value, {
        message: `${t('form:requiredField')}`
      }),
    state: z
      .object(
        {
          id: z.any(),
          supportingObj: z.any(),
          value: z.any()
        },
        {
          required_error: `${t('form:requiredField')}`
        }
      )
      .nullish()
      .refine(obj => !!obj?.value, {
        message: `${t('form:requiredField')}`
      }),
    city: z.nullable(z.string()).optional(),
    zipCode: z.nullable(z.string()).optional()
  })
}

export default schemaAddCompanyLocationForm
