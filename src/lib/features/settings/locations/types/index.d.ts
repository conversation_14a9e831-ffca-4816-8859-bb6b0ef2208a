import type { ISelectOption } from '~/core/ui/Select'

export interface LocationForm {
  id?: string
  name?: string
  address?: string
  headquarter?: boolean
  country?: ISelectOptionsLocation
  countryCode?: string
  countryId?: string
  state?: ISelectOptionsLocation | null
  city?: string
  zipCode?: string
  countryStateId?: number
}
export type IAddLocationForm = LocationForm
export type IEditLocationForm = LocationForm

export interface ILocation {
  id?: string
  name?: string
  address?: string | null
  headquarter?: boolean
  country?: string
  countryCode?: string | null
  countryId?: number
  state?: string
  city?: string
  zipCode?: string
  openJobsCount?: number
  jobLocationsCount?: number
  countryStateId?: number
  countryState?: {
    country?: {
      id: string
    }
  }
  transferLocationId?: ISelectOption
}

export interface IAgencyLocation {
  id?: string
  address?: string
  zipCode?: string
  country?: string
  state?: string
  city?: string
}

export interface ISelectOptionsLocation {
  id?: string
  value?: string

  label?: string
  supportingObj: {
    name?: string
    countryCode?: string | null
  }
}
