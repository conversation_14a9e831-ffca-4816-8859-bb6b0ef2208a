import type { TFunction } from 'i18next'
import { z } from 'zod'

import { regexInputUrlValidation } from '~/core/utilities/common'

const schemaWorkspaceForm = (t: TFunction) => {
  return z.object({
    name: z
      .string()
      .min(1, {
        message: `${t('form:requiredField')}`
      })
      .max(100, {
        message: `${t('form:field_max_number_required', { number: 100 })}`
      }),
    logo: z.any(),
    phoneNumber: z
      .string()
      .min(1, {
        message: `${t('form:requiredField')}`
      })
      .max(15, {
        message: `${t('form:invalid_phone_number', { number: 15 })}`
      }),
    companySize: z.string().optional(),
    language: z
      .string({
        required_error: `${t('form:requiredField')}`
      })
      .min(1, { message: `${t('form:requiredField')}` }),
    currency: z.string().optional(),
    workspaceUrl: z
      .string({ required_error: `${t('form:requiredField')}` })
      .min(1, {
        message: `${t('form:requiredField')}`
      })
      .max(250, {
        message: `${t('form:field_max_number_required', { number: 250 })}`
      })
    // .refine((value) => regexInputUrlValidation.test(value), {
    //   message: `${t('form:invalid_input_url')}`
    // })
  })
}

export default schemaWorkspaceForm
