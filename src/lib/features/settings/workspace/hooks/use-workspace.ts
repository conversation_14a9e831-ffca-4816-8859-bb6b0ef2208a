'use client'

import { useEffect } from 'react'

import useQueryGraphQL from '~/core/middleware/use-query-graphQL'

import useSkillSettingsStore from '~/lib/features/settings/skills/store'

import QueryTenantShow from '../graphql/query-tenant-show'
import type { IWorkspaceForm } from '../types'

const useWorkSpace = ({ shouldPause = false }: { shouldPause?: boolean }) => {
  const { data, trigger: fetchWorkspace } = useQueryGraphQL({
    query: QueryTenantShow,
    variables: {},
    shouldPause
  })

  const { setAllowAddNewSkill } = useSkillSettingsStore()

  useEffect(() => {
    if (data) {
      setAllowAddNewSkill(data.tenantShow.tenantSkillsMngtSettings?.enabling || false)
    }
  }, [data])

  return {
    tenantShow: data?.tenantShow as IWorkspaceForm,
    fetchWorkspace
  }
}

export default useWorkSpace
