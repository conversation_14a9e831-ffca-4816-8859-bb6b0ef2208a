import type { ILogoAndAvatarVariants } from '~/core/@types/global'

import type { ITermsAndConditions } from '../../careers/types'

export interface IWorkspaceForm {
  name?: string
  phoneNumber?: string
  companySize?: string
  companyKind?: string
  logo?: string
  logoVariants?: ILogoAndAvatarVariants
  description?: string
  careerSiteSettings?: {
    canonical_url?: string
    terms_and_conditions?: ITermsAndConditions
  }
  language?: string
  currency?: string
  workspaceUrl?: string
  domainInfo?: {
    status: string
    mapping_dns: string
    domain_url: string
    mapping_info?: {
      host?: string
      type?: string
      value?: string
    }
  }
  tenantSkillsMngtSettings?: {
    enabling?: boolean
  }
}
