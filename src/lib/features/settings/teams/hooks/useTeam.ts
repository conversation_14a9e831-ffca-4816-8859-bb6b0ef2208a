import { useState } from 'react'

import configuration from '~/configuration'
import { DEFAULT_MAX_PAGE_SIZE } from '~/core/constants/enum'
import type { IResponseContextResult } from '~/core/middleware/use-context-graphQL'
import useContextGraphQL from '~/core/middleware/use-context-graphQL'
import type { IPromiseSearchOption, ISelectOption } from '~/core/ui/Select'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'

import QueryTenantTeam from '../graphql/query-tenant-teams'
import type { ITeam } from '../types'

const useTeam = () => {
  const { clientGraphQL } = useContextGraphQL()
  const [countTeam, setCountTeam] = useState(0)

  const promiseTeamOptions = (params = {} as IPromiseSearchOption & { parentId?: number }) =>
    new Promise<any>(resolve => {
      clientGraphQL
        .query(QueryTenantTeam, params)
        .toPromise()
        .then((result: IResponseContextResult<ITeam>) => {
          if (result.error) {
            catchErrorFromGraphQL({
              error: result.error
            })
            resolve({
              metadata: {
                totalCount: configuration.defaultAsyncLoadingOptions
              },
              collection: []
            })
          }

          const { tenantTeamsList } = result.data
          const collection = tenantTeamsList?.collection || []
          const metadata = tenantTeamsList?.metadata || { totalCount: 0 }

          const cloneData = collection.map((item: ITeam) => {
            return {
              value: item.id,
              supportingObj: {
                name: item.name
              }
            }
          })
          setCountTeam(cloneData?.length)
          return resolve({ metadata, collection: cloneData })
        })
    })

  const promiseTeamsMultiLevelOptions = (params = {} as IPromiseSearchOption) =>
    new Promise<any>(resolve => {
      clientGraphQL
        .query(QueryTenantTeam, { ...params, limit: DEFAULT_MAX_PAGE_SIZE })
        .toPromise()
        .then((result: IResponseContextResult<ITeam>) => {
          if (result.error) {
            catchErrorFromGraphQL({
              error: result.error
            })
            resolve({
              metadata: {
                totalCount: configuration.defaultAsyncLoadingOptions
              },
              collection: []
            })
          }
          const { tenantTeamsList } = result.data
          const collection = tenantTeamsList?.collection || []
          const metadata = tenantTeamsList?.metadata || { totalCount: 0 }

          const cloneData = collection.map((item: ITeam) => {
            return {
              id: item.id,
              name: item.name,
              membersCount: item.membersCount,
              parentId: item.parentId,
              subordinates: item.subordinates
            }
          })

          const newCloneData = [] as Array<ISelectOption>
          cloneData.forEach(item => {
            newCloneData.push({
              value: String(item.id),
              parentId: undefined,
              supportingObj: {
                name: item.name || ''
              },
              subordinates: item.subordinates
            })

            if (item.subordinates?.length) {
              item.subordinates.forEach(sub => {
                newCloneData.push({
                  value: String(sub.id),
                  parentId: String(item.id),
                  supportingObj: {
                    name: sub.name || ''
                  }
                })
              })
            }
          })
          return resolve({ metadata, collection: newCloneData })
        })
    })

  return {
    promiseTeamOptions,
    promiseTeamsMultiLevelOptions,
    countTeam
  }
}
export default useTeam
