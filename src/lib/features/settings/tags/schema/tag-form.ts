import type { TFunction } from 'i18next'
import { z } from 'zod'

const schemaTagForm = (t: TFunction) => {
  return z.object({
    id: z.any().optional(),
    name: z
      .string()
      .trim()
      .min(1, {
        message: `${t('form:requiredField')}`
      })
      .max(30, {
        message: `${t('form:field_max_number_required', { number: 30 })}`
      })
  })
}

export default schemaTagForm
