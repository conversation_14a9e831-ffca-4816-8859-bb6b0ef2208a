import { create } from 'zustand'

import type { IResume } from '../types'

export type ResumeStore = {
  resumeData: IResume
  setResume: (resume?: any) => void
  setResumePermittedFields: (resume?: any) => void
}

export const useResumeStore = create<ResumeStore>()(set => ({
  resumeData: undefined as unknown as IResume,
  setResume: (resumeData?: any) =>
    set(state => ({
      ...state,
      resumeData: resumeData === undefined ? undefined : { ...state.resumeData, ...resumeData }
    })),
  setResumePermittedFields: (resumeData?: any) =>
    set(state => ({
      ...state,
      resumeData:
        resumeData === undefined
          ? undefined
          : {
              ...state.resumeData,
              permittedFields: {
                ...state.resumeData?.permittedFields,
                ...resumeData
              }
            }
    }))
}))
