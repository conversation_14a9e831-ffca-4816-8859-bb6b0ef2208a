import type { TFunction } from 'next-i18next'
import { z } from 'zod'

import { removeHTMLTags } from '../utilities'

const schemaSummaryProfileForm = (t: TFunction) => {
  return z.object({
    summary: z
      .string()
      .refine(async content => removeHTMLTags(content || '').length <= 300000, {
        message: `${t('form:field_max_number_required', {
          number: 300000
        })}`
      })
      .optional()
  })
}

export default schemaSummaryProfileForm
