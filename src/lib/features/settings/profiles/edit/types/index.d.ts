import type { ISelectOption } from '~/core/ui/Select'

import type { CertificatesType, EducationsType, ICandidateProfile, ReferencesType } from '~/lib/features/candidates/types'
import { LanguagesType } from '~/lib/features/candidates/types'

export interface IResume extends ICandidateProfile {
  cvTemplateSections: ISectionParamType
}

export interface ISectionCustomFieldParamType {
  name?: string
  key?: string
  customRelatedFields?: {
    [key: string]: boolean
  }
  cvTemplateCustomFields?: Array<{
    customSettingId?: string
    key?: string
    name?: string
    isCustom?: boolean
    fieldKind?: string
    fieldName?: string
    selectOptions?: JSON
    visibility?: boolean
    visibleToEmployeeProfile?: boolean
    customRelatedFields?: {
      [key: string]: boolean
    }
  }>
}

export type ISectionParamType = {
  isDefault?: boolean // param only apply for FE
  templateName?: string // param only apply for FE
  templateGuideline?: string // param only apply for FE
  templateNameEnabling?: boolean
  dateEnabling?: boolean
  profileIdEnabling?: boolean
  fullnameEnabling?: boolean
  avatarEnabling?: boolean
  logoEnabling?: boolean
  emailEnabling?: boolean
  phoneNumberEnabling?: boolean
  watermarkEnabling?: boolean
  templateStyle: string
  cvTemplateSections: Array<ISectionCustomFieldParamType>
}

export interface IWorkExperienceParamType {
  company?: string
  createdAt?: string
  currentWorking?: boolean
  description?: string
  from?: string
  fromMonth?: string
  fromYear?: string
  id: number
  location?: ISelectOption
  countryStateId?: string
  position?: string
  profileId: number
  tenantId?: number
  title?: string
  to?: string
  toMonth?: string
  toYear?: string
  updatedAt?: string
  validate?: {
    [key: string]: boolean
  }
}

export interface IEducationParamType extends EducationsType {
  createdAt?: string
  degree?: string
  degreeSubject?: string
  description?: string
  id: number
  position?: string
  profileId: number
  schoolName?: string
  tenantId?: number
  updatedAt?: string
  from: {
    month?: string
    year?: string
  }
  to: {
    month?: string
    year?: string
  }
  validate?: {
    [key: string]: boolean
  }
}

export interface ICertificateParamType extends CertificatesType {
  validate?: {
    [key: string]: boolean
  }
}

export interface IReferenceParamType extends ReferencesType {
  validate?: {
    [key: string]: boolean
  }
}

export interface ILanguageParamType {
  id?: number // only for FE
  index?: number
  language?: ISelectOption
  proficiency?: ISelectOption
  validate?: {
    [key: string]: boolean
  }
}

export interface ILinkParamType {
  [key: string]: Array<string>
}

export interface ICompletionSection {
  id: string
  percent?: number
  name: string
  total: number
  current: number
}
