import { useTranslation } from 'react-i18next'

import { formatDateValue } from '~/core/utilities/common'

import type { EducationsType, WorkExperiencesType } from '../../../../candidates/types'
import { mappingCustomFieldKind } from '../../../profile-fields/mapping/custom-field-mapping'
import type { CustomFieldViewType } from '../../../profile-fields/types/custom-field'
import type { ICompletionSection, IResume, ISectionCustomFieldParamType } from '../types'
import { checkShowSectionName } from '../utilities'
import { LIST_SECTIONS_DEFAULT, LIST_SECTIONS_FIELD_DEFAULT } from '../utilities/enum'

const useResumeCompletion = (params: { resumeData?: IResume; customFieldViewData?: CustomFieldViewType[] }) => {
  const { resumeData, customFieldViewData } = params
  const { t } = useTranslation()
  const columns = resumeData?.cvTemplateSections?.cvTemplateSections || []
  const completions: ICompletionSection[] = []
  let currentPercent = 0
  let totalPercent = 0

  const formatSectionNameByKey = (key: string) => {
    if (key === LIST_SECTIONS_DEFAULT.ContactDetails) {
      return `${t('candidates:tabs:candidateOverview:contactDetails:heading')}`
    }
    if (key === LIST_SECTIONS_DEFAULT.ProfileInformation) {
      return `${t('candidates:tabs:candidateOverview:profileInformation:heading')}`
    }
    if (key === LIST_SECTIONS_DEFAULT.Summary) {
      return `${t('candidates:tabs:candidateOverview:summary:heading')}`
    }
    if (key === LIST_SECTIONS_DEFAULT.WorkExperiences) {
      return `${t('candidates:tabs:candidateOverview:workExperiences:heading')}`
    }
    if (key === LIST_SECTIONS_DEFAULT.Educations) {
      return `${t('candidates:tabs:candidateOverview:education:heading')}`
    }
    if (key === LIST_SECTIONS_DEFAULT.Certificates) {
      return `${t('candidates:tabs:candidateOverview:certificates:heading')}`
    }
    if (key === LIST_SECTIONS_DEFAULT.References) {
      return `${t('candidates:tabs:candidateOverview:references:heading')}`
    }

    return ''
  }

  const formatTotalByKey = (column: ISectionCustomFieldParamType, key: string) => {
    if (key === LIST_SECTIONS_DEFAULT.Summary && resumeData?.permittedFields?.summary) {
      return resumeData?.permittedFields?.summary?.value ? 1 : 0
    }
    if (key === LIST_SECTIONS_DEFAULT.WorkExperiences && resumeData?.permittedFields?.workExperiences) {
      return resumeData?.permittedFields?.workExperiences?.value?.length
    }
    if (key === LIST_SECTIONS_DEFAULT.Educations && resumeData?.permittedFields?.educations) {
      return resumeData?.permittedFields?.educations?.value?.length
    }
    if (key === LIST_SECTIONS_DEFAULT.Certificates && resumeData?.permittedFields?.certificates) {
      return resumeData?.permittedFields?.certificates?.value?.length
    }
    if (key === LIST_SECTIONS_DEFAULT.References && resumeData?.permittedFields?.references) {
      return resumeData?.permittedFields?.references?.value?.length
    }

    if (key === LIST_SECTIONS_DEFAULT.ProfileInformation) {
      let totalField = 0

      for (const field of column.cvTemplateCustomFields || []) {
        if (field.key === LIST_SECTIONS_FIELD_DEFAULT.customField) {
          const mappingsField = (customFieldViewData || [])?.filter(item => item.visibleToEmployeeProfile && item.visibility)

          totalField += mappingsField?.length
        } else {
          if (field.key === LIST_SECTIONS_FIELD_DEFAULT.publicId) totalField += 1
          if (field.key === LIST_SECTIONS_FIELD_DEFAULT.fullName) totalField += 1
          if (field.key === LIST_SECTIONS_FIELD_DEFAULT.email) totalField += 1
          if (field.key === LIST_SECTIONS_FIELD_DEFAULT.phoneNumber) totalField += 1
          if (field.key === LIST_SECTIONS_FIELD_DEFAULT.location && resumeData?.permittedFields?.location) totalField += 1
          if (field.key === LIST_SECTIONS_FIELD_DEFAULT.links && resumeData?.permittedFields?.links) totalField += 1
          if (field.key === LIST_SECTIONS_FIELD_DEFAULT.headline && resumeData?.permittedFields?.headline) totalField += 1
          if (field.key === LIST_SECTIONS_FIELD_DEFAULT.totalYearsOfExp && resumeData?.permittedFields?.totalYearsOfExp) totalField += 1
          if (field.key === LIST_SECTIONS_FIELD_DEFAULT.skills && resumeData?.permittedFields?.skills) totalField += 1
          if (field.key === LIST_SECTIONS_FIELD_DEFAULT.languages && resumeData?.permittedFields?.languages) totalField += 1
          if (field.key === LIST_SECTIONS_FIELD_DEFAULT.currentSalary && resumeData?.permittedFields?.currentSalary) totalField += 1
          if (field.key === LIST_SECTIONS_FIELD_DEFAULT.expectedSalary && resumeData?.permittedFields?.expectedSalary) totalField += 1
          if (field.key === LIST_SECTIONS_FIELD_DEFAULT.birthday && resumeData?.permittedFields?.birthday) totalField += 1
          if (field.key === LIST_SECTIONS_FIELD_DEFAULT.profileLevel && resumeData?.permittedFields?.profileLevel) totalField += 1
          if (field.key === LIST_SECTIONS_FIELD_DEFAULT.nationality && resumeData?.permittedFields?.nationality) totalField += 1
          if (field.key === LIST_SECTIONS_FIELD_DEFAULT.willingToRelocate && resumeData?.permittedFields?.willingToRelocate) totalField += 1
          if (field.key === LIST_SECTIONS_FIELD_DEFAULT.noticeToPeriodDays && resumeData?.permittedFields?.noticeToPeriodDays) totalField += 1
          if (field.key === LIST_SECTIONS_FIELD_DEFAULT.talentPools && resumeData?.permittedFields?.talentPools) totalField += 1
        }
      }

      return totalField
    }

    let total = 0
    for (const field of column.cvTemplateCustomFields || []) {
      if (field.isCustom) {
        total += field.visibility && field.visibleToEmployeeProfile ? 1 : 0
      } else {
        if (field.key === LIST_SECTIONS_FIELD_DEFAULT.publicId) total += 1
        if (field.key === LIST_SECTIONS_FIELD_DEFAULT.fullName) total += 1
        if (field.key === LIST_SECTIONS_FIELD_DEFAULT.email) total += 1
        if (field.key === LIST_SECTIONS_FIELD_DEFAULT.phoneNumber) total += 1
        if (field.key === LIST_SECTIONS_FIELD_DEFAULT.location && resumeData?.permittedFields?.location) total += 1
        if (field.key === LIST_SECTIONS_FIELD_DEFAULT.links && resumeData?.permittedFields?.links) total += 1
        if (field.key === LIST_SECTIONS_FIELD_DEFAULT.headline && resumeData?.permittedFields?.headline) total += 1
        if (field.key === LIST_SECTIONS_FIELD_DEFAULT.totalYearsOfExp && resumeData?.permittedFields?.totalYearsOfExp) total += 1
        if (field.key === LIST_SECTIONS_FIELD_DEFAULT.skills && resumeData?.permittedFields?.skills) total += 1
        if (field.key === LIST_SECTIONS_FIELD_DEFAULT.languages && resumeData?.permittedFields?.languages) total += 1
        if (field.key === LIST_SECTIONS_FIELD_DEFAULT.currentSalary && resumeData?.permittedFields?.currentSalary) total += 1
        if (field.key === LIST_SECTIONS_FIELD_DEFAULT.expectedSalary && resumeData?.permittedFields?.expectedSalary) total += 1
        if (field.key === LIST_SECTIONS_FIELD_DEFAULT.birthday && resumeData?.permittedFields?.birthday) total += 1
        if (field.key === LIST_SECTIONS_FIELD_DEFAULT.profileLevel && resumeData?.permittedFields?.profileLevel) total += 1
        if (field.key === LIST_SECTIONS_FIELD_DEFAULT.nationality && resumeData?.permittedFields?.nationality) total += 1
        if (field.key === LIST_SECTIONS_FIELD_DEFAULT.willingToRelocate && resumeData?.permittedFields?.willingToRelocate) total += 1
        if (field.key === LIST_SECTIONS_FIELD_DEFAULT.noticeToPeriodDays && resumeData?.permittedFields?.noticeToPeriodDays) total += 1
        if (field.key === LIST_SECTIONS_FIELD_DEFAULT.talentPools && resumeData?.permittedFields?.talentPools) total += 1
      }
    }
    return total
  }

  const isShowProfileSystemField = (key: string) => {
    if (key === LIST_SECTIONS_DEFAULT.Summary && resumeData?.permittedFields?.summary) {
      return true
    }
    if (key === LIST_SECTIONS_DEFAULT.WorkExperiences && resumeData?.permittedFields?.workExperiences) {
      return true
    }
    if (key === LIST_SECTIONS_DEFAULT.Educations && resumeData?.permittedFields?.educations) {
      return true
    }
    if (key === LIST_SECTIONS_DEFAULT.Certificates && resumeData?.permittedFields?.certificates) {
      return true
    }
    if (key === LIST_SECTIONS_DEFAULT.References && resumeData?.permittedFields?.references) {
      return true
    }

    return false
  }

  const formatCurrentByKey = (column: ISectionCustomFieldParamType, key: string) => {
    if (key === LIST_SECTIONS_DEFAULT.Summary && resumeData?.permittedFields?.summary) {
      return resumeData?.permittedFields?.summary?.value ? 1 : 0
    }
    if (key === LIST_SECTIONS_DEFAULT.WorkExperiences && resumeData?.permittedFields?.workExperiences) {
      return ((resumeData?.permittedFields?.workExperiences?.value as WorkExperiencesType[]) || []).filter((item: WorkExperiencesType) => item.id)
        .length
    }
    if (key === LIST_SECTIONS_DEFAULT.Educations && resumeData?.permittedFields?.educations) {
      return ((resumeData?.permittedFields?.educations?.value as EducationsType[]) || []).filter((item: EducationsType) => item.id).length
    }
    if (key === LIST_SECTIONS_DEFAULT.Certificates && resumeData?.permittedFields?.certificates) {
      return (resumeData?.permittedFields?.certificates?.value || []).filter(item => item.id).length
    }
    if (key === LIST_SECTIONS_DEFAULT.References && resumeData?.permittedFields?.references) {
      return (resumeData?.permittedFields?.references?.value || []).filter(item => item.email && item.email).length
    }

    let total = 0
    for (const field of column.cvTemplateCustomFields || []) {
      if (field.key === LIST_SECTIONS_FIELD_DEFAULT.customField) {
        const mappingsField = (customFieldViewData || [])?.filter(item => item.visibleToEmployeeProfile && item.visibility)
        for (const field of mappingsField) {
          const value = !['select', 'multiple'].includes(field.type)
            ? resumeData?.customFields?.find(item => String(item.customSettingId) === String(field.id))?.value
            : resumeData?.customFields?.find(item => String(item.customSettingId) === String(field.id))?.selectedOptionKeys

          if (['select', 'multiple'].includes(field.type)) {
            if (typeof value === 'object') {
              const filter = (value || [])?.filter(item => item)
              total += filter?.length ? 1 : 0
            }
          } else {
            if (field.type === 'date') {
              const formatValue = formatDateValue({
                value
              })
              total += formatValue ? 1 : 0
            } else {
              total += value ? 1 : 0
            }
          }
        }
      } else {
        if (field.isCustom) {
          const value = !['select', 'multiple'].includes(mappingCustomFieldKind(field.fieldKind || 'text'))
            ? resumeData?.customFields?.find(item => String(item.customSettingId) === String(field.customSettingId))?.value
            : resumeData?.customFields?.find(item => String(item.customSettingId) === String(field.customSettingId))?.selectedOptionKeys

          if (['select', 'multiple'].includes(mappingCustomFieldKind(field.fieldKind || 'text'))) {
            if (typeof value === 'object') {
              const filter = (value || [])?.filter(item => item)
              total += filter?.length ? 1 : 0
            }
          } else {
            if (mappingCustomFieldKind(field.fieldKind || 'text') === 'date') {
              const formatValue = formatDateValue({
                value
              })
              total += formatValue ? 1 : 0
            } else {
              total += value ? 1 : 0
            }
          }
        } else {
          if (field.key === LIST_SECTIONS_FIELD_DEFAULT.publicId && resumeData?.permittedFields?.publicId) {
            total += resumeData?.permittedFields?.publicId?.value ? 1 : 0
          }

          if (field.key === LIST_SECTIONS_FIELD_DEFAULT.fullName && resumeData?.permittedFields?.fullName) {
            total += resumeData?.permittedFields?.fullName?.value ? 1 : 0
          }

          if (field.key === LIST_SECTIONS_FIELD_DEFAULT.email && resumeData?.permittedFields?.email) {
            total += resumeData?.permittedFields?.email?.value?.length ? 1 : 0
          }

          if (field.key === LIST_SECTIONS_FIELD_DEFAULT.phoneNumber && resumeData?.permittedFields?.phoneNumber) {
            total += resumeData?.permittedFields?.phoneNumber?.value?.length ? 1 : 0
          }

          if (field.key === LIST_SECTIONS_FIELD_DEFAULT.location && resumeData?.permittedFields?.location) {
            total += resumeData?.permittedFields?.location?.value ? 1 : 0
          }

          if (field.key === LIST_SECTIONS_FIELD_DEFAULT.links && resumeData?.permittedFields?.links) {
            total += Object.values(resumeData?.permittedFields?.links?.value || {}).flat().length ? 1 : 0
          }

          if (field.key === LIST_SECTIONS_FIELD_DEFAULT.headline && resumeData?.permittedFields?.headline) {
            total += resumeData?.permittedFields?.headline?.value ? 1 : 0
          }

          if (field.key === LIST_SECTIONS_FIELD_DEFAULT.totalYearsOfExp && resumeData?.permittedFields?.totalYearsOfExp) {
            total += String(resumeData?.permittedFields?.totalYearsOfExp?.value) !== 'null' ? 1 : 0
          }

          if (field.key === LIST_SECTIONS_FIELD_DEFAULT.skills && resumeData?.permittedFields?.skills) {
            total += (resumeData?.permittedFields?.skills?.value || [])?.length ? 1 : 0
          }

          if (field.key === LIST_SECTIONS_FIELD_DEFAULT.languages && resumeData?.permittedFields?.languages) {
            total += resumeData?.permittedFields?.languages?.value?.length ? 1 : 0
          }

          if (field.key === LIST_SECTIONS_FIELD_DEFAULT.currentSalary && resumeData?.permittedFields?.currentSalary) {
            total += Number(resumeData?.permittedFields?.currentSalary?.value) > 0 ? 1 : 0
          }

          if (field.key === LIST_SECTIONS_FIELD_DEFAULT.expectedSalary && resumeData?.permittedFields?.expectedSalary) {
            total += Number(resumeData?.permittedFields?.expectedSalary?.value) > 0 ? 1 : 0
          }

          if (field.key === LIST_SECTIONS_FIELD_DEFAULT.birthday && resumeData?.permittedFields?.birthday) {
            total += resumeData?.permittedFields?.birthday?.value?.year ? 1 : 0
          }

          if (field.key === LIST_SECTIONS_FIELD_DEFAULT.profileLevel && resumeData?.permittedFields?.profileLevel) {
            total += resumeData?.permittedFields?.profileLevel?.value ? 1 : 0
          }

          if (field.key === LIST_SECTIONS_FIELD_DEFAULT.nationality && resumeData?.permittedFields?.nationality) {
            total += resumeData?.permittedFields?.nationality?.value ? 1 : 0
          }

          if (field.key === LIST_SECTIONS_FIELD_DEFAULT.willingToRelocate && resumeData?.permittedFields?.willingToRelocate) {
            total += resumeData?.permittedFields?.willingToRelocate?.value ? 1 : 0
          }

          if (field.key === LIST_SECTIONS_FIELD_DEFAULT.noticeToPeriodDays && resumeData?.permittedFields?.noticeToPeriodDays) {
            total += resumeData?.permittedFields?.noticeToPeriodDays?.value ? 1 : 0
          }

          if (field.key === LIST_SECTIONS_FIELD_DEFAULT.talentPools && resumeData?.permittedFields?.talentPools) {
            total += (resumeData?.permittedFields?.talentPools?.value || [])?.length ? 1 : 0
          }
        }
      }
    }

    return total
  }

  const mappingsField = (customFieldViewData || [])?.filter(item => item.visibleToEmployeeProfile && item.visibility)
  for (const [index, column] of columns.entries()) {
    if ([LIST_SECTIONS_DEFAULT.ContactDetails, LIST_SECTIONS_DEFAULT.ProfileInformation].includes(column.key || '')) {
      if (
        checkShowSectionName({
          section: column,
          resumeData,
          mappingsField
        })
      ) {
        const total = Number(formatTotalByKey(column, String(column.key))) || 0
        const current = Number(formatCurrentByKey(column, String(column.key))) || 0

        if (total) {
          totalPercent += total || 1
          currentPercent += current
          completions.push({
            id: `section-completion-${index}`,
            name: column.key ? formatSectionNameByKey(column.key) : String(column.name),
            total,
            current,
            percent: Math.round((current / total) * 100) || 0
          })
        }
      }
    } else {
      if (column.key) {
        if (isShowProfileSystemField(column.key || '')) {
          const total = Number(formatTotalByKey(column, String(column.key))) || 0
          const current = Number(formatCurrentByKey(column, String(column.key))) || 0

          totalPercent += total || 1
          currentPercent += current
          completions.push({
            id: `section-completion-${index}`,
            name: column.key ? formatSectionNameByKey(column.key) : String(column.name),
            total,
            current,
            percent: Math.round((current / total) * 100) || 0
          })
        }
      } else {
        if (
          checkShowSectionName({
            section: column,
            resumeData,
            mappingsField
          })
        ) {
          const total = Number(formatTotalByKey(column, String(column.key))) || 0
          const current = Number(formatCurrentByKey(column, String(column.key))) || 0

          totalPercent += total || 1
          currentPercent += current
          completions.push({
            id: `section-completion-${index}`,
            name: column.key ? formatSectionNameByKey(column.key) : String(column.name),
            total,
            current,
            percent: Math.round((current / total) * 100) || 0
          })
        }
      }
    }
  }

  return {
    completions,
    percent: Math.round((currentPercent / totalPercent) * 100) || 0
  }
}

export default useResumeCompletion
