import type { TFunction } from 'i18next'

import configuration from '~/configuration'
import type { IPromiseSearchOption } from '~/core/@types/global'
import { DEFAULT_LOADING_PAGE_SIZE } from '~/core/constants/enum'
import type { IResponseContextResult } from '~/core/middleware/use-context-graphQL'
import useContextGraphQL from '~/core/middleware/use-context-graphQL'
import useQueryGraphQL from '~/core/middleware/use-query-graphQL'
import { isInCareerHubApp } from '~/core/utilities/common'

import MutationAgencyProfileUpdate from '~/lib/features/candidates/graphql/mutation-agency-update-profile'
import MutationProfileUpdate from '~/lib/features/candidates/graphql/mutation-update-profile'
import MutationCHubProfileUpdate from '~/lib/features/career-hub/graphql/mutation-chub-update-profile'
import { useSubmitCommon } from '~/lib/hooks/use-submit-graphql-common'
import { useRouter<PERSON>ontext } from '~/lib/next/use-router-context'

import QueryProfileTemplatesList from '../../../profile-templates/graphql/query-profile-templates-list'
import type { IProfileTemplate } from '../../../profile-templates/types'
import { PROFILE_TEMPLATE_PREVIEW_STYLE } from '../utilities/enum'
import { useQueryGetResume } from './use-query-get-resume'

const useResumes = ({ employeeId, isCompanyKind, t }: { employeeId?: number; isCompanyKind?: boolean; t?: TFunction }) => {
  const { clientGraphQL } = useContextGraphQL()
  const { asPath } = useRouterContext()

  const { data: resumeData, trigger: triggerGetResume } = useQueryGetResume({
    variables: { employeeId },
    shouldPause: true
  })

  const { data: dataCvTemplatesList, trigger: triggerGetCvTemplatesList } = useQueryGraphQL({
    query: QueryProfileTemplatesList,
    variables: { limit: DEFAULT_LOADING_PAGE_SIZE, page: 1, search: '' },
    shouldPause: true
  })

  const promiseProfileTemplatesOptions = (params = {} as IPromiseSearchOption) =>
    new Promise<any>(resolve => {
      clientGraphQL
        .query(QueryProfileTemplatesList, params)
        .toPromise()
        .then((result: IResponseContextResult<IProfileTemplate>) => {
          if (result.error) {
            resolve({
              metadata: {
                totalCount: configuration.defaultAsyncLoadingOptions
              },
              collection: []
            })
          }

          const { cvTemplatesList } = result?.data
          const collection = cvTemplatesList?.collection || []
          const metadata = cvTemplatesList?.metadata || { totalCount: 0 }
          const cloneData = collection.map((item: IProfileTemplate) => {
            return {
              value: item.id,
              default: item.default,
              supportingObj: {
                name: item.name,
                shortDescription: item.templateStyle
              }
            }
          })

          if (params.page == 1) {
            cloneData.unshift({
              value: '0',
              default: false,
              supportingObj: {
                name: t ? `${t('label:defaultTemplate')}` : 'Full profile',
                shortDescription: PROFILE_TEMPLATE_PREVIEW_STYLE.default
              }
            })
          }
          return resolve({
            metadata,
            collection: cloneData
          })
        })
    })

  const { trigger: triggerUpdateProfile, isLoading } = useSubmitCommon(
    isInCareerHubApp(asPath) ? MutationCHubProfileUpdate : isCompanyKind ? MutationAgencyProfileUpdate : MutationProfileUpdate
  )

  return {
    triggerGetResume,
    resumeData,
    promiseProfileTemplatesOptions,
    triggerUpdateProfile,
    isLoading,
    cvTemplatesList: dataCvTemplatesList?.cvTemplatesList?.collection,
    triggerGetCvTemplatesList
  }
}

export default useResumes
