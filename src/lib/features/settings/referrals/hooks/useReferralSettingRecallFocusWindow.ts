'use client'

import { useCallback, useEffect, useState } from 'react'

import { AGENCY_TENANT } from '~/core/constants/enum'
import useContextGraphQL from '~/core/middleware/use-context-graphQL'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'
import { isInCareerHubApp } from '~/core/utilities/common'

import QueryTenantReferralCHubSettingShow from '~/lib/features/career-hub/graphql/query-tenant-referral-chub'
import useDetectCompanyWithKind from '~/lib/hooks/use-detect-company-with-kind'
import { useRouterContext } from '~/lib/next/use-router-context'

import QueryTenantReferralSettingShow from '../graphql/query-tenant-referral'
import QueryTenantReferralAgencySettingShow from '../graphql/query-tenant-referral-agency'
import type { ReferralSettingType } from '../types'

const useReferralSettingRecallFocusWindow = ({ suspend }: { suspend?: boolean } = {}) => {
  const { asPath } = useRouterContext()
  const { clientGraphQL } = useContextGraphQL()
  const [dataReferral, setDataReferral] = useState<ReferralSettingType>()
  const { isCompanyKind } = useDetectCompanyWithKind({
    kind: AGENCY_TENANT
  })

  const fetchReferralSettingShow = useCallback(() => {
    return clientGraphQL
      .query(
        isInCareerHubApp(asPath)
          ? QueryTenantReferralCHubSettingShow
          : isCompanyKind
            ? QueryTenantReferralAgencySettingShow
            : QueryTenantReferralSettingShow
      )
      .then((result: { error: { graphQLErrors: Array<object> }; data: { tenantReferralSettingShow: ReferralSettingType } }) => {
        if (result.error) {
          return catchErrorFromGraphQL({
            error: result.error,
            error404ShouldForceToNotFoundPage: true
          })
        }

        const { tenantReferralSettingShow } = result.data || []
        setDataReferral(tenantReferralSettingShow)
        return
      })
  }, [clientGraphQL])

  useEffect(() => {
    !suspend && fetchReferralSettingShow()

    window.addEventListener('focus', fetchReferralSettingShow)

    return () => {
      window.removeEventListener('focus', fetchReferralSettingShow)
    }
  }, [suspend])

  return { dataReferral, fetchReferralSettingShow }
}
export default useReferralSettingRecallFocusWindow
