import type { ILogoAndAvatarVariants } from '~/core/@types/global'
import type { ISelectOption } from '~/core/ui/Select'

import type { DepartmentItem } from '~/lib/features/careers/[id]/types'

export interface ReferralSettingType {
  id: number
  values: {
    enabling?: boolean
    email_notification?: boolean
    enabling_policy?: boolean
    policy_type?: string
    policy_info?: string
    policy_files?: File[]
    always_enable_referral?: boolean
    enabling_register_chub?: boolean
    protectDepartmentIds?: Array<number>
    referral_portal?: {
      job_only: boolean
      referral_job: boolean
      referral_only: boolean
    }
  }
  attachments: {
    file: string
    name: string
    blobs: {
      filename: string
      content_type: string
      size: number
    }
  }[]
  departments: Array<DepartmentItem>
  enablingRegisterChub: boolean
  chubRegistrationType: string
  restrictEmailDomains?: Array<string>
  loginMethods?: Array<string>
  description?: {
    enable: boolean
    content: string
  }
}
export interface IReferralSettingForm {
  id?: number
  enabling?: boolean
  emailNotification?: boolean
  enablingPolicy?: boolean
  policyType?: string
  policyInfo?: string
  policyFiles?: File[0]
  referralPortal?: string | {}
  alwaysEnableReferral?: boolean
  enablingRegisterChub?: boolean
  protectDepartmentIds?: ISelectOption[]
  departmentSelectAll?: boolean
  chubRegistrationType?: string
  restrictEmailDomains?: ISelectOption[]
  loginMethods?: Array<string>
  description?: {
    enable: boolean
    content: string
  }
}
export interface IReferralSettingSubmit {
  id?: number
  enabling?: boolean
  emailNotification?: boolean
  enablingPolicy?: boolean
  policyType?: string
  policyInfo?: string
  policyFiles?: File[0]
  referralPortal?: string | {}
  alwaysEnableReferral?: boolean
  enabledRegisterChub?: boolean
  protectDepartmentIds?: Array<number>
  chubRegistrationType?: string
  restrictEmailDomains?: Array<string>
  loginMethods?: Array<string>
  description?: {
    enable: boolean
    content: string
  }
}

export interface IMemberCHUB {
  id?: string
  email?: string
  fullName?: string
  avatarVariants?: ILogoAndAvatarVariants
  defaultColour?: string
  confirmed?: boolean
  role?: string
  roleName?: string
  roleId?: string
  roleCode?: string
  ownTenant?: boolean
  roles?: Array<{ id: string; name: string; code: string }>
  user?: {
    ownTenant?: boolean
    roles?: Array<{ id: string; name: string; code: string }>
  }
  departments: Array<{
    id: string
    name: string
    subordinates: Array<{
      id: string
      name: string
    }>
    parent: {
      id: string
      name: string
    }
  }>
  allDepartments: boolean
}

export type ClaimType = {
  id?: string
  profile?: {
    fullName: string
  }
  job?: {
    title: string
  }
  referral?: {
    user: {
      email: string
      fullName: string
      avatarVariants: ILogoAndAvatarVariants
      defaultColour: string
    }
  }
  hiredDate?: string
}
