import type { TFunction } from 'i18next'
import { z } from 'zod'

import { findInvalidEmails } from '../../members/utilities/common'

const schemaCHUBInviteForm = (t: TFunction) => {
  return z.object({
    email: z.array(
      z.object({
        value: z.any().superRefine((data, ctx) => {
          if (findInvalidEmails(data).length > 1) {
            ctx.addIssue({
              code: 'custom',
              message: `${t('form:invalidMultiEmail', {
                number: findInvalidEmails(data).length,
                value: findInvalidEmails(data)
              })}`
            })
          } else if (findInvalidEmails(data).length === 1) {
            ctx.addIssue({
              code: 'custom',
              message: `${t('form:invalid_email')}`
            })
          }
        })
      }),
      { required_error: `${t('form:requiredField')}` }
    ),
    teamDepartmentIds: z
      .array(
        z.object({
          value: z.string(),
          supportingObj: z.object({ name: z.string() }),
          subordinates: z.any()
        })
      )
      .optional(),
    teamDepartmentSelectAll: z.boolean().optional()
  })
}

export default schemaCHUBInviteForm
