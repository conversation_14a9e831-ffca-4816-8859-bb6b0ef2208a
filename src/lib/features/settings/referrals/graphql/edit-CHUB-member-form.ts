import type { TFunction } from 'i18next'
import { z } from 'zod'

const schemaCHUBEditForm = (t: TFunction) => {
  return z.object({
    teamDepartmentIds: z
      .array(
        z.object({
          value: z.string(),
          supportingObj: z.object({ name: z.string() }),
          subordinates: z.any()
        })
      )
      .optional(),
    teamDepartmentSelectAll: z.boolean().optional()
  })
}

export default schemaCHUBEditForm
