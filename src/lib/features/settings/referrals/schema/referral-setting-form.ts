import type { TFunction } from 'i18next'
import { z } from 'zod'

import { PUBLIC_LANDING_PAGE_URL } from '~/core/constants/env'
import { removeHTMLTags } from '~/core/utilities/common'

import { ACCEPTED_FILE_TYPES, convertFileSizeToBytes, MAX_FILE_SIZE, regexUrlValidation } from '~/lib/schema'

import { REGISTRATION_RESTRICT_EMAIL_DOMAIN } from '../utilities/enum'

const schemaReferralSettingForm = (t: TFunction) => {
  return z
    .object({
      enablingPolicy: z.boolean().optional(),
      policyType: z.string().optional(),
      policyInfo: z.string().optional(),
      policyFiles: z.string().optional().or(z.array(z.any())),
      referralPortal: z.string().optional(),
      alwaysEnableReferral: z.boolean().optional(),
      enablingRegisterChub: z.boolean().optional(),
      protectDepartmentIds: z.array(z.object({ value: z.string() })),
      chubRegistrationType: z.string(),
      restrictEmailDomains: z
        .array(
          z.object({
            value: z.string()
          })
        )
        .optional(),
      loginMethods: z.nullable(z.array(z.string())).refine(
        data => {
          return (data || []).length > 0
        },
        {
          message: `${t('form:atLeastOneMethod')}`
        }
      ),
      description: z
        .object({
          enable: z.boolean(),
          content: z.string().optional()
        })
        .refine(
          data => {
            if (!data.enable) {
              return true
            }

            return removeHTMLTags((data.content || '').trim()).length <= 200
          },
          {
            message: `${t('form:field_max_number_required', {
              number: 200
            })}`
          }
        )
    })
    .refine(
      data => {
        if (data.policyType === 'link' && data.enablingPolicy) {
          return (data.policyInfo?.trim() || '').length > 0
        } else return true
      },
      {
        message: `${t('form:requiredField')}`,
        path: ['policyInfo']
      }
    )
    .refine(
      data => {
        if (data.policyType === 'link' && data.enablingPolicy) {
          return regexUrlValidation.test(data?.policyInfo || '')
        } else return true
      },
      {
        path: ['policyInfo'],
        message: `${t('form:invalid_url_domain', {
          landingPage: PUBLIC_LANDING_PAGE_URL
        })}`
      }
    )
    .refine(
      data => {
        if (data.policyType === 'file' && data.enablingPolicy) {
          return (data.policyFiles || []).length > 0
        } else return true
      },
      {
        message: `${t('form:requiredField')}`,
        path: ['policyFiles']
      }
    )

    .refine(
      data => {
        if (data.policyType === 'file' && data.enablingPolicy) {
          return data.policyFiles?.[0]?.size <= convertFileSizeToBytes({ size: MAX_FILE_SIZE })
        } else return true
      },
      {
        message: `${t('referrals:referral_modal:form:wrong_upload_file_format')}`,
        path: ['policyFiles']
      }
    )
    .refine(
      data => {
        if (data.policyType === 'file' && data.enablingPolicy) {
          return ACCEPTED_FILE_TYPES.includes(data.policyFiles?.[0]?.type)
        } else return true
      },
      {
        message: `${t('referrals:referral_modal:form:wrong_upload_file_format')}`,
        path: ['policyFiles']
      }
    )
    .refine(
      data => {
        if (
          !!data.enablingRegisterChub &&
          data.chubRegistrationType === REGISTRATION_RESTRICT_EMAIL_DOMAIN &&
          (data.restrictEmailDomains || []).length === 0
        )
          return false
        return true
      },
      {
        path: ['chubRegistrationType'],
        message: `${t('form:enterAtLeastOneEmailDomain')}`
      }
    )
}

export default schemaReferralSettingForm
