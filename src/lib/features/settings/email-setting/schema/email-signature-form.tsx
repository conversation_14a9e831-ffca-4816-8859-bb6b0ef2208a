import type { TFunction } from 'i18next'
import { z } from 'zod'

const schemaEmailSignatureForm = (t: TFunction) => {
  return z.object({
    signature: z
      .string()
      .optional()
      .refine(
        data => {
          const parser = new DOMParser()
          const doc = parser.parseFromString(data || '', 'text/html')

          // Get all img elements
          const imgTags = doc.getElementsByTagName('img')

          return !data || imgTags.length <= 2
        },
        {
          message: `${t('form:maximum_upload_images', { num: 2 })}`
        }
      )
  })
}

export default schemaEmailSignatureForm
