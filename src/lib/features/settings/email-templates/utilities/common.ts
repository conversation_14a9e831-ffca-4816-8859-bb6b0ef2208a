import type { IEmailTemplate } from '../types'

export const checkDisableDeleteBtn = (emailTemplate: IEmailTemplate): boolean => {
  if ((emailTemplate?.createdBy === null || emailTemplate?.createdBy === undefined) && emailTemplate?.emailKind === 'auto_confirmation') {
    return true
  }
  if (emailTemplate?.default) {
    return true
  }
  return false
}

export const formatBodyContent = ({ forPresent, value }: { forPresent: boolean; value: string }) => {
  return forPresent
    ? value.replace(/{([^}]*)}/g, '<mark style="background-color: #EDF5FE"><span style="color: #5081F0">$1</span></mark>')
    : value.replace(/<span[^>]*>(.*?)<\/span>/g, '{$1}')

  // value
  //     .replace(/<mark\b[^>]*><span\b[^>]*>/g, '{')
  //     .replace(/<\/span><\/mark>/g, '}')
}
