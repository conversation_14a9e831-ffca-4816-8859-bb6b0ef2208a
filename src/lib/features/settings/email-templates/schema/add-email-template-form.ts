import type { TFunction } from 'i18next'
import { z } from 'zod'

import { removeHTMLTags } from '~/core/utilities/common'

const schemaAddEmailTemplateForm = (t: TFunction) => {
  return z.object({
    name: z
      .string()
      .trim()
      .min(1, {
        message: `${t('form:requiredField')}`
      })
      .max(200, {
        message: `${t('form:field_max_number_required', { number: 200 })}`
      })
      .refine(value => value.trim() !== '', {
        message: `${t('form:requiredField')}`
      }),
    subject: z
      .string()
      .trim()
      .min(1, {
        message: `${t('form:requiredField')}`
      })
      .max(250, {
        message: `${t('form:field_max_number_required', { number: 250 })}`
      })
      .refine(value => value.trim() !== '', {
        message: `${t('form:requiredField')}`
      }),
    body: z
      .string({ required_error: `${t('form:requiredField')}` })
      .min(1, {
        message: `${t('form:requiredField')}`
      })
      .refine(value => removeHTMLTags(value).trim() !== '', {
        message: `${t('form:requiredField')}`
      }),
    default: z.boolean().optional(),
    emailKind: z.string().min(1, {
      message: `${t('form:requiredField')}`
    })
  })
}

export default schemaAddEmailTemplateForm
