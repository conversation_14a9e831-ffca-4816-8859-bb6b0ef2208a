import type { TaskItemType } from '../types'

export const mappingTaskCandidateList = (pages: Array<{ tasksList: { collection: Array<TaskItemType> } }>) => {
  return pages
    .map(page =>
      page.tasksList?.collection?.reduce<{ list: Array<TaskItemType> }>(
        (groupingData, task) => {
          groupingData['list'].push(task)

          return groupingData
        },
        { list: [] }
      )
    )
    .reduce<{ list: Array<TaskItemType> }>(
      (mergeData, groupingData) => {
        return {
          list: [...mergeData.list, ...groupingData.list]
        }
      },
      {
        list: []
      }
    )
}
