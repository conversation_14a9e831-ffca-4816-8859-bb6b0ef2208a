import type { ILogoAndAvatarVariants } from '~/core/@types/global'
import type { ISelectOption } from '~/core/ui/Select'

import type { ICompanyPermittedFields } from '../../agency/companies/types'

export interface ITasksManagementFilter {
  page?: number
  tab?: string
  profileId?: number
  filterBy?: string | null
  dueDateExisting?: boolean
  fromDatetime?: string
  toDatetime?: string
  assigneesIds?: Array<ISelectOption>
}

export interface TaskFormType {
  title?: string
  assigneeIds?: Array<ISelectOption>
  dueDate?: Date
  id?: number
  relatedIds?: ISelectOption
  tab?: string
}

export interface TaskItemType {
  id: number
  title: string
  dueDate: string
  status: string
  comments?: {
    content: string
  }[]
  applicant?: {
    id: number
    profile: {
      id: number
      fullName: string
      avatarVariants: ILogoAndAvatarVariants
      email: string
    }
  }
  profile: {
    id: number
    fullName: string
    avatarVariants?: ILogoAndAvatarVariants
    email: string
  }
  applicantId?: number
  assignees: Array<{
    id: number
    fullName: string
    avatarVariants: ILogoAndAvatarVariants
    defaultColour: string
    roles: Array<{
      id: number
      name: string
    }>
    email: string
  }>
  company?: {
    id: number
    permittedFields?: ICompanyPermittedFields
    logoVariants: ILogoAndAvatarVariants
  }
  creator?: {
    id: number
    avatarVariants: ILogoAndAvatarVariants
    fullName: string
    defaultColour: string
    email: string
  }
  applicant?: {
    id?: number
    profile?: {
      id: number
      fullName: string
      avatarVariants?: ILogoAndAvatarVariants
      email: string
    }
  }
}

export interface TaskItemMutationResponse {
  task: TaskItemType
}

export interface TaskListGrouping {
  upcoming: Array<TaskItemType>
  today: Array<TaskItemType>
  overdue: Array<TaskItemType>
  tomorrow: Array<TaskItemType>
}
export interface TaskActions {
  taskDeleteAction: {
    deleteTask: (args: { id: number; profileId?: number }) => Promise<OperationResult<{ success: boolean }, AnyVariables>>
    deletingTask: boolean
  }
  updateTaskAction: {
    updateTask: (args: {
      id: number
      title?: string
      dueDate?: string
      profileId?: number | null
      companyId?: number | null
      assigneeIds?: Array<number>
    }) => Promise<OperationResult<TaskItemMutationResponse, AnyVariables>>
    updatingTask: boolean
    updateTaskStatus: (args: {
      id: number
      title?: string
      status?: string
      assigneeIds?: Array<number>
    }) => Promise<OperationResult<TaskItemMutationResponse, AnyVariables>>
  }
}

export interface TaskFormInActiveType {
  type?: 'edit' | 'add'
  taskId?: number
}

export interface FilterAssigneesListType {
  search?: string
  page?: number
  limit?: number
  profileId?: number
  applicantId?: number
}

export interface AssigneeType {
  id: number
  fullName: string
  avatarVariants?: ILogoAndAvatarVariants
  defaultColour: string
  roles?: Array<{
    id?: number
    name?: string
  }>
}
export type TaskEditModalProps = {
  open: boolean
  task?: TaskItemType
  onCloseEditModal?: () => void
  onReopenEditModal?: () => void
  callBack?: () => void
}
