import { endOfDay, startOfDay } from 'date-fns'
import type { TFunction } from 'next-i18next'

import { TAB_TEAMS } from '~/core/constants/enum'
import type { IFunnelChartDataProps } from '~/core/ui/FunnelChart'
import type { IPieChartDataProps } from '~/core/ui/PieChart'

import { addTzToDate, changeTimezone } from '../../calendar/utilities/helper-schedule-interview'
import type { IReportManagementFilter } from '../types'
import {
  DIRECT_REFER,
  MAPPING_DATA_TYPE_FUNNEL_CHART,
  MAPPING_DATA_TYPE_PIE_CHART,
  METRIC_REPORT_DATA_TYPE,
  REPORT_FILTER_SHOW_CONFIG,
  SOCIAL_SHARES
} from './enum'

export const mappingReportFilter = ({ filter, tab, timezone }: { filter?: IReportManagementFilter; tab: string; timezone: string }) => {
  const detectFilterDateRange = ({
    dateRange
  }: {
    dateRange?: {
      from?: string | Date
      to?: string | Date
    }
  }) => {
    if (dateRange?.from && dateRange?.to) {
      return {
        dateRange: undefined,
        fromDatetime: addTzToDate(String(startOfDay(new Date(dateRange.from))), timezone),
        toDatetime: addTzToDate(String(endOfDay(new Date(dateRange.to))), timezone)
      }
    }

    const toDay = new Date()
    return {
      dateRange: undefined,
      fromDatetime: addTzToDate(
        String(
          startOfDay(
            changeTimezone({
              date: new Date(toDay.setDate(toDay.getDate() - 365)),
              timezone: timezone
            })
          )
        ),
        timezone
      ),
      toDatetime: addTzToDate(
        String(
          endOfDay(
            changeTimezone({
              date: new Date(),
              timezone: timezone
            })
          )
        ),
        timezone
      )
    }
  }

  if (filter?.tabsMembersTeams === TAB_TEAMS && filter?.teamIds) {
    delete filter?.hiringMemberIds
  } else if (filter?.hiringMemberIds) {
    delete filter?.teamIds
  }
  return {
    ...filter,
    ...(REPORT_FILTER_SHOW_CONFIG[tab]?.dateRange
      ? detectFilterDateRange({ dateRange: filter?.dateRange })
      : {
          dateRange: undefined,
          fromDatetime: undefined,
          toDatetime: undefined
        }),
    ...(filter?.locationIds?.length && REPORT_FILTER_SHOW_CONFIG[tab]?.locationIds
      ? { locationIds: filter.locationIds.map(item => Number(item.value)) }
      : { locationIds: undefined }),
    ...(filter?.departmentIds?.length && REPORT_FILTER_SHOW_CONFIG[tab]?.departmentIds
      ? {
          departmentIds: filter?.departmentIds.map(item => Number(item.value))
        }
      : { departmentIds: undefined }),
    ...(filter?.sourceKeys?.length && REPORT_FILTER_SHOW_CONFIG[tab]?.sourceKeys
      ? {
          sourceKeys: filter?.sourceKeys.map(item => item.label)
        }
      : { sourceKeys: undefined }),
    ...(filter?.sortDirection && REPORT_FILTER_SHOW_CONFIG[tab]?.sortDirection
      ? { sortDirection: filter.sortDirection }
      : { sortDirection: undefined }),
    ...((filter?.hiringMemberIds || [])?.length > 0 && REPORT_FILTER_SHOW_CONFIG[tab]?.hiringMemberIds
      ? {
          hiringMemberIds: (filter?.hiringMemberIds || []).map(item => Number(item.value))
        }
      : { hiringMemberIds: undefined }),
    ...(filter?.hiredByIds && REPORT_FILTER_SHOW_CONFIG[tab]?.hiredByIds
      ? {
          hiredByIds: [Number(filter?.hiredByIds?.value)]
        }
      : { hiredByIds: undefined }),
    ...(filter?.jobIds?.length && REPORT_FILTER_SHOW_CONFIG[tab]?.jobIds
      ? {
          jobIds: filter.jobIds.map(item => Number(item.value))
        }
      : { jobIds: undefined }),
    ...((filter?.teamIds || [])?.length > 0 && REPORT_FILTER_SHOW_CONFIG[tab]?.teamIds
      ? {
          teamIds: (filter?.teamIds || []).map(item => Number(item.value))
        }
      : { teamIds: undefined }),
    ...(filter?.createdByIds && REPORT_FILTER_SHOW_CONFIG[tab]?.createdByIds
      ? {
          createdByIds: [Number(filter?.createdByIds?.value)]
        }
      : { createdByIds: undefined }),
    ...(filter?.referrerId && REPORT_FILTER_SHOW_CONFIG[tab]?.referrerId
      ? {
          referrerId: Number(filter?.referrerId?.value)
        }
      : { referrerId: undefined }),

    sourcedName: filter?.sourcedName && REPORT_FILTER_SHOW_CONFIG[tab]?.sourcedName ? filter?.sourcedName?.value : undefined,
    ...(REPORT_FILTER_SHOW_CONFIG[tab]?.kpisFilter
      ? {
          kpisFilter: [
            { report_key: 'application_notes', visibility: true },
            { report_key: 'candidate_sourced', visibility: true },
            { report_key: 'stages_moved', visibility: true },
            { report_key: 'disqualifications', visibility: true },
            { report_key: 'interview_feedbacks', visibility: true },
            { report_key: 'interviews', visibility: true },
            { report_key: 'hires', visibility: true },
            { report_key: 'job_added', visibility: true },
            { report_key: 'job_notes', visibility: true },
            { report_key: 'applicant_feedbacks', visibility: true },
            { report_key: 'offers', visibility: true },
            { report_key: 'tasks', visibility: true },
            { report_key: 'company_added', visibility: true },
            { report_key: 'company_notes', visibility: true },
            { report_key: 'contact_added', visibility: true },
            { report_key: 'job_ordered', visibility: true },
            { report_key: 'application_notes', visibility: true },
            { report_key: 'sent_candidates', visibility: true },
            { report_key: 'client_submissions', visibility: true },
            { report_key: 'placements', visibility: true },
            { report_key: 'total_activities', visibility: true },
            { report_key: 'candidate_added', visibility: true },
            { report_key: 'total_upload_resumes', visibility: true },
            { report_key: 'candidate_call_logs', visibility: true },
            { report_key: 'contact_call_logs', visibility: true },
            { report_key: 'jobs', visibility: true }
          ]
        }
      : { kpisFilter: undefined }),
    isFilterTouched: undefined
  } as {
    [key: string]: unknown | undefined
  }
}

export const mappingReportMetricData = ({
  data,
  dataTypeMapping,
  subType
}: {
  data: {
    [key: string]: number
  }
  dataTypeMapping: string
  subType?: {
    current: string
    previous: string
  }
}) => {
  if (Object.keys(data).length === 0) return undefined

  const calculatePercent = ({ currentNumber, previousNumber }: { currentNumber: number; previousNumber?: number }) => {
    if (!previousNumber) return undefined

    const differ = Math.abs(currentNumber - previousNumber)
    return {
      increase: currentNumber > previousNumber,
      value: Math.round((differ / previousNumber) * 100)
    }
  }

  if (dataTypeMapping === METRIC_REPORT_DATA_TYPE.applicants) {
    return {
      currentData: Number(data.applicants_count || 0),
      previousData: data.previous_applicants_count,
      compare: calculatePercent({
        currentNumber: Number(data.applicants_count || 0),
        previousNumber: data.previous_applicants_count
      })
    }
  }

  if (dataTypeMapping === METRIC_REPORT_DATA_TYPE.hires) {
    return {
      currentData: Number(data.hires_count || 0),
      previousData: data.previous_hires_count,
      compare: calculatePercent({
        currentNumber: Number(data.hires_count || 0),
        previousNumber: data.previous_hires_count
      })
    }
  }

  if (dataTypeMapping === METRIC_REPORT_DATA_TYPE.timeToHires) {
    return {
      currentData: Number(data.time_to_hire || 0),
      previousData: data.previous_time_to_hire,
      compare: calculatePercent({
        currentNumber: Number(data.time_to_hire || 0),
        previousNumber: data.previous_time_to_hire
      })
    }
  }

  if (dataTypeMapping === METRIC_REPORT_DATA_TYPE.timeToHiresSummary && subType) {
    return {
      currentData: Number(data[subType.current] || 0),
      previousData: data[subType.previous],
      compare: calculatePercent({
        currentNumber: Number(data[subType.current] || 0),
        previousNumber: data[subType.previous]
      })
    }
  }

  if (dataTypeMapping === METRIC_REPORT_DATA_TYPE.referrals) {
    return {
      currentData: Number(data.referrals_count || 0),
      previousData: Number(data.previous_referrals_count || 0),
      compare: calculatePercent({
        currentNumber: Number(data.referrals_count || 0),
        previousNumber: Number(data.previous_referrals_count || 0)
      })
    }
  }

  return {
    currentData: Number(data.jobs_count || 0),
    previousData: data.previous_jobs_count,
    compare: calculatePercent({
      currentNumber: Number(data.jobs_count || 0),
      previousNumber: data.previous_jobs_count
    })
  }
}

export const mappingReportPieData = ({
  data = {},
  t
}: {
  data: {
    [key: string]: number
  }
  t: TFunction
}) => {
  let empty = true
  const mappings: Array<IPieChartDataProps> = []
  if (data && Object.keys(data).length) {
    Object.keys(data).forEach(key => {
      if (data[key]) {
        empty = false
        mappings.push({
          id: `${t(`report:profileSource:${key}`)}`,
          label: `${t(`report:profileSource:${key}`)}`,
          value: data[key],
          key,
          color: MAPPING_DATA_TYPE_PIE_CHART[key]?.color || ''
        })
      }
    })
  }

  if (empty) return undefined
  return mappings
}

export const mappingReportFunnelData = ({
  data = {},
  t
}: {
  data: {
    [key: string]: number
  }
  t: TFunction
}) => {
  let empty = true
  const mappings: Array<IFunnelChartDataProps> = []
  if (data && Object.keys(data).length) {
    Object.keys(data).forEach(key => {
      if (data[key]) {
        empty = false
        mappings.push({
          id: `${t(`report:stageTypes:${key}`)}`,
          label: `${t(`report:stageTypes:${key}`)}`,
          value: data[key],
          key,
          color: MAPPING_DATA_TYPE_FUNNEL_CHART[key]?.color || ''
        })
      }
    })
  }

  if (empty) return undefined
  return mappings
}

export const mappingReportPipelineData = ({ data = [] }: { data: [] }) => {
  let empty = true
  const mappings: Array<{}> = []
  if (data && data.length) {
    data.forEach((item: any, index: number) => {
      if (item?.summary) {
        empty = false
      }
      mappings.push({
        id: index,
        label: item.groupped_date,
        groupType: item.group,
        extraLabel: item.extra_date,
        fromDate: item.from_date,
        toDate: item.to_date,
        sourced: item?.summary?.['1'] || 0,
        sourcedColor: 'hsl(221.71deg 100% 67.84%)',
        applied: item?.summary?.['2'] || 0,
        appliedColor: 'hsl(258.37deg 100% 71.18%)',
        screening: item?.summary?.['3'] || 0,
        screeningColor: 'hsl(0deg 90.91% 78.43%)',
        interview: item?.summary?.['4'] || 0,
        interviewColor: 'hsl(43.05deg 84.52% 69.61%)',
        offer: item?.summary?.['5'] || 0,
        offerColor: 'hsl(116.51deg 50.59% 66.67%)',
        hired: item?.summary?.['6'] || 0,
        hiredColor: 'hsl(158.76deg 55.67% 60.2%)',
        clientSubmission: item?.summary?.['7'] || 0,
        clientSubmissionColor: 'hsl(21.64deg 100% 76.08%)'
      })
    })
  }

  if (empty) return undefined
  return mappings
}
export const REFER_TYPE = [
  {
    value: SOCIAL_SHARES,
    supportingObj: {
      name: SOCIAL_SHARES
    }
  },
  {
    value: DIRECT_REFER,
    supportingObj: {
      name: DIRECT_REFER
    }
  }
]

export const mappingReportFilterExport = ({
  filter,
  tab,
  timezone,
  displayKPIs
}: {
  filter?: IReportManagementFilter
  tab: string
  timezone: string
  displayKPIs?: {
    report_key: string
    visibility: boolean
  }[]
}) => {
  const detectFilterDateRange = ({
    dateRange
  }: {
    dateRange?: {
      from?: string | Date
      to?: string | Date
    }
  }) => {
    if (dateRange?.from && dateRange?.to) {
      return {
        dateRange: undefined,
        from_datetime: addTzToDate(String(startOfDay(new Date(dateRange.from))), timezone),
        to_datetime: addTzToDate(String(endOfDay(new Date(dateRange.to))), timezone)
      }
    }

    const toDay = new Date()
    return {
      dateRange: undefined,
      from_datetime: addTzToDate(
        String(
          startOfDay(
            changeTimezone({
              date: new Date(toDay.setDate(toDay.getDate() - 365)),
              timezone: timezone
            })
          )
        ),
        timezone
      ),
      to_datetime: addTzToDate(
        String(
          endOfDay(
            changeTimezone({
              date: new Date(),
              timezone: timezone
            })
          )
        ),
        timezone
      )
    }
  }

  if (filter?.tabsMembersTeams === TAB_TEAMS && filter?.teamIds) {
    delete filter?.hiringMemberIds
  } else if (filter?.hiringMemberIds) {
    delete filter?.teamIds
  }
  return {
    // ...filter,
    ...(filter?.tabsMembersTeams
      ? {
          tabs_members_teams: filter?.tabsMembersTeams
        }
      : { tabs_members_teams: undefined }),
    ...(REPORT_FILTER_SHOW_CONFIG[tab]?.dateRange
      ? detectFilterDateRange({ dateRange: filter?.dateRange })
      : {
          dateRange: undefined,
          from_datetime: undefined,
          to_datetime: undefined
        }),
    ...(filter?.locationIds?.length && REPORT_FILTER_SHOW_CONFIG[tab]?.locationIds
      ? { location_ids: filter.locationIds.map(item => Number(item.value)) }
      : { location_ids: undefined }),
    ...(filter?.departmentIds?.length && REPORT_FILTER_SHOW_CONFIG[tab]?.departmentIds
      ? {
          department_ids: filter?.departmentIds.map(item => Number(item.value))
        }
      : { department_ids: undefined }),
    ...(filter?.sourceKeys?.length && REPORT_FILTER_SHOW_CONFIG[tab]?.sourceKeys
      ? {
          source_keys: filter?.sourceKeys.map(item => item.label)
        }
      : { source_keys: undefined }),
    ...(filter?.sortDirection && REPORT_FILTER_SHOW_CONFIG[tab]?.sortDirection
      ? { sort_direction: filter.sortDirection }
      : { sort_direction: undefined }),
    ...((filter?.hiringMemberIds || [])?.length > 0 && REPORT_FILTER_SHOW_CONFIG[tab]?.hiringMemberIds
      ? {
          hiring_member_ids: (filter?.hiringMemberIds || []).map(item => Number(item.value))
        }
      : { hiring_member_ids: undefined }),
    ...(filter?.hiredByIds && REPORT_FILTER_SHOW_CONFIG[tab]?.hiredByIds
      ? {
          hired_by_ids: [Number(filter?.hiredByIds?.value)]
        }
      : { hired_by_ids: undefined }),
    ...(filter?.jobIds?.length && REPORT_FILTER_SHOW_CONFIG[tab]?.jobIds
      ? {
          job_ids: filter.jobIds.map(item => Number(item.value))
        }
      : { job_ids: undefined }),
    ...((filter?.teamIds || [])?.length > 0 && REPORT_FILTER_SHOW_CONFIG[tab]?.teamIds
      ? {
          team_ids: (filter?.teamIds || []).map(item => Number(item.value))
        }
      : { team_ids: undefined }),
    ...(filter?.createdByIds && REPORT_FILTER_SHOW_CONFIG[tab]?.createdByIds
      ? {
          created_by_ids: [Number(filter?.createdByIds?.value)]
        }
      : { created_by_ids: undefined }),
    ...(filter?.referrerId && REPORT_FILTER_SHOW_CONFIG[tab]?.referrerId
      ? {
          referrer_id: Number(filter?.referrerId?.value)
        }
      : { referrer_id: undefined }),

    sourced_name: filter?.sourcedName && REPORT_FILTER_SHOW_CONFIG[tab]?.sourcedName ? filter?.sourcedName?.value : undefined,
    ...(REPORT_FILTER_SHOW_CONFIG[tab]?.kpisFilter
      ? {
          kpis_filter: (displayKPIs || []).map(({ report_key, visibility }) => ({
            report_key,
            visibility
          }))
        }
      : { kpis_filter: undefined }),
    isFilterTouched: undefined
  } as {
    [key: string]: unknown | undefined
  }
}
