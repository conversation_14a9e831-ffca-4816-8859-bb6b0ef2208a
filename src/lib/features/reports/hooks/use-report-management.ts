import { useMemo, useState } from 'react'

import type { IUserInformation } from '~/core/@types/global'
import { TAB_MEMBERS } from '~/core/constants/enum'

import { changeTimezone } from '../../calendar/utilities/helper-schedule-interview'
import type { IReportManagementFilter } from '../types'

const useReportManagement = ({ user }: { user: IUserInformation }) => {
  const [filterValue, onChangeFilter] = useState<undefined | IReportManagementFilter>(() => {
    if (!user?.timezone) return {}

    const toDay = new Date()
    return {
      dateRange: {
        from: changeTimezone({
          date: new Date(toDay.setDate(toDay.getDate() - 29)),
          timezone: user.timezone
        }),
        to: changeTimezone({
          date: new Date(),
          timezone: user.timezone
        })
      },
      sortDirection: { direction: 'desc', field: 'total_activities' },
      tabsMembersTeams: TAB_MEMBERS
    }
  })

  return {
    filterControl: useMemo(
      () => ({
        value: filterValue,
        onChange: onChangeFilter
      }),
      [filterValue]
    ),
    action: {}
  }
}

export default useReportManagement
