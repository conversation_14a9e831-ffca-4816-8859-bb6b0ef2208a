import { endOfDay, startOfDay } from 'date-fns'
import type { AnyVariables, DocumentInput } from 'urql'

import usePaginationGraphPage from '~/lib/hooks/use-pagination-graph-page'
import useBoundStore from '~/lib/store'

import { addTzToDate, changeTimezone, removeTzFromDate } from '../../calendar/utilities/helper-schedule-interview'
import type { IReportDetailFilter } from '../types'

export const useReportDetailManagement = ({
  queryReportDetail,
  filterControl,
  typeModal,
  sortDirection
}: {
  sortDirection?: { direction?: string; field?: string }
  queryReportDetail?: DocumentInput<any, AnyVariables>
  typeModal?: string
  filterControl?: {
    value?: IReportDetailFilter | undefined
    onChange?: ((value?: IReportDetailFilter) => void) | undefined
  }
}) => {
  const user = useBoundStore(state => state.user)

  const { data, status, error, isFetching, fetchPagination, forceChangeCurrentPage, globalFilter, setGlobalFilter } = usePaginationGraphPage({
    queryDocumentNode: queryReportDetail as any,
    queryKey: 'use-report-detail-management',
    filter: {
      ...(filterControl?.value?.fromDateTimePipelineItem
        ? {
            fromDatetime: addTzToDate(
              String(startOfDay(new Date(removeTzFromDate(filterControl?.value?.fromDateTimePipelineItem)))),
              String(user.timezone)
            )
          }
        : filterControl?.value?.dateRange?.from
          ? {
              fromDatetime: addTzToDate(String(startOfDay(new Date(filterControl?.value?.dateRange?.from))), String(user.timezone))
            }
          : {
              fromDatetime: addTzToDate(
                String(
                  startOfDay(
                    changeTimezone({
                      date: new Date(new Date().setDate(new Date().getDate() - 365)),
                      timezone: user.timezone
                    })
                  )
                ),
                String(user.timezone)
              )
            }),
      ...(filterControl?.value?.toDateTimePipelineItem
        ? {
            toDatetime: addTzToDate(String(endOfDay(new Date(filterControl?.value?.toDateTimePipelineItem))), String(user.timezone))
          }
        : filterControl?.value?.dateRange?.to
          ? {
              toDatetime: addTzToDate(String(endOfDay(new Date(filterControl?.value?.dateRange?.to))), String(user.timezone))
            }
          : {
              toDatetime: addTzToDate(
                String(
                  endOfDay(
                    changeTimezone({
                      date: new Date(),
                      timezone: user.timezone
                    })
                  )
                ),
                String(user.timezone)
              )
            }),
      ...(filterControl?.value?.locationIds && filterControl?.value?.locationIds?.length > 0
        ? {
            locationIds: filterControl?.value?.locationIds?.map(item => Number(item.value))
          }
        : {}),
      ...(filterControl?.value?.jobIds && filterControl?.value?.jobIds?.length > 0
        ? {
            jobIds: filterControl?.value?.jobIds?.map(item => Number(item.value))
          }
        : {}),
      ...(filterControl?.value?.hiringMemberIds && filterControl?.value?.hiringMemberIds?.length > 0
        ? {
            hiringMemberIds: filterControl?.value?.hiringMemberIds?.map(item => Number(item.value))
          }
        : {}),
      ...(filterControl?.value?.teamIds && filterControl?.value?.teamIds?.length > 0
        ? {
            teamIds: filterControl?.value?.teamIds?.map(item => Number(item.value))
          }
        : {}),
      ...(filterControl?.value?.departmentIds && filterControl?.value?.departmentIds.length > 0
        ? {
            departmentIds: filterControl?.value?.departmentIds?.map(item => Number(item.value))
          }
        : {}),
      ...(filterControl?.value?.stageKey
        ? {
            stageKey: String(filterControl?.value?.stageKey)
          }
        : {}),
      ...(filterControl?.value?.sourced
        ? {
            sourced: String(filterControl?.value?.sourced)
          }
        : {}),
      ...(filterControl?.value?.sourceKeys
        ? filterControl?.value?.sourceKeys[0]?.label
          ? {
              sourceKeys: filterControl?.value?.sourceKeys?.map(item => item.label)
            }
          : {
              sourceKeys: [String(filterControl?.value?.sourceKeys)]
            }
        : {}),
      ...(filterControl?.value?.stageTypeId ? { stageTypeId: Number(filterControl?.value?.stageTypeId) } : {}),
      modelKeY: typeModal || '',
      sortDirection: sortDirection
        ? {
            direction: sortDirection?.direction,
            field: sortDirection?.field
          }
        : undefined
    }
  })

  return {
    data,
    status,
    error,
    isFetching,
    globalFilter,
    setGlobalFilter,
    fetchPagination,
    forceChangeCurrentPage
  }
}
