import { gql } from 'urql'

import type { IReportDetailActionLog, IReportDetailFilter } from '../types'

const QueryReportsHiresBySourceList = gql<
  {
    reportsOverviewHiresBySourceList: {
      data: Array<IReportDetailActionLog>
      metadata: {
        totalCount: number
        currentPage: number
      }
    }
  },
  IReportDetailFilter
>`
  query (
    $fromDatetime: ISO8601DateTime!
    $toDatetime: ISO8601DateTime!
    $locationIds: [Int!]
    $jobIds: [Int!]
    $hiringMemberIds: [Int!]
    $departmentIds: [Int!]
    $sourced: String!
    $page: Int
    $limit: Int
    $teamIds: [Int!]
  ) {
    reportsOverviewHiresBySourceList(
      fromDatetime: $fromDatetime
      toDatetime: $toDatetime
      locationIds: $locationIds
      hiringMemberIds: $hiringMemberIds
      departmentIds: $departmentIds
      sourced: $sourced
      page: $page
      limit: $limit
      jobIds: $jobIds
      teamIds: $teamIds
    ) {
      collection {
        id
        hiredFillingDays
        hiredHiringDays
        createdAt
        profile {
          id
          sourcedDescription
          fullName
          email
          avatarVariants
          sourcedDescription
          owner {
            id
            fullName
            avatarVariants
            defaultColour
          }
        }
        job {
          openedAt
          id
          title
          jobLocations {
            name
            address
            state
            city
            country
          }
          department {
            name
          }
          status
          statusDescription
          owner {
            id
            fullName
            avatarVariants
            defaultColour
          }
        }
        applicant {
          id
          createdAt
          hiredDate
          status
          timeToHire
          rejectedReasonLabel
          jobStage {
            stageLabel
            stageTypeId
          }
          hiredBy {
            id
            fullName
            avatarVariants
            defaultColour
          }
        }
      }

      metadata {
        totalCount
        currentPage
      }
    }
  }
`

export default QueryReportsHiresBySourceList
