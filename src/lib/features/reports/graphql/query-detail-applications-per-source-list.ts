import { gql } from 'urql'

import type { IReportDetailApplication, IReportDetailFilter } from '../types'

const QueryDetailApplicantsPerSourceList = gql<
  {
    reportsOverviewApplicantsPerSourceList: {
      data: Array<IReportDetailApplication>
      metadata: {
        totalCount: number
        currentPage: number
      }
    }
  },
  IReportDetailFilter
>`
  query (
    $fromDatetime: ISO8601DateTime!
    $toDatetime: ISO8601DateTime!
    $locationIds: [Int!]
    $jobIds: [Int!]
    $hiringMemberIds: [Int!]
    $departmentIds: [Int!]
    $sourced: String!
    $page: Int
    $limit: Int
    $teamIds: [Int!]
  ) {
    reportsOverviewApplicantsPerSourceList(
      fromDatetime: $fromDatetime
      toDatetime: $toDatetime
      locationIds: $locationIds
      hiringMemberIds: $hiringMemberIds
      departmentIds: $departmentIds
      sourced: $sourced
      page: $page
      limit: $limit
      jobIds: $jobIds
      teamIds: $teamIds
    ) {
      collection {
        id
        profile {
          id
          fullName
          avatarVariants
          sourcedDescription
        }
        job {
          id
          title
          status
          statusDescription
        }
        rejectedReasonLabel
        jobStage {
          stageTypeId
          stageLabel
        }
        createdAt
        hiredDate
        status
        timeToHire
      }

      metadata {
        totalCount
        currentPage
      }
    }
  }
`

export default QueryDetailApplicantsPerSourceList
