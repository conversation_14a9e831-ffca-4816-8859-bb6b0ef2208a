import { gql } from 'urql'

import type { IReportDetailFilter, IReportDetailOpenJob } from '../types'

const QueryDetailOpenJobs = gql<
  {
    reportsOverviewOpenJobsList: {
      data: Array<IReportDetailOpenJob>
      metadata: {
        totalCount: number
        currentPage: number
      }
    }
  },
  IReportDetailFilter
>`
  query (
    $fromDatetime: ISO8601DateTime!
    $toDatetime: ISO8601DateTime!
    $hiringMemberIds: [Int!]
    $locationIds: [Int!]
    $jobIds: [Int!]
    $departmentIds: [Int!]
    $page: Int
    $limit: Int
    $teamIds: [Int!]
  ) {
    reportsOverviewOpenJobsList(
      fromDatetime: $fromDatetime
      toDatetime: $toDatetime
      hiringMemberIds: $hiringMemberIds
      locationIds: $locationIds
      departmentIds: $departmentIds
      page: $page
      limit: $limit
      jobIds: $jobIds
      teamIds: $teamIds
    ) {
      collection {
        id
        title
        status
        statusDescription
        jobLocations {
          address
          state
          country
        }
        department {
          name
        }
        createdAt
        openedAt
        archivedAt
        applicantsCount
        firstHiredDate
      }

      metadata {
        totalCount
        currentPage
      }
    }
  }
`

export default QueryDetailOpenJobs
