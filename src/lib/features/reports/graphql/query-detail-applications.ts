import { gql } from 'urql'

import type { IReportDetailApplication, IReportDetailFilter } from '../types'

const QueryDetailApplications = gql<
  {
    reportsOverviewApplicantsList: {
      data: Array<IReportDetailApplication>
      metadata: {
        totalCount: number
        currentPage: number
      }
    }
  },
  IReportDetailFilter
>`
  query (
    $fromDatetime: ISO8601DateTime!
    $toDatetime: ISO8601DateTime!
    $hiringMemberIds: [Int!]
    $locationIds: [Int!]
    $jobIds: [Int!]
    $departmentIds: [Int!]
    $page: Int
    $limit: Int
    $teamIds: [Int!]
  ) {
    reportsOverviewApplicantsList(
      fromDatetime: $fromDatetime
      toDatetime: $toDatetime
      hiringMemberIds: $hiringMemberIds
      locationIds: $locationIds
      departmentIds: $departmentIds
      page: $page
      limit: $limit
      jobIds: $jobIds
      teamIds: $teamIds
    ) {
      collection {
        id
        profile {
          id
          fullName
          avatarVariants
          sourcedDescription
        }
        job {
          id
          title
          status
          statusDescription
          owner {
            id
            fullName
            avatarVariants
            defaultColour
          }
        }
        rejectedReasonLabel
        jobStage {
          stageTypeId
          stageLabel
        }
        createdAt
        hiredDate
        status
        timeToHire
      }

      metadata {
        totalCount
        currentPage
      }
    }
  }
`

export default QueryDetailApplications
