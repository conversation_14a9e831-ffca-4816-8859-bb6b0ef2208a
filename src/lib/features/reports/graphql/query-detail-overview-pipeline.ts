import { gql } from 'urql'

import type { IReportDetailActionLog, IReportDetailFilter } from '../types'

const QueryDetailOverviewPipelineDetailList = gql<
  {
    reportsOverviewPipelinesList: {
      data: Array<IReportDetailActionLog>
      metadata: {
        totalCount: number
        currentPage: number
      }
    }
  },
  IReportDetailFilter
>`
  query (
    $page: Int
    $limit: Int
    $stageTypeId: Int!
    $fromDatetime: ISO8601DateTime!
    $toDatetime: ISO8601DateTime!
    $locationIds: [Int!]
    $sortDirection: JSON
    $hiringMemberIds: [Int!]
    $departmentIds: [Int!]
    $jobIds: [Int!]
    $teamIds: [Int!]
  ) {
    reportsOverviewPipelinesList(
      page: $page
      limit: $limit
      sortDirection: $sortDirection
      stageTypeId: $stageTypeId
      fromDatetime: $fromDatetime
      toDatetime: $toDatetime
      locationIds: $locationIds
      hiringMemberIds: $hiringMemberIds
      departmentIds: $departmentIds
      jobIds: $jobIds
      teamIds: $teamIds
    ) {
      collection {
        id
        hiredFillingDays
        hiredHiringDays
        createdAt
        profile {
          id
          sourcedDescription
          fullName
          email
          avatarVariants
          sourcedDescription
        }
        job {
          openedAt
          id
          title
          jobLocations {
            name
            address
            state
            city
            country
          }
          department {
            name
          }
          status
          statusDescription
        }
        applicant {
          id
          createdAt
          hiredDate
          status
          timeToHire
          rejectedReasonLabel
          jobStage {
            stageLabel
            stageTypeId
          }
        }
      }

      metadata {
        totalCount
        currentPage
      }
    }
  }
`

export default QueryDetailOverviewPipelineDetailList
