import { gql } from 'urql'

import type { IReportDetailActionLog, IReportDetailFilter } from '../types'

const QueryReportsTimeToHiresList = gql<
  {
    reportsTimeHiresList: {
      data: Array<IReportDetailActionLog>
      metadata: {
        totalCount: number
        currentPage: number
      }
    }
  },
  IReportDetailFilter
>`
  query (
    $fromDatetime: ISO8601DateTime!
    $toDatetime: ISO8601DateTime!
    $locationIds: [Int!]
    $hiringMemberIds: [Int!]
    $jobIds: [Int!]
    $hiredByIds: [Int!]
    $departmentIds: [Int!]
    $page: Int
    $limit: Int
    $createdByIds: [Int!]
    $teamIds: [Int!]
  ) {
    reportsTimeHiresList(
      fromDatetime: $fromDatetime
      toDatetime: $toDatetime
      locationIds: $locationIds
      hiringMemberIds: $hiringMemberIds
      departmentIds: $departmentIds
      jobIds: $jobIds
      page: $page
      limit: $limit
      hiredByIds: $hiredByIds
      createdByIds: $createdByIds
      teamIds: $teamIds
    ) {
      collection {
        id
        loggedDate
        hiredFillingDays
        hiredHiringDays
        createdAt
        recordOwner {
          id
          fullName
          avatarVariants
          defaultColour
        }
        user {
          id
          fullName
          avatarVariants
          defaultColour
        }
        profile {
          id
          sourcedDescription
          fullName
          email
          avatarVariants
          sourcedDescription
          defaultColour
          owner {
            id
            fullName
            avatarVariants
            defaultColour
          }
        }
        job {
          openedAt
          id
          title
          status
          statusDescription
          jobLocations {
            name
            address
            state
            city
            country
          }
          department {
            name
          }
          owner {
            id
            fullName
            avatarVariants
            defaultColour
          }
        }
        applicant {
          id
          createdAt
          hiredDate
          status
          timeToHire
          rejectedReasonLabel
          jobStage {
            stageLabel
            stageTypeId
          }
          hiredBy {
            id
            fullName
            avatarVariants
            defaultColour
          }
        }
      }

      metadata {
        totalCount
        currentPage
      }
    }
  }
`

export default QueryReportsTimeToHiresList
