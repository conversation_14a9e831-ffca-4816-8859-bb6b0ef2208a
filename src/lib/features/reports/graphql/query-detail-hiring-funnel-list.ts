import { gql } from 'urql'

import type { IReportDetailActionLog, IReportDetailFilter } from '../types'

const QueryDetailHiringFunnelList = gql<
  {
    reportsOverviewHiringFunnelList: {
      data: Array<IReportDetailActionLog>
      metadata: {
        totalCount: number
        currentPage: number
      }
    }
  },
  IReportDetailFilter
>`
  query (
    $fromDatetime: ISO8601DateTime!
    $toDatetime: ISO8601DateTime!
    $locationIds: [Int!]
    $hiringMemberIds: [Int!]
    $departmentIds: [Int!]
    $stageKey: String!
    $page: Int
    $limit: Int
    $jobIds: [Int!]
    $teamIds: [Int!]
  ) {
    reportsOverviewHiringFunnelList(
      fromDatetime: $fromDatetime
      toDatetime: $toDatetime
      locationIds: $locationIds
      hiringMemberIds: $hiringMemberIds
      departmentIds: $departmentIds
      stageKey: $stageKey
      page: $page
      limit: $limit
      jobIds: $jobIds
      teamIds: $teamIds
    ) {
      collection {
        id
        hiredFillingDays
        hiredHiringDays
        createdAt
        profile {
          id
          sourcedDescription
          fullName
          email
          avatarVariants
          sourcedDescription
        }
        job {
          openedAt
          id
          title
          jobLocations {
            name
            address
            state
            city
            country
          }
          department {
            name
          }
          status
          statusDescription
        }
        applicant {
          id
          createdAt
          hiredDate
          status
          timeToHire
          rejectedReasonLabel
          jobStage {
            stageLabel
            stageTypeId
          }
        }
      }

      metadata {
        totalCount
        currentPage
      }
    }
  }
`

export default QueryDetailHiringFunnelList
