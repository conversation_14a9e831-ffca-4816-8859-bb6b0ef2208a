import { gql } from 'urql'

import type { IReportDetailApplication, IReportDetailFilter } from '../types'

const QueryDetailReferrals = gql<
  {
    reportsOverviewReferralsList: {
      data: Array<IReportDetailApplication>
      metadata: {
        totalCount: number
        currentPage: number
      }
    }
  },
  IReportDetailFilter
>`
  query (
    $fromDatetime: ISO8601DateTime!
    $toDatetime: ISO8601DateTime!
    $hiringMemberIds: [Int!]
    $locationIds: [Int!]
    $jobIds: [Int!]
    $departmentIds: [Int!]
    $page: Int
    $limit: Int
  ) {
    reportsOverviewReferralsList(
      fromDatetime: $fromDatetime
      toDatetime: $toDatetime
      hiringMemberIds: $hiringMemberIds
      locationIds: $locationIds
      departmentIds: $departmentIds
      jobIds: $jobIds
      page: $page
      limit: $limit
    ) {
      collection {
        id
        profile {
          id
          fullName
          avatarVariants
          sourcedDescription
        }
        job {
          id
          title
          status
          statusDescription
        }
        rejectedReasonLabel
        jobStage {
          stageLabel
          stageTypeId
        }
        createdAt
        hiredDate
        status
        timeToHire
        referral {
          user {
            id
            fullName
            avatarVariants
            defaultColour
          }
          createdAt
        }
      }

      metadata {
        totalCount
        currentPage
      }
    }
  }
`

export default QueryDetailReferrals
