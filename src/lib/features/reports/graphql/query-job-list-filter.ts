import { gql } from 'urql'

import type { IJobManagementItemQuery } from '../../jobs/types'

const QueryJobListFilterReport = gql<
  {
    reportFilterJobsList: {
      collection: IJobManagementItemQuery[]
      metadata: { totalCount: number }
    }
  },
  {
    search?: string
    page: number
    limit: number
  }
>`
  query ($page: Int, $limit: Int, $search: String) {
    reportFilterJobsList(limit: $limit, page: $page, search: $search) {
      collection {
        id
        title
        jobLocations {
          name
          address
          state
          city
          country
        }
        department {
          name
        }
      }
      metadata {
        totalCount
      }
    }
  }
`

export default QueryJobListFilterReport
