import type { ILogoAndAvatarVariants } from '~/core/@types/global'
import type { ISelectOption } from '~/core/ui/Select'

import type { IJobStage } from '../../jobs/types'

export interface IReportManagementFilter {
  dateRange?: {
    from?: string | Date
    to?: string | Date
  }
  locationIds?: Array<ISelectOption>
  departmentIds?: Array<ISelectOption>
  hiredByIds?: ISelectOption
  jobIds?: Array<ISelectOption>
  hiringMemberIds?: Array<ISelectOption>
  sortDirection?: { field: string; direction: string }
  teamIds?: Array<ISelectOption>
  tabsMembersTeams?: string
  createdByIds?: ISelectOption
  referrerId?: ISelectOption
  sourcedName?: ISelectOption
  sourceKeys?: Array<ISelectOption>
}

export type IReportDetailFilter = IReportManagementFilter & {
  page?: number
  limit?: number
  stageTypeId?: number
  fromDateTimePipelineItem?: string
  toDateTimePipelineItem?: string
  stageKey?: string
  sourced?: string
}

export type IReportQueryParams = {
  fromDatetime?: string
  toDatetime?: string
  locationIds?: Array<number>
  hiringMemberIds?: Array<number>
  departmentId?: number
}

export interface IReportTimeToHire {
  id?: string
  hiredFillingDays?: number
  hiredHiringDays?: number
  createdAt?: string
  profile?: {
    id?: string
    sourcedDescription?: string
    fullName?: string
    email?: string
  }
  job?: {
    id?: string
    openedAt?: string
    title?: string
    status: string
    statusDescription?: string
    jobLocations?: Array<{
      name?: string
      address?: string
      state?: string
      city?: string
      country?: string
    }>
    department?: {
      name?: string
    }
  }
  applicant?: {
    id?: number
  }
}

export interface IReportTeamProductivity {
  id?: string
  user?: {
    id?: string
    fullName?: string
    email?: string
    avatarVariants?: ILogoAndAvatarVariants
    defaultColour?: string
  }
  report?: {
    [key: string]: {
      count?: number
      percentage?: number
      trend?: string
    }
  }
}
export interface IReportReferral {
  id?: string
  profile?: {
    id?: string
    fullName?: string
    sourcedDescription?: string
    sourcedName?: string
    avatarVariants?: ILogoAndAvatarVariants
    defaultColour?: string
    email?: string
    sourcedNameDescription?: string
  }
  user: {
    id?: string
    fullName?: string
    avatarVariants?: ILogoAndAvatarVariants
    defaultColour?: string
  }
  referral: {
    applicant: {
      rejectedReasonLabel?: string

      jobStage: {
        stageTypeId?: string
        stageLabel?: string
      }
    }
  }
  job?: {
    id?: string
    jobLocations?: {
      id?: string
      locationId?: string
    }
    rewardAmount?: number
    rewardCurrency?: string
    rewardGift?: string
    referralRewardType?: string
    statusDescription?: string
    status?: string
    title?: string
    department: {
      id?: string
      name?: string
    }
  }
  createdAt?: string
}

export type IReportDetailOpenJob = {
  id: string
  title: string
  status: string
  statusDescription: string
  applicantsCount: number
  jobLocations: Array<{
    address: string
    state: string
    country: string
  }>
  department: {
    name: string
  }
  createdAt: string
  openedAt: string
  archivedAt: string
  firstHiredDate: string
  currentUserAccessible: boolean | undefined
}

export type IReportDetailApplication = {
  id: number
  hiredBy?: {
    id: number
    fullName: string
    avatarVariants: ILogoAndAvatarVariants
    defaultColour: string
  }
  profile: {
    id: number
    fullName: string
    avatarVariants?: ILogoAndAvatarVariants
    sourcedDescription: string
    owner?: {
      id: number
      fullName: string
      avatarVariants: ILogoAndAvatarVariants
      defaultColour: string
    }
  }
  job: {
    id: number
    title: string
    status: string
    statusDescription?: string
    owner?: {
      id: number
      fullName: string
      avatarVariants: ILogoAndAvatarVariants
      defaultColour: string
    }
    jobLocations: {
      address: string
      city: string
      state: string
      country: string
    }[]
    department: {
      name: string
    }
    company: {
      name: string
    }
  }
  rejectedReasonLabel: string
  jobStage: IJobStage
  createdAt: string
  hiredDate: string
  status: string
  timeToHire: number
  referral?: {
    user: {
      id: number
      fullName: string
      avatarVariants: ILogoAndAvatarVariants
      defaultColour: string
    }
    createdAt: string
  }
  applicant?: {
    id: number
    createdAt: string
    hiredDate: string
    status: string
    timeToHire: number
    rejectedReasonLabel: string
    jobStage: IJobStage
    hiredBy: {
      id: number
      fullName: string
      avatarVariants: ILogoAndAvatarVariants
      defaultColour: string
    }
  }
}
export type IReportDetailCandidateSource = {
  id?: string
  fullName?: string
  email?: string
  employeeId?: number
  avatarVariants?: ILogoAndAvatarVariants
  sourcedDescription?: string
  sourcedName?: string
  sourcedNameDescription?: string
  createdAt?: string
  applicants?: [{ flagNew: boolean }]
  owner?: {
    id: string
    fullName: string
    avatarVariants: ILogoAndAvatarVariants
    defaultColour?: string
  }
}

export type IReportDetailCandidateInInterviewSource = {
  id?: string
  profile?: {
    id?: string
    fullName?: string
    email?: string
    avatarVariants?: ILogoAndAvatarVariants
    sourcedDescription?: string
    sourcedName?: string
    sourcedNameDescription?: string
    createdAt?: string
    owner?: {
      id: string
      fullName: string
      avatarVariants: ILogoAndAvatarVariants
      defaultColour?: string
    }
  }
  job?: {
    id: number
    title: string
    statusDescription?: string
    status: string
  }
  jobStage: IJobStage
}
export type IReportDetailActionLog = {
  id: number
  hiredFillingDays: number
  hiredHiringDays: number
  createdAt: string
  recordOwner?: {
    id: number
    fullName: string
    avatarVariants: ILogoAndAvatarVariants
    defaultColour: string
  }
  profile: {
    id: number
    sourcedDescription: string
    fullName: string
    email: string
    avatarVariants?: ILogoAndAvatarVariants
    sourcedDescription: string
    defaultColour?: string
    owner?: {
      id: number
      fullName: string
      avatarVariants: ILogoAndAvatarVariants
      defaultColour: string
    }
  }
  job: {
    openedAt: string
    id: number
    title: string
    status: string
    statusDescription: string
    jobLocations: Array<{
      name: string
      address: string
      state: string
      city: string
      country: string
    }>
    department: {
      name: string
    }
    owner?: {
      id: number
      fullName: string
      avatarVariants: ILogoAndAvatarVariants
      defaultColour: string
    }
  }
  applicant: {
    id: number
    createdAt: string
    hiredDate: string
    status: string
    timeToHire: number
    rejectedReasonLabel: string
    jobStage: IJobStage
    hiredBy: {
      id: number
      fullName: string
      avatarVariants: ILogoAndAvatarVariants
      defaultColour: string
    }
  }
  user?: {
    id: number
    fullName: string
    avatarVariants: ILogoAndAvatarVariants
    defaultColour: string
  }
}
