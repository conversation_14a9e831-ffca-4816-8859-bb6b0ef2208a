import { create } from 'zustand'

import type { OpenJobCountSlice } from './open-job-count-slice'
import { createOpenJobsCountSlice } from './open-job-count-slice'
import type { MyReferralsListSlice } from './refetch-my-referral-list-slice'
import { createMyReferralListSlice } from './refetch-my-referral-list-slice'

const useReferralStore = create<MyReferralsListSlice & OpenJobCountSlice>()((...a) => ({
  ...createMyReferralListSlice(...a),
  ...createOpenJobsCountSlice(...a)
}))

export default useReferralStore
