import type { StateCreator } from 'zustand'

export interface MyReferralsListSlice {
  refetchMyReferralsList: () => Promise<any>
  setRefetchMyReferralsList: (func: () => Promise<any>) => void
}

export const createMyReferralListSlice: StateCreator<MyReferralsListSlice, [], [], MyReferralsListSlice> = (set: Function) => ({
  refetchMyReferralsList: () => Promise.resolve(),
  setRefetchMyReferralsList: func => set({ refetchMyReferralsList: func })
})
