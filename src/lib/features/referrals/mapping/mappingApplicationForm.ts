import type { IUserInformation } from '~/core/@types/global'

import { formatInitialValueCustomField } from '../../settings/profile-fields/mapping/custom-field-mapping'
import type { EmployeeProfileType } from '../types'

export const mappingProfileEmployeeToApplicationForm = ({ profile, user }: { profile?: EmployeeProfileType; user: IUserInformation }) => {
  const permittedFields = profile?.permittedFields

  return {
    fullName: String(profile?.fullName || user.fullName),
    email: String(profile?.email?.[0] || user.email),
    headline: profile?.headline || user.jobTitle,
    phoneNumber: String(profile?.phoneNumber || user.phoneNumber),
    attachments: profile?.profileCvs?.[0]?.attachments || [],
    coverLetter: profile?.coverLetter,
    totalYearsOfExp: permittedFields?.totalYearsOfExp?.value && {
      value: String(permittedFields?.totalYearsOfExp?.value)
    },
    currentSalary: {
      typeOfSalary: permittedFields?.typeOfCurrentSalary?.value && {
        value: permittedFields?.typeOfCurrentSalary?.value,
        supportingObj: {
          name: permittedFields?.typeOfCurrentSalary?.value
        }
      },
      salary: permittedFields?.currentSalary?.value ? Number(permittedFields?.currentSalary?.value) : 0,
      currency: (permittedFields?.currentSalaryCurrency?.value || user?.currentTenant?.currency) && {
        value: permittedFields?.currentSalaryCurrency?.value || user?.currentTenant?.currency,
        supportingObj: {
          name: permittedFields?.currentSalaryCurrency?.value || user?.currentTenant?.currency
        }
      }
    },
    expectedSalary: {
      typeOfSalary: permittedFields?.typeOfExpectedSalary?.value && {
        value: permittedFields?.typeOfExpectedSalary?.value,
        supportingObj: {
          name: permittedFields?.typeOfExpectedSalary?.value
        }
      },
      salary: permittedFields?.expectedSalary?.value ? Number(permittedFields?.expectedSalary?.value) : 0,
      currency: (permittedFields?.expectedSalaryCurrency?.value || user?.currentTenant?.currency) && {
        value: permittedFields?.expectedSalaryCurrency?.value || user?.currentTenant?.currency,
        supportingObj: {
          name: permittedFields?.expectedSalaryCurrency?.value || user?.currentTenant?.currency
        }
      }
    },
    birthday: {
      date: permittedFields?.birthday?.value?.birth_date ? Number(permittedFields?.birthday?.value?.birth_date) : undefined,
      month: permittedFields?.birthday?.value?.birth_month ? Number(permittedFields?.birthday?.value?.birth_month) : undefined,
      year: permittedFields?.birthday?.value?.birth_year ? Number(permittedFields?.birthday?.value?.birth_year) : undefined
    },
    profileLevel: permittedFields?.profileLevel?.value && {
      value: permittedFields?.profileLevel?.value,
      supportingObj: {
        name: permittedFields?.profileLevel?.value
      }
    },
    nationality: permittedFields?.nationality?.value,
    willingToRelocate: permittedFields?.willingToRelocate?.value,
    noticeToPeriodDays: permittedFields?.noticeToPeriodDays?.value && {
      value: permittedFields?.noticeToPeriodDays?.value,
      supportingObj: {
        name: permittedFields?.noticeToPeriodDays?.value
      }
    },
    languages: permittedFields?.languages?.value?.map(item => ({
      language: {
        value: item.language,
        supportingObj: {
          name: item.languageDescription
        }
      },
      proficiency: {
        value: item.proficiency,
        supportingObj: {
          name: item.proficiencyDescription
        }
      }
    })),
    customFields: formatInitialValueCustomField(profile?.customFields)
  }
}
