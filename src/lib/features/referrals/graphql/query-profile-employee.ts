import { gql } from 'urql'

import type { EmployeeProfileType } from '../types'

const QueryProfileEmployee = gql<
  {
    internalApplicationProfilesShow: EmployeeProfileType
  },
  {
    employeeId?: number
  }
>`
  query ($employeeId: Int!) {
    internalApplicationProfilesShow(employeeId: $employeeId) {
      id
      fullName
      email
      headline
      phoneNumber
      address
      coverLetter
      countryStateId
      links
      sourced
      sourcedDescription
      sourcedName
      sourcedNameDescription
      profileCvs {
        id
        attachments {
          id
          file
          blobs
        }
      }
      totalYearsOfExp
      permittedFields
      tags {
        value
        name
        id
      }
      customFields
    }
  }
`

export default QueryProfileEmployee
