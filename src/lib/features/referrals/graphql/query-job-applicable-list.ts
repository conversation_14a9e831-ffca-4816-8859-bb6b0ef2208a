import { gql } from 'urql'

import type { JobApplicableType, QueryJobApplicableParam } from '../types'

const QueryJobsApplicableList = gql<
  {
    jobsReferableList: {
      collection: Array<JobApplicableType>
      metadata: {
        totalCount: number
      }
    }
  },
  QueryJobApplicableParam
>`
  query ($page: Int, $limit: Int) {
    jobsReferableList(page: $page, limit: $limit) {
      collection {
        id
        title
        department {
          name
        }
        jobLocations {
          state
          country
        }
      }
      metadata {
        totalCount
        extras
      }
    }
  }
`

export default QueryJobsApplicableList
