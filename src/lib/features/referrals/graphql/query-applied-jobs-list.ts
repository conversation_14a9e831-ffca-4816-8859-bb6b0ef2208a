import { gql } from 'urql'

import type { JobDetailType, OpenJobsParams } from '../types'

const QueryJobsAppliedList = gql<
  {
    jobsAppliedList: {
      collection: Array<JobDetailType>
      metadata: {
        totalCount: number
      }
    }
  },
  OpenJobsParams
>`
  query ($limit: Int, $page: Int) {
    jobsAppliedList(limit: $limit, page: $page) {
      collection {
        id
        title
        slug
        createdAt
        referralsCount
        jobReferable
        jobLocations {
          state
          country
        }
        department {
          name
        }
        tenant {
          slug
        }
        status
        enablingReward
        rewardAmount
        rewardCurrency
        rewardGift
        referralRewardType
        publicReferralUri
        recommendationMatchedFields
        savedReferralJobMemberIds
        permittedFields
        currentUserAppliedAt
        tags {
          id
          name
        }
        skills
      }
      metadata {
        totalCount
      }
    }
  }
`

export default QueryJobsAppliedList
