import { gql } from 'urql'

import type { JobDetailType, OpenJobsParams } from '../types'

const QueryJobsSavedList = gql<
  {
    jobsSavedList: {
      collection: Array<JobDetailType>
      metadata: {
        totalCount: number
      }
    }
  },
  OpenJobsParams
>`
  query ($limit: Int, $page: Int) {
    jobsSavedList(limit: $limit, page: $page) {
      collection {
        id
        title
        slug
        createdAt
        referralsCount
        jobReferable
        jobLocations {
          state
          country
        }
        department {
          name
        }
        tenant {
          slug
        }
        status
        enablingReward
        rewardAmount
        rewardCurrency
        rewardGift
        referralRewardType
        publicReferralUri
        recommendationMatchedFields
        savedReferralJobMemberIds
        permittedFields
        currentUserAppliedAt
        tags {
          id
          name
        }
        skills
      }
      metadata {
        totalCount
      }
    }
  }
`

export default QueryJobsSavedList
