'use client'

import { useCallback, useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'

import useEnumsData from 'src/hooks/data/use-enums-data'
import { AGENCY_TENANT, DEFAULT_MOUNT_PAGE_SIZE } from '~/core/constants/enum'
import { convertObjectWithEnum } from '~/core/utilities/common'

import useDetectCompanyWithKind from '~/lib/hooks/use-detect-company-with-kind'

import QueryDirectOpenJobsCareerHub from '../../career-hub/graphql/query-direct-open-jobs-chub'
import QueryOpenJobsCareerHub from '../../career-hub/graphql/query-open-jobs'
import { useInfinityGraphPage } from '../../jobs/hooks/use-infinity-graph-page'
import { SOURCE_JOB_STATUS_APPLIED } from '../../settings/referrals/utilities/enum'
import { trimObjectProps } from '../../tasks/utilities/common'
import QueryDirectOpenJobs from '../graphql/query-direct-open-jobs'
import QueryOpenJobs from '../graphql/query-open-jobs'
import type { IOpenJobsManagementFilter } from '../types'
import { JOB_KEY_DEFAULT } from '../utilities/enum'

export const PAGE_SIZE_OPEN_JOBS_MANAGEMENT = 10

const useOpenJobsManagement = (props: IOpenJobsManagementFilter) => {
  const { t, i18n } = useTranslation()
  const { isCompanyKind } = useDetectCompanyWithKind({ kind: AGENCY_TENANT })

  const jobLevel = useEnumsData({
    enumType: 'JobJobLevel',
    locale: i18n.language
  })
  const remoteStatusOptions = useEnumsData({
    enumType: 'JobRemoteStatus',
    locale: i18n.language
  })
  const [filterValue, onChangeFilter] = useState<IOpenJobsManagementFilter | undefined>({
    jobsKey: SOURCE_JOB_STATUS_APPLIED.map(i => ({
      value: i.value,
      supportingObj: {
        name: t(`careerHub:filter:${i.supportingObj.name}`)
      }
    })).find(fi => fi.value === JOB_KEY_DEFAULT),
    operator: props?.operator || 'or',
    search: props.search || undefined,
    countryStateIds: props?.countryStateIds || undefined,
    departmentIds: props?.departmentIds || undefined,
    talentPoolIds: props?.talentPoolIds || undefined,
    jobLevel: props?.jobLevel ? convertObjectWithEnum(jobLevel, [props?.jobLevel])?.[0] : undefined,
    remoteStatus: props?.remoteStatus ? convertObjectWithEnum(remoteStatusOptions, [props?.remoteStatus])?.[0] : undefined,
    tagIds: props?.tagIds || undefined,
    companyId: props?.companyId || undefined,
    isGetRecommendation: false || undefined
  })
  const jobsPaging = useInfinityGraphPage({
    queryDocumentNote: isCompanyKind
      ? filterValue?.isGetRecommendation
        ? QueryOpenJobsCareerHub
        : QueryOpenJobs
      : filterValue?.isGetRecommendation
        ? QueryDirectOpenJobsCareerHub
        : QueryDirectOpenJobs,
    getVariable: useCallback(
      (page: number) => {
        const {
          search,
          countryStateIds,
          departmentIds,
          talentPoolIds,
          jobsKey,
          jobLevel,
          tagIds,
          operator,
          remoteStatus,
          recommendWeight,
          companyId
        } = filterValue || {}

        return trimObjectProps({
          limit: DEFAULT_MOUNT_PAGE_SIZE,
          page,
          search,
          countryStateIds: (countryStateIds?.length || 0) > 0 ? countryStateIds?.map(item => parseInt(item.value)) : undefined,
          departmentIds: (departmentIds?.length || 0) > 0 ? departmentIds?.map(item => parseInt(item.value)) : undefined,
          talentPoolIds: (talentPoolIds?.length || 0) > 0 ? talentPoolIds?.map(item => parseInt(item.value)) : undefined,
          jobsKey: jobsKey?.value || JOB_KEY_DEFAULT,
          jobLevel: jobLevel?.value,
          remoteStatus: remoteStatus?.value,
          recommendWeight: recommendWeight,
          ...(tagIds?.length
            ? {
                operator,
                tagIds: tagIds?.map(item => parseInt(item.value))
              }
            : undefined),
          companyIds: companyId ? [Number(companyId.value)] : undefined
        })
      },
      [filterValue]
    ),
    getPageAttribute: (_lastGroup, groups) => ({
      totalCount: _lastGroup?.jobsReferableList?.metadata?.totalCount,
      pageLength: Number(groups?.[0]?.jobsReferableList?.collection?.length)
    }),
    queryKey: ['referral-open-jobs']
  })
  useEffect(() => {
    jobsPaging.refetch()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filterValue])
  return {
    openJobsPaging: jobsPaging,
    filterControl: useMemo(
      () => ({
        value: filterValue,
        onChange: onChangeFilter
      }),
      [filterValue]
    ),
    action: {},
    refetch: () => jobsPaging.refetch()
  }
}

export default useOpenJobsManagement
