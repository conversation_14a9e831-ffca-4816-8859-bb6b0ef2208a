import { gql } from 'urql'

import type { TypeNote } from '../../jobs/graphql/query-job-notes'

export type TalentPoolNoteQuery = {
  talentPoolNotesList: {
    collection: TypeNote[]
    metadata: { totalCount: number }
  }
}

export const QueryTalentPoolNotesList = gql<
  TalentPoolNoteQuery,
  {
    page?: number
    limit?: number
    talentPoolId: number
  }
>`
  query ($page: Int, $limit: Int, $talentPoolId: Int!) {
    talentPoolNotesList(page: $page, limit: $limit, talentPoolId: $talentPoolId) {
      collection {
        id
        content
        user {
          id
          email
          fullName
          avatarVariants
          defaultColour
        }
        createdAt
        updatedAt
        sharedUsers {
          id
          email
          fullName
          avatarVariants
          defaultColour
        }
        notePrivated
        attachments {
          id
          blobs
          file
        }
      }
      metadata {
        totalCount
      }
    }
  }
`
