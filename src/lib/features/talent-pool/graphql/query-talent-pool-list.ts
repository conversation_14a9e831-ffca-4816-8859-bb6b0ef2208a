import { gql } from 'urql'

import type { IQueryTalentPoolParam, ITalentPoolType } from '../types/talent-pool-type'

const QueryTalentPoolList = gql<
  {
    talentPoolsManagementList: {
      collection: Array<ITalentPoolType>
      metadata: {
        totalCount: number
      }
    }
  },
  IQueryTalentPoolParam
>`
  query ($page: Int, $limit: Int, $search: String, $status: TalentPoolStatus, $sorting: JSON, $talentPoolMembersIds: [Int!]) {
    talentPoolsManagementList(
      page: $page
      limit: $limit
      search: $search
      status: $status
      sorting: $sorting
      talentPoolMembersIds: $talentPoolMembersIds
    ) {
      collection {
        id
        name
        statusDescription
        status
        members {
          id
          fullName
          avatarVariants
          defaultColour
        }
        createdAt
        createdBy {
          id
          fullName
          avatarVariants
          defaultColour
        }
        profilesCount
        jobsCount
        state
        description
      }
      metadata {
        totalCount
      }
    }
  }
`

export default QueryTalentPoolList
