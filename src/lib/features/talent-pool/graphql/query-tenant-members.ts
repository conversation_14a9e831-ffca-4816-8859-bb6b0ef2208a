import { gql } from 'urql'

import type { OwnerType } from '../../requisitions/types/management-page-type'

const QueryTenantMembersTalentPool = gql<
  {
    tenantMembers: {
      collection: Array<OwnerType>
      metadata: {
        totalCount: number
      }
    }
  },
  {
    page?: number
    limit?: number
    search?: string
    jobOwnable?: boolean
  }
>`
  query ($page: Int, $limit: Int, $search: String, $jobOwnable: Boolean) {
    tenantMembers(page: $page, limit: $limit, search: $search, jobOwnable: $jobOwnable) {
      collection {
        id
        email
        fullName
        avatarVariants
        defaultColour
        roles {
          id
          name
        }
      }
      metadata {
        totalCount
      }
    }
  }
`

export default QueryTenantMembersTalentPool
