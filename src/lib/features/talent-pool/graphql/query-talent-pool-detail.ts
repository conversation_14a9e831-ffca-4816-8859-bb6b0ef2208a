import { gql } from 'urql'

import type { ITalentPoolType } from '../types/talent-pool-type'

const QueryTalentPoolDetail = gql<
  {
    talentPoolsShow: ITalentPoolType
  },
  {
    id: number
  }
>`
  query ($id: Int) {
    talentPoolsShow(id: $id) {
      id
      name
      statusDescription
      status
      members {
        id
        fullName
        avatarVariants
        defaultColour
        email
        roles {
          name
        }
      }
      createdAt
      profilesCount
      createdBy {
        id
      }
      state
      description
    }
  }
`

export default QueryTalentPoolDetail
