import { gql } from 'urql'

import type { IReportDetailApplication, IReportDetailFilter } from '~/lib/features/reports/types'

const QueryTalentPoolShowHiredApplicantsSummaryDetails = gql<
  {
    talentPoolsShowHiredApplicantsSummaryDetails: {
      data: Array<IReportDetailApplication>
      metadata: {
        totalCount: number
        currentPage: number
      }
    }
  },
  IReportDetailFilter
>`
  query ($id: Int!, $fromDatetime: ISO8601DateTime!, $toDatetime: ISO8601DateTime!, $page: Int, $limit: Int) {
    talentPoolsShowHiredApplicantsSummaryDetails(id: $id, fromDatetime: $fromDatetime, toDatetime: $toDatetime, page: $page, limit: $limit) {
      collection {
        id
        job {
          id
          currentUserAccessible
          title
          status
          statusDescription
          company {
            id
            permittedFields
          }
        }
        hiredDate
        flagNew
        profile {
          id
          fullName
          email
          avatarVariants
        }
      }

      metadata {
        totalCount
        currentPage
      }
    }
  }
`

export default QueryTalentPoolShowHiredApplicantsSummaryDetails
