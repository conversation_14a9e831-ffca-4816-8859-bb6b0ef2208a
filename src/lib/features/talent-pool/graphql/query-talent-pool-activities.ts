import { gql } from 'urql'

import { IUserInformation } from '~/core/@types/global'

import type { ActivityBase } from '../../activity/types'

const QueryTalentPoolActivities = gql<
  {
    talentPoolActivitiesList: {
      collection: ActivityBase[]
      metadata: { totalCount: number }
    }
  },
  { id: number; page: number; limit: number }
>`
  query ($id: Int!, $page: Int, $limit: Int) {
    talentPoolActivitiesList(id: $id, page: $page, limit: $limit) {
      collection {
        id
        actionKey
        properties
        payload
        createdAt
        loggedDate
        propertiesRelated
        user {
          id
          fullName
          avatarVariants
          defaultColour
        }
        payloadRelated
      }
      metadata {
        totalCount
      }
    }
  }
`

export default QueryTalentPoolActivities
