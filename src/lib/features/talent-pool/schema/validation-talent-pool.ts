import type { TFunction } from 'i18next'
import { z } from 'zod'

import { removeHTMLTags } from '~/core/utilities/common'

export const schemaTalentPool = (t: TFunction) => {
  return z.object({
    name: z
      .string()
      .min(1, {
        message: `${t('form:requiredField')}`
      })
      .max(80, {
        message: `${t('form:field_max_number_required', {
          number: 80
        })}`
      }),
    talentPoolMembersIds: z.array(z.object({ value: z.string() })).min(1, {
      message: `${t('form:requiredField')}`
    }),
    talentPoolState: z
      .object({
        value: z.string(),
        supportingObj: z.object({ name: z.string() })
      })
      .optional(),
    description: z.nullable(
      z
        .string()
        .refine(async content => removeHTMLTags(content || '').length <= 500, {
          message: `${t('form:field_max_number_required', {
            number: 500
          })}`
        })
        .optional()
    )
  })
}
