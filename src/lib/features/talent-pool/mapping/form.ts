import type { ITalentPoolParamType } from '../types/talent-pool-type'

const mappingAddTalentPoolModal = (data: ITalentPoolParamType) => {
  return {
    ...(data?.id ? { id: Number(data.id) } : undefined),
    name: data.name,
    talentPoolMembersIds: data?.talentPoolMembersIds?.map(item => Number(item.value)),
    state: data?.talentPoolState?.value,
    description: data.description
  }
}

export { mappingAddTalentPoolModal }
