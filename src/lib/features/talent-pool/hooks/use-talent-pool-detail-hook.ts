'use client'

import { useRouter } from 'next/navigation'
import { useCallback, useEffect, useMemo, useState } from 'react'

import type { IUserInformation } from '~/core/@types/global'
import useContextGraphQL from '~/core/middleware/use-context-graphQL'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'
import { changeTimezone } from '~/core/utilities/format-date'

import { useRouterContext } from '~/lib/next/use-router-context'

import QueryTalentPoolDetail from '../graphql/query-talent-pool-detail'
import type { ITalentPoolReportManagementFilter, ITalentPoolType } from '../types/talent-pool-type'

const useTalentPoolDetailHook = ({ id, user }: { id: number; user: IUserInformation }) => {
  const router = useRouter()
  const { pathname, params } = useRouterContext()
  const { clientGraphQL } = useContextGraphQL()
  const [talentPoolData, setTalentPoolData] = useState<ITalentPoolType>()
  const [filterValue, onChangeFilter] = useState<undefined | ITalentPoolReportManagementFilter>(() => {
    if (!user?.timezone) return {}

    const toDay = new Date()
    return {
      dateRange: {
        from: changeTimezone({
          date: new Date(toDay.setDate(toDay.getDate() - 29)),
          timezone: user.timezone
        }),
        to: changeTimezone({
          date: new Date(),
          timezone: user.timezone
        })
      }
    }
  })

  const fetchTalentPool = useCallback(() => {
    return clientGraphQL
      .query(QueryTalentPoolDetail, {
        id
      })
      .toPromise()
      .then(
        (result: {
          error: { graphQLErrors: Array<object>; networkError?: object }
          data: {
            talentPoolsShow: ITalentPoolType
          }
        }) => {
          if (result.error) {
            return catchErrorFromGraphQL({
              error: result.error,
              router: {
                push: router.push,
                pathname,
                params
              },
              error404ShouldForceToNotFoundPage: true
            })
          }

          return setTalentPoolData(result?.data?.talentPoolsShow)
        }
      )
  }, [id])

  useEffect(() => {
    id && fetchTalentPool()
  }, [id])

  return {
    talentPoolData,
    fetchTalentPool,
    updateLocalTalentPool: setTalentPoolData,
    filterControl: useMemo(
      () => ({
        value: filterValue,
        onChange: onChangeFilter
      }),
      [filterValue]
    )
  }
}

export default useTalentPoolDetailHook
