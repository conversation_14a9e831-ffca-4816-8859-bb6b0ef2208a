import type { ILogoAndAvatarVariants, ISelectOption } from '~/core/@types/global'

import type { IPermittedFields } from '../../candidates/types'

export type IQueryTalentPoolParam = Partial<{
  page: number
  limit: number
  search: string
  status: 'active' | 'archived'
  sorting: {
    createdAt?: 'desc' | 'asc'
  }
}>

export type IFilterTalentPool = Partial<{
  isFilterTouched: boolean
  search: string
  status: 'active' | 'archived'
  talentPoolMembersIds: ISelectOption[]
}>

export type ITalentPoolStatus = 'Archived' | 'Active'

export type ITalentPoolStatusValue = 'active' | 'archived'

export type ITalentPoolType = Partial<{
  id: string
  name: string
  description?: string
  statusDescription: string
  status: string
  members: Partial<{
    id: number
    fullName: string
    avatarVariants: ILogoAndAvatarVariants
    defaultColour: string
    email: string
    roles: Array<{
      name: string
    }>
  }>[]
  createdAt: string
  createdBy: {
    id: number
    fullName: string
    avatarVariants: ILogoAndAvatarVariants
    defaultColour: string
    email: string
    roles: Array<{
      name: string
    }>
  }
  updatedAt: string
  comments: Array<{
    id: number
  }>
  profilesCount: number
  jobsCount: number
  state: string
}>
export type ITalentPoolParamType = {
  id?: string
  name: string
  talentPoolMembersIds: ISelectOption[]
  status?: string
  talentPoolState?: ISelectOption
  description?: string
}

export interface ITalentPoolReportManagementFilter {
  id?: number
  dateRange?: {
    from?: string | Date
    to?: string | Date
  }
}

export interface ITalentPoolReportOvertime {
  from_date: string
  group: string
  groupped_date: string
  summary: { profiles_count: number; jobs_count: number }
  to_date: string
}

export type ITalentPoolReportDetailProfile = {
  id: number
  fullName?: string
  email?: string
  phoneNumber?: string
  avatarVariants?: ILogoAndAvatarVariants
  createdAt?: string
  updatedAt?: string
  links?: {
    [key: string]: Array<string>
  }
  tags: Array<{
    name: string
  }>
  permittedFields?: IPermittedFields
}
