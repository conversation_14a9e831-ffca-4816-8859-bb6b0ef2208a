import { endOfDay, startOfDay } from 'date-fns'

import configuration from '~/configuration'
import type { IUserInformation } from '~/core/@types/global'
import { TAB_MEMBERS } from '~/core/constants/enum'
import type { ISelectOption } from '~/core/ui/Select'
import { formatDatePickerToDate } from '~/core/ui/SingleDateWithYearOnlyPicker'

import { addTzToDate } from '../../calendar/utilities/helper-schedule-interview'
import { mappingTypeToFieldKind } from '../../settings/profile-fields/mapping/custom-field-mapping'
import type { IFilterPlacement, IPlacementFilters } from '../types/management-page-type'
import { FILTER_PLACEMENT_FIELDS_CONDITION, FILTER_PLACEMENT_FIELDS_VALUE, PLACEMENT_YES_NO } from '../utilities/enum'

type IValueSearch = ISelectOption | ISelectOption[] | { from: string; to: string } | string | number

export const mappingAdvancedFilterPlacement = (pageParam: IFilterPlacement, user?: IUserInformation) => {
  const {
    isFilterTouched, // don't need use this field
    page,
    search,
    operator,
    profile_full_name,
    applicant_statuses,
    job_id,
    company_id,
    status,
    hired_date,
    onboard_date,
    end_of_probation_date,
    salary,
    fee,
    typeFee,
    revenue,
    profit_splits,
    public_id,
    hired_by_id,
    created_by_id,
    tabsMembersTeams,
    comments,
    fieldsFilter,
    sorting,
    ...restCustomField
  } = pageParam
  const timeZone = user?.timezone || 'Asia/Saigon'
  const arrFieldsFilter: Array<IPlacementFilters> = []
  let relatedObjects: any

  const formatDateValue = ({ value }: { value: IValueSearch }) => {
    const formatRangeSelection = value as {
      from?: string
      to?: string
    }
    if (formatRangeSelection.from && formatRangeSelection.to) {
      return `${addTzToDate(String(startOfDay(new Date(formatRangeSelection?.from))), timeZone)} - ${addTzToDate(
        String(endOfDay(new Date(formatRangeSelection?.to))),
        timeZone
      )}`
    }

    const formatSelection = value as {
      year?: number
      month?: number
      date?: number
    }

    if (formatSelection.year && !formatSelection.month && !formatSelection.date) {
      return formatSelection.year
    }

    if (!formatSelection.year && !formatSelection.month && !formatSelection.date) {
      if (Object.keys(formatSelection).length === 0) {
        return addTzToDate(String(startOfDay(new Date(formatSelection as string))), timeZone)
      }

      return ''
    }

    return addTzToDate(
      String(
        startOfDay(
          formatDatePickerToDate({
            year: formatSelection?.year,
            month: formatSelection?.month,
            date: formatSelection?.date
          })
        )
      ),
      timeZone
    )
  }

  const formattedValue = ({ key, customFieldKind, value }: { key: string; customFieldKind?: string; value: IValueSearch }) => {
    if (key === 'custom_field') {
      if (customFieldKind === 'toggle') {
        const formatSelection = value as ISelectOption
        return formatSelection.value === PLACEMENT_YES_NO[0]?.value ? true : false
      }

      if (customFieldKind === 'select') {
        const formatSelection = value as ISelectOption
        return [String(formatSelection.value)]
      }

      if (customFieldKind === 'date') {
        return formatDateValue({ value })
      }

      if (customFieldKind === 'number') {
        return value
      }

      if (isFinite(Number(value))) {
        return Number(value)
      }

      if (customFieldKind === 'multiple') {
        const formatSelection = value as ISelectOption[]
        return formatSelection.map(val => val.value)
      }

      return value
    }

    if (
      [
        FILTER_PLACEMENT_FIELDS_VALUE.hired_date,
        FILTER_PLACEMENT_FIELDS_VALUE.onboard_date,
        FILTER_PLACEMENT_FIELDS_VALUE.end_of_probation_date
      ].includes(key)
    ) {
      return formatDateValue({ value })
    }

    // if (key === FILTER_PLACEMENT_FIELDS_VALUE.phone_number) {
    //   return value
    // }

    if (typeof value === 'object') {
      if (Array.isArray(value)) {
        const formatSelection = value as ISelectOption[]
        if (key === FILTER_PLACEMENT_FIELDS_VALUE.created_by_id && formatSelection.length) {
          // handle for case tab members/teams
          return {
            tab: tabsMembersTeams || TAB_MEMBERS,
            value:
              formatSelection.map(item => {
                return Number(item.value)
              }) || undefined
          }
        }
        return (
          formatSelection.map(item => {
            if ([PLACEMENT_YES_NO[0]?.value, PLACEMENT_YES_NO[1]?.value].includes(String(item.value))) {
              return item.value === PLACEMENT_YES_NO[0]?.value ? true : false
            }

            if (isFinite(Number(item.value))) {
              return Number(item.value)
            }

            return String(item.value)
          }) || undefined
        )
      } else {
        const formatSelection = value as ISelectOption
        if ([PLACEMENT_YES_NO[0]?.value, PLACEMENT_YES_NO[1]?.value].includes(String(formatSelection.value))) {
          return formatSelection.value === PLACEMENT_YES_NO[0]?.value ? true : false
        }

        if (isFinite(Number(formatSelection.value))) {
          return Number(formatSelection.value)
        }

        return String(formatSelection.value)
      }
    }

    if ([PLACEMENT_YES_NO[0]?.value, PLACEMENT_YES_NO[1]?.value].includes(String(value))) {
      return value === PLACEMENT_YES_NO[0]?.value ? true : false
    }

    if (isFinite(Number(value)) && key === 'fee') {
      return {
        value: Number(value),
        type: typeFee?.value
      }
    }

    if (isFinite(Number(value))) {
      return Number(value)
    }

    return value
  }

  Object.keys(FILTER_PLACEMENT_FIELDS_VALUE).forEach(key => {
    const valueSearch = pageParam[key as keyof IFilterPlacement]
    const findElement = fieldsFilter?.find(item => item.field === key)

    if (findElement?.id) {
      if ([FILTER_PLACEMENT_FIELDS_CONDITION.isEmpty, FILTER_PLACEMENT_FIELDS_CONDITION.isNotEmpty].includes(String(findElement?.direction))) {
        arrFieldsFilter.push({
          fieldType: String(findElement.id),
          object: findElement?.field,
          direction: findElement?.direction,
          value: undefined,
          id: undefined
        })
      } else if (valueSearch) {
        if (
          [
            FILTER_PLACEMENT_FIELDS_VALUE.hired_date,
            FILTER_PLACEMENT_FIELDS_VALUE.onboard_date,
            FILTER_PLACEMENT_FIELDS_VALUE.end_of_probation_date
          ].includes(key)
        ) {
          const value = formatDateValue({
            value: valueSearch as IValueSearch
          })

          if (value) {
            arrFieldsFilter.push({
              fieldType: String(findElement.id),
              object: findElement?.field,
              direction: findElement?.direction,
              value: formattedValue({
                key: String(findElement?.field),
                customFieldKind: '',
                value: valueSearch as IValueSearch
              }),
              id: undefined
            })
          }
        } else {
          const formatValue = formattedValue({
            key: String(findElement?.field),
            customFieldKind: '',
            value: valueSearch as IValueSearch
          })
          if (Array.isArray(formatValue)) {
            if (formatValue.length) {
              arrFieldsFilter.push({
                fieldType: String(findElement.id),
                object: findElement?.field,
                direction: findElement?.direction,
                value: formatValue,
                id: undefined
              })
            }
          } else {
            arrFieldsFilter.push({
              fieldType: String(findElement.id),
              object: findElement?.field,
              direction: findElement?.direction,
              value: formatValue,
              id: undefined
            })
          }
        }
      }

      relatedObjects = {
        ...relatedObjects,
        [String(findElement?.field)]: valueSearch
      }
    }
  })

  if (Object.keys(restCustomField).length) {
    Object.keys(restCustomField).forEach(key => {
      const valueSearch = pageParam[key as keyof IFilterPlacement]
      const findElement = fieldsFilter?.find(item => item.field === key)
      const splitCustomField = findElement?.id?.split('_')
      const customFieldKind = splitCustomField?.[0]
      const customFieldId = splitCustomField?.[1]

      if (customFieldId && valueSearch) {
        if (![FILTER_PLACEMENT_FIELDS_CONDITION.isEmpty, FILTER_PLACEMENT_FIELDS_CONDITION.isNotEmpty].includes(String(findElement?.direction))) {
          if (customFieldKind === 'date') {
            const value = formatDateValue({
              value: valueSearch as IValueSearch
            })

            if (value) {
              arrFieldsFilter.push({
                fieldType: 'custom_field',
                fieldKind: mappingTypeToFieldKind(String(customFieldKind)),
                object: '',
                direction: findElement?.direction,
                value: formattedValue({
                  key: 'custom_field',
                  customFieldKind,
                  value: valueSearch as IValueSearch
                }),
                id: Number(customFieldId)
              })
            }
          } else {
            const formatValue = formattedValue({
              key: 'custom_field',
              customFieldKind,
              value: valueSearch as IValueSearch
            })
            if (Array.isArray(formatValue)) {
              if (formatValue.length) {
                arrFieldsFilter.push({
                  fieldType: 'custom_field',
                  fieldKind: mappingTypeToFieldKind(String(customFieldKind)),
                  object: '',
                  direction: findElement?.direction,
                  value: formatValue,
                  id: Number(customFieldId)
                })
              }
            } else {
              arrFieldsFilter.push({
                fieldType: 'custom_field',
                fieldKind: mappingTypeToFieldKind(String(customFieldKind)),
                object: '',
                direction: findElement?.direction,
                value: formatValue,
                id: Number(customFieldId)
              })
            }
          }

          relatedObjects = {
            ...relatedObjects,
            [String(findElement?.field)]: valueSearch
          }
        }
      }
    })
  }

  if (fieldsFilter?.length) {
    const filterCustomFields = fieldsFilter.filter(item => !['default_field', 'system_field'].includes(String(item.id)))
    if (filterCustomFields.length) {
      filterCustomFields.forEach(field => {
        const splitCustomField = field?.id?.split('_')
        const customFieldKind = splitCustomField?.[0]
        const customFieldId = splitCustomField?.[1]

        if (customFieldId) {
          if ([FILTER_PLACEMENT_FIELDS_CONDITION.isEmpty, FILTER_PLACEMENT_FIELDS_CONDITION.isNotEmpty].includes(String(field?.direction))) {
            arrFieldsFilter.push({
              fieldType: 'custom_field',
              fieldKind: mappingTypeToFieldKind(String(customFieldKind)),
              object: '',
              direction: field?.direction,
              value: undefined,
              id: Number(customFieldId)
            })

            relatedObjects = {
              ...relatedObjects,
              [String(field?.field)]: undefined
            }
          }
        }
      })
    }
  }

  return {
    limit: configuration.defaultPageSize,
    page,
    operator,
    sorting,
    search,
    fieldsFilter: arrFieldsFilter,
    relatedObjects: relatedObjects
  }
}
