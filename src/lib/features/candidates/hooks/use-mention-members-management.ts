import { useCallback } from 'react'

import configuration from '~/configuration'
import type { ILogoAndAvatarVariants, IRouterWithID } from '~/core/@types/global'
import type { IResponseContextResult } from '~/core/middleware/use-context-graphQL'
import useContextGraphQL from '~/core/middleware/use-context-graphQL'
import type { AvatarSize } from '~/core/ui/Avatar'

import QueryHiringMembersList from '../graphql/query-hiring-members-list'

interface IMentionProps {
  profileId?: IRouterWithID
  applicantId?: IRouterWithID
  jobId?: number
  talentPoolId?: number
}

export const useMentionMemberManagement = (props: IMentionProps) => {
  const { profileId, applicantId, jobId, talentPoolId } = props
  const { clientGraphQL } = useContextGraphQL()

  const getMentionMemberIdsFromHTMLString = useCallback((htmlContent: string) => {
    const parser = new DOMParser()
    const html = parser.parseFromString(htmlContent, 'text/html')
    const body = html.body

    const mentionEles = body.querySelectorAll('[data-type*="mention"]')
    const mentionIds = [] as Array<number>
    ;(mentionEles || []).forEach(ele => {
      const memberId = Number(ele.getAttribute('data-id'))
      !mentionIds.includes(memberId) && mentionIds.push(memberId)
    })

    return mentionIds
  }, [])

  const promiseMentionMembersOptions = useCallback(
    (params = {}) =>
      clientGraphQL
        .query(QueryHiringMembersList, {
          ...params,
          ...(profileId && !applicantId ? { profileId: Number(profileId) } : undefined),
          ...(applicantId ? { applicantId: Number(applicantId) } : undefined),
          ...(jobId ? { jobId: Number(jobId) } : undefined),
          ...(talentPoolId ? { talentPoolId: talentPoolId } : undefined)
        })
        .toPromise()
        .then(
          (
            result: IResponseContextResult<{
              id: number
              avatarVariants?: ILogoAndAvatarVariants
              avatarSize?: AvatarSize
              roles: { name: string }[]
              fullName: string
              email: string
              defaultColour: string
            }>
          ) => {
            if (result.error) {
              return {
                metadata: {
                  totalCount: configuration.defaultAsyncLoadingOptions
                },
                collection: []
              }
            }

            const { hiringMembersList } = result.data
            const collection = hiringMembersList?.collection || []
            const metadata = hiringMembersList?.metadata || { totalCount: 0 }

            const cloneData = collection.map(item => {
              return {
                value: String(item.id),
                avatar: item.avatarVariants?.thumb?.url,
                avatarVariants: item.avatarVariants,
                avatarSize: 'md',
                label: item?.roles?.[0]?.name,
                supportingObj: {
                  name: item.fullName || item.email,
                  description: item.email,
                  defaultColour: item.defaultColour
                }
              }
            })
            return { metadata, collection: cloneData }
          }
        ),
    [clientGraphQL]
  )
  return {
    getMentionMemberIdsFromHTMLString,
    promiseMentionMembersOptions
  }
}
