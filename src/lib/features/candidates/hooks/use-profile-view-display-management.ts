import { useCallback, useMemo, useState } from 'react'

import configuration from '~/configuration'
import useQueryGraphQL from '~/core/middleware/use-query-graphQL'

import { useSubmitCommon } from '~/lib/hooks/use-submit-graphql-common'

import { useInfinityGraphPage } from '../../jobs/hooks/use-infinity-graph-page'
import MutationAddProfileViewDisplay from '../graphql/mutation-add-profile-view-display'
import MutationDeleteProfileViewDisplay from '../graphql/mutation-delete-profile-view-display'
import MutationUpdateProfileViewDisplay from '../graphql/mutation-update-profile-view-display'
import QueryProfileViewDisplayList from '../graphql/query-profile-view-display-list'
import QueryProfileViewDisplayShow from '../graphql/query-profile-view-display-show'
import type { IProfileViewDisplay } from '../types'

const trimObjectProps = <
  T extends {
    [key: string]: number | number[] | string | string[] | boolean | undefined
  }
>(
  props: T
): Partial<T> =>
  Object.keys(props).reduce(
    (result, key) => ({
      ...result,
      ...(props[key] ? { [key]: props[key] } : {})
    }),
    {}
  )

const useProfileViewDisplayManagement = ({ viewId }: { viewId: number }) => {
  const queryKey = 'profile-view-display-management-default'
  const [filterValue, onChangeFilter] = useState<{
    page?: number
    search?: string | number
  }>()

  const { data: profileShow, trigger: fetchProfileViewShow } = useQueryGraphQL({
    query: QueryProfileViewDisplayShow,
    variables: { id: Number(viewId) },
    shouldPause: true
  })
  const { trigger: triggerDelete, isLoading: isLoadingDelete } = useSubmitCommon(MutationDeleteProfileViewDisplay)
  const { trigger: triggerCreate, isLoading: isLoadingCreate } = useSubmitCommon(MutationAddProfileViewDisplay)
  const { trigger: triggerUpdate, isLoading: isLoadingUpdate } = useSubmitCommon(MutationUpdateProfileViewDisplay)

  const profileViewDisplayPaging = useInfinityGraphPage({
    queryDocumentNote: QueryProfileViewDisplayList,
    getVariable: useCallback(
      page => {
        const { search } = filterValue || {}
        return trimObjectProps({
          limit: configuration.defaultPageSize,
          page,
          ...(search ? { search } : {})
        })
      },
      [filterValue]
    ),
    getPageAttribute: (_lastGroup, groups) => ({
      totalCount: _lastGroup?.userSettingsProfileViewsList?.metadata?.totalCount,
      pageLength: Number(groups?.[0]?.userSettingsProfileViewsList?.collection?.length)
    }),
    queryKey: [queryKey],
    enabled: false
  })

  return {
    profileViewDisplayShow: {
      data: profileShow,
      fetch: fetchProfileViewShow
    },
    queryKey,
    profileViewDisplayPaging,
    filterControl: useMemo(() => ({ value: filterValue, onChange: onChangeFilter }), [filterValue]),
    action: {
      create: {
        profileView: (args: IProfileViewDisplay) => triggerCreate(args),
        isLoading: isLoadingCreate
      },
      update: {
        profileView: (args: IProfileViewDisplay) => triggerUpdate(args),
        isLoading: isLoadingUpdate
      },
      delete: {
        profileView: (args: IProfileViewDisplay) => triggerDelete(args),
        isLoading: isLoadingDelete
      }
    }
  }
}

export default useProfileViewDisplayManagement
