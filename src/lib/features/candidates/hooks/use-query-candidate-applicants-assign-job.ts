import { TFunction } from 'i18next'
import { useTranslation } from 'react-i18next'
import type { AnyVariables, TypedDocumentNode } from 'urql'

import useQueryGraphQL from '~/core/middleware/use-query-graphQL'

import type { ICompanyPermittedFields } from '../../agency/companies/types'
import { descriptionLocationSelectJob } from '../../jobs/utilities/common'

export function useQueryApplicantAssignJob({
  query,
  variables,
  shouldPause = true,
  isCompanyKind
}: {
  query: TypedDocumentNode<
    {
      jobsApplicableList: {
        collection: Array<{
          id: number
          title: string
          jobLocations: {
            address: string
            city: string
            state: string
            country: string
          }[]
          department: {
            name: string
          }
          company: {
            permittedFields?: ICompanyPermittedFields
          }
        }>
        metadata: {
          totalCount: number
        }
      }
    },
    AnyVariables
  >
  variables: object
  shouldPause?: boolean
  isCompanyKind?: boolean
}) {
  const { t } = useTranslation()

  const {
    trigger,
    isLoading,
    data: response,
    error
  } = useQueryGraphQL({
    query,
    variables,
    shouldPause
  })
  const data = (response?.jobsApplicableList?.collection || []).map(
    (job: {
      id: number
      title: string
      jobLocations: {
        address: string
        city: string
        state: string
        country: string
      }[]
      department: {
        name: string
      }
      permittedFields?: {
        [key: string]: {
          role_changeable?: boolean
          visibility_changeable?: boolean
          roles?: Array<string>
          value?: string & { name?: string; id?: string }
        }
      }
    }) => ({
      value: String(job.id),
      supportingObj: {
        name: job.title,
        shortName: job.title,
        description: isCompanyKind ? job?.permittedFields?.company?.value?.name : job?.department?.name,
        descriptionHelpName: descriptionLocationSelectJob(job?.jobLocations, t)
      }
    })
  )

  return {
    trigger,
    isLoading,
    data,
    error
  }
}
