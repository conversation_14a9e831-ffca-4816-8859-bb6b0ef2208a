import configuration from '~/configuration'
import { useAgencyGraphQLRequest } from '~/core/hooks/use-agency-graphQL'
import type { IResponseContextResult } from '~/core/middleware/use-context-graphQL'
import type { IPromiseSearchOption } from '~/core/ui/Select'

import QueryEmailTemplates from '../../settings/email-templates/graphql/query-email-templates'
import type { IEmailResponseType } from '../../settings/email-templates/types'
import { EMAIL_TEMPLATE_TAG_KIND } from '../../settings/email-templates/utilities/enum'
import QueryEmailTemplatesSendToClient from '../graphql/query-email-templates-send-to-client'

export function useQueryEmailTemplateSendToClient() {
  const { clientAgencyGraphQL } = useAgencyGraphQLRequest()
  const promiseEmailTemplatesSendToClient = () =>
    clientAgencyGraphQL
      .query(QueryEmailTemplatesSendToClient, {})
      .toPromise()
      .then(
        (result: {
          error: any
          data: {
            stcEmailTemplate: {
              name: string
              body: string
              subject: string
            }
          }
        }) => {
          if (result.error) {
            return {
              metadata: {
                totalCount: configuration.defaultAsyncLoadingOptions
              },
              collection: []
            }
          }

          const { stcEmailTemplate } = result.data

          return {
            metadata: { totalCount: 1 },
            collection: [stcEmailTemplate]
          }
        }
      )

  const promiseEmailTemplates = (params = {} as IPromiseSearchOption) =>
    new Promise<IEmailResponseType>(resolve => {
      return clientAgencyGraphQL
        .query(QueryEmailTemplates, {
          ...params,
          tag: EMAIL_TEMPLATE_TAG_KIND.sentToClient
        })
        .toPromise()
        .then((result: IResponseContextResult<any>) => {
          if (result.error) {
            return resolve({
              metadata: {
                totalCount: configuration.defaultAsyncLoadingOptions
              },
              collection: []
            })
          }

          const { emailTemplatesList } = result.data
          const collection = emailTemplatesList?.collection || []
          const metadata = emailTemplatesList?.metadata || { totalCount: 0 }
          const cloneData = collection.map(item => {
            return {
              value: item.id,
              name: item.name,
              body: item.body,
              emailKind: item.emailKind,
              default: item.default,
              subject: item.subject,
              createdBy: item.createdBy,
              supportingObj: {
                name: item.name
              }
            }
          })

          return resolve({ metadata, collection: cloneData })
        })
    })

  return { promiseEmailTemplatesSendToClient, promiseEmailTemplates }
}
