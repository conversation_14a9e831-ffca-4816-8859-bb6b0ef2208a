import configuration from '~/configuration'
import type { IPromiseSearchOption } from '~/core/@types/global'
import { AGENCY_TENANT } from '~/core/constants/enum'
import type { IResponseContextResult } from '~/core/middleware/use-context-graphQL'
import useContextGraphQL from '~/core/middleware/use-context-graphQL'

import useDetectCompanyWithKind from '~/lib/hooks/use-detect-company-with-kind'

import QueryContactsList from '../../agency/contacts/graphql/query-contact-list'
import QueryAgencyProfilesList from '../graphql/query-agency-profiles-list'
import QueryProfilesList from '../graphql/query-profiles-list'

const useCallLogsManagement = () => {
  const { clientGraphQL } = useContextGraphQL()
  const { isCompanyKind } = useDetectCompanyWithKind({ kind: AGENCY_TENANT })
  const url = isCompanyKind ? QueryAgencyProfilesList : QueryProfilesList

  const promiseCandidatesOptions = (params = {} as IPromiseSearchOption) =>
    new Promise<any>(resolve => {
      return clientGraphQL
        .query(url, params)
        .toPromise()
        .then((result: IResponseContextResult<any>) => {
          if (result.error) {
            resolve({
              metadata: {
                totalCount: configuration.defaultAsyncLoadingOptions
              },
              collection: []
            })
          }

          const { profilesList } = result.data
          const collection = profilesList?.collection || []
          const metadata = profilesList?.metadata || { totalCount: 0 }

          const cloneData = collection.map(item => {
            return {
              value: item.id,
              supportingObj: {
                name: item.fullName
              },
              label: item.fullName
            }
          })

          return resolve({ metadata, collection: cloneData })
        })
    })

  const promiseContactOptions = (params = {} as IPromiseSearchOption) =>
    new Promise<any>(resolve => {
      return clientGraphQL
        .query(QueryContactsList, params)
        .toPromise()
        .then((result: IResponseContextResult<any>) => {
          if (result.error) {
            resolve({
              metadata: {
                totalCount: configuration.defaultAsyncLoadingOptions
              },
              collection: []
            })
          }

          const { contactsList } = result.data
          const collection = contactsList?.collection || []
          const metadata = contactsList?.metadata || { totalCount: 0 }

          const cloneData = collection.map(item => {
            return {
              value: item.id,
              supportingObj: {
                name: item.permittedFields.firstName.value
              }
            }
          })

          return resolve({ metadata, collection: cloneData })
        })
    })

  return {
    promiseCandidatesOptions,
    promiseContactOptions
  }
}

export default useCallLogsManagement
