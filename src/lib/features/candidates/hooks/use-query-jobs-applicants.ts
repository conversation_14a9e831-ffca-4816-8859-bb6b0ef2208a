import { useTranslation } from 'react-i18next'

import configuration from '~/configuration'
import type { IPromiseSearchOption, IRouterWithID } from '~/core/@types/global'
import type { IResponseContextResult } from '~/core/middleware/use-context-graphQL'
import useContextGraphQL from '~/core/middleware/use-context-graphQL'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'

import { descriptionLocationSelectJob } from '../../jobs/utilities/common'
import QueryCandidateApplicants from '../graphql/query-candidate-applicants'
import type { ICandidateApplicant } from '../types'

const useJobsApplications = ({ jobStatuses, id }: { jobStatuses?: Array<string>; id?: IRouterWithID }) => {
  const { t } = useTranslation()
  const { clientGraphQL } = useContextGraphQL()

  const promiseJobsOptions = (params = {} as IPromiseSearchOption) =>
    new Promise<any>(resolve => {
      clientGraphQL
        .query(QueryCandidateApplicants, {
          ...params,
          ...(jobStatuses ? { jobStatuses } : undefined),
          profileId: Number(id),
          limit: 50
        })
        .toPromise()
        .then((result: IResponseContextResult<ICandidateApplicant>) => {
          if (result.error) {
            catchErrorFromGraphQL({
              error: result.error
            })

            return {
              data: [],
              meta: {
                totalRowCount: 0,
                pageSize: configuration.defaultPageSize
              }
            }
          }

          const { profileApplicantsList } = result.data
          const collection = profileApplicantsList?.collection || []
          const metadata = profileApplicantsList?.metadata || { totalCount: 0 }

          const cloneData = collection.map((applicant: ICandidateApplicant) => {
            return {
              value: String(applicant?.jobId),
              supportingObj: {
                name: applicant?.job?.title || '',
                shortName: applicant?.job.title || '',
                description: applicant?.job?.department?.name,
                descriptionHelpName: descriptionLocationSelectJob(applicant?.job?.jobLocations, t)
              },
              owner: {
                email: applicant?.job?.owner?.email,
                fullName: applicant?.job?.owner?.fullName
              }
            }
          })

          return resolve({ metadata, collection: cloneData })
        })
    })

  return {
    promiseJobsOptions
  }
}

export default useJobsApplications
