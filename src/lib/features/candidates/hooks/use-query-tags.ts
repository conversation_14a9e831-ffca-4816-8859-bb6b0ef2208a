import { useCallback } from 'react'
import { useTranslation } from 'react-i18next'
import type { ActionMeta } from 'react-select'

import type { IToast } from '~/core/@types/global'
import type { ISelectOption } from '~/core/ui/Select'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'

import MutationAddTagMutation from '~/lib/features/candidates/graphql/mutation-add-tag-to-profile'
import MutationDeleteTagMutation from '~/lib/features/candidates/graphql/mutation-delete-tag-to-profile'
import QueryAddTagMutation from '~/lib/features/settings/tags/graphql/submit-add-tag-mutation'
import { TAG_KIND } from '~/lib/features/settings/tags/utilities/enum'
import { useSubmitCommon } from '~/lib/hooks/use-submit-graphql-common'

const useTagsProfile = ({ setToast }: { setToast: (state: IToast) => void }) => {
  const { t } = useTranslation()

  const { trigger: triggerAddTags, isLoading: isLoadingAddTags } = useSubmitCommon(MutationAddTagMutation)
  const { trigger: triggerDeleteTags, isLoading: isLoadingDeleteTags } = useSubmitCommon(MutationDeleteTagMutation)
  const { trigger: triggerCreateTags, isLoading: isLoadingCreateTags } = useSubmitCommon(QueryAddTagMutation)

  const onAddTags = useCallback(async (tagIds: number[], id: string) => {
    return triggerAddTags({
      profileId: Number(id),
      tagIds
    }).then(result => {
      if (result.error) {
        return catchErrorFromGraphQL({
          error: result.error,
          setToast
        })
      }

      const { tagsAddToProfile } = result.data
      if (tagsAddToProfile.success) {
        setToast({
          open: true,
          type: 'success',
          title: `${t('notification:candidates:tags:success_update')}`
        })
      }

      return
    })
  }, [])

  const onDeleteTags = useCallback(
    async (tagIds: number[], id: string) => {
      if (isLoadingDeleteTags) {
        return
      }

      return triggerDeleteTags({
        profileId: Number(id),
        tagIds
      }).then(result => {
        if (result.error) {
          return catchErrorFromGraphQL({
            error: result.error,
            setToast
          })
        }

        const { tagsRemoveFromProfile } = result.data
        if (tagsRemoveFromProfile.success) {
          setToast({
            open: true,
            type: 'success',
            title: `${t('notification:candidates:tags:success_update')}`
          })
        }

        return
      })
    },
    [isLoadingDeleteTags]
  )

  const onCreateTags = useCallback(
    async (name: string, id: string) => {
      if (isLoadingCreateTags) {
        return
      }

      return triggerCreateTags({
        name,
        kind: TAG_KIND.profile
      }).then(result => {
        if (result.error) {
          return catchErrorFromGraphQL({
            error: result.error,
            setToast
          })
        }

        const { tagsCreate } = result.data
        if (tagsCreate.tag) {
          // Add tag to profile
          // id: profileId
          onAddTags([Number(tagsCreate.tag.id)], id)
        }

        return tagsCreate.tag
      })
    },
    [isLoadingCreateTags]
  )

  const onSubmitTags = (actionMeta: ActionMeta<ISelectOption>, id: string): Promise<any> => {
    const tagSelected = Number(actionMeta?.option?.value)
    if (actionMeta.action === 'select-option') {
      return onAddTags([tagSelected], id)
    }
    if (actionMeta.action === 'deselect-option') {
      return onDeleteTags([tagSelected], id)
    }
    if (actionMeta.action === 'create-option') {
      return onCreateTags(actionMeta?.option?.value, id)
    }
    return Promise.resolve()
  }

  const validationTag = (actionMeta: ActionMeta<ISelectOption>, value: ISelectOption[]) => {
    if (actionMeta.option?.value && actionMeta?.option?.value?.trim().length < 1) {
      setToast({
        open: true,
        type: 'error',
        title: `${t('notification:candidates:tags:invalid_value')}`
      })
    } else if (actionMeta.option?.value && actionMeta?.option?.value?.length > 30) {
      setToast({
        open: true,
        type: 'error',
        title: `${t('notification:candidates:tags:field_max_number', {
          number: 30
        })}`
      })
    } else {
      return true
    }
    return false
  }

  return {
    onAddTags,
    onDeleteTags,
    onCreateTags,
    onSubmitTags,
    validationTag
  }
}

export default useTagsProfile
