'use client'

import { useEffect, useMemo } from 'react'
import type { AnyVariables, TypedDocumentNode } from 'urql'

import configuration from '~/configuration'
import type { IResponseContextResult } from '~/core/middleware/use-context-graphQL'
import useContextGraphQL from '~/core/middleware/use-context-graphQL'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'

import { useInfinityQuerySearch } from '~/lib/hooks/use-infinity-query-search'

import QueryCandidateApplicants from '../graphql/query-candidate-applicants'
import type { ICandidateApplicant } from '../types'

export function useQueryCandidateApplicants({
  variables,
  graphQLQuery,
  keyGraphQL
}: {
  graphQLQuery?: TypedDocumentNode<any, AnyVariables>
  keyGraphQL?: string
  variables: {
    profileId: number
    page: number
    limit: number
    jobStatuses?: string[]
    sorting?: {
      [key: string]: string
    }
  }
}) {
  const { clientGraphQL } = useContextGraphQL()

  const fetchData = async (params = {}) => {
    return clientGraphQL
      .query(graphQLQuery || QueryCandidateApplicants, {
        ...params,
        ...(variables?.sorting ? { sorting: variables.sorting } : undefined)
      })
      .toPromise()
      .then((result: IResponseContextResult<ICandidateApplicant>) => {
        if (result.error) {
          catchErrorFromGraphQL({
            error: result.error
          })

          return {
            data: [],
            meta: {
              totalRowCount: 0,
              pageSize: configuration.defaultPageSize
            }
          }
        }

        if (keyGraphQL) {
          const { collection, metadata } = result.data[keyGraphQL] || {
            collection: [],
            metadata: { totalCount: 0 }
          }
          return {
            data: collection,
            meta: {
              totalRowCount: metadata.totalCount,
              pageSize: configuration.defaultPageSize
            }
          }
        } else {
          const { profileApplicantsList } = result.data
          const collection = profileApplicantsList?.collection || []
          const metadata = profileApplicantsList?.metadata || { totalCount: 0 }
          return {
            data: collection,
            meta: {
              totalRowCount: metadata.totalCount,
              pageSize: configuration.defaultPageSize
            }
          }
        }
      })
  }
  const { data, fetchNextPage, refetch, hasNextPage, isFetching, isLoading, isFetchedAfterMount, updateLocalRecord } =
    useInfinityQuerySearch<ICandidateApplicant>({
      configuration,
      fetchData,
      queryKey: variables
    })
  useEffect(() => {
    refetch()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [variables.profileId])

  return {
    trigger: refetch,
    isLoading,
    fetchNextPage,
    hasNextPage,
    isFetching,
    data: useMemo(() => data?.pages.reduce<Array<ICandidateApplicant>>((all, page) => [...all, ...page.data], []), [data]),
    totalRowCount: data?.pages?.[0]?.meta?.totalRowCount,
    updateLocalRecord
  }
}
