import { useMemo } from 'react'
import type { AnyVariables, TypedDocumentNode } from 'urql'

import configuration from '~/configuration'
import type { IResponseContextResult } from '~/core/middleware/use-context-graphQL'
import useContextGraphQL from '~/core/middleware/use-context-graphQL'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'

import { useInfinityQuerySearch } from '~/lib/hooks/use-infinity-query-search'

import QueryEmailApplicantCandidateList from '../../calendar/graphql/query-email-applicant-candidate-list'
import type { IEmailCandidate } from '../../calendar/types'

export function useQueryCandidateEmails({
  variables,
  graphQLQuery,
  keyGraphQL
}: {
  graphQLQuery?: TypedDocumentNode<any, AnyVariables>
  keyGraphQL?: string
  variables: {
    id?: number
    page?: number
    limit?: number
    profileId?: number
  }
}) {
  const { clientGraphQL } = useContextGraphQL()

  const fetchData = async (params = {}) => {
    return clientGraphQL
      .query(graphQLQuery || QueryEmailApplicantCandidateList, params)
      .toPromise()
      .then((result: IResponseContextResult<IEmailCandidate>) => {
        if (result.error) {
          catchErrorFromGraphQL({
            error: result.error
          })

          return {
            data: [],
            meta: {
              totalRowCount: 0,
              pageSize: configuration.defaultPageSize
            }
          }
        }
        if (keyGraphQL) {
          const { collection, metadata } = result.data[keyGraphQL] || {
            collection: [],
            metadata: { totalCount: 0 }
          }
          return {
            data: collection,
            meta: {
              totalRowCount: metadata.totalCount,
              pageSize: configuration.defaultPageSize
            }
          }
        } else {
          const { applicantEmailsList } = result.data
          const collection = applicantEmailsList?.collection || []
          const metadata = applicantEmailsList?.metadata || { totalCount: 0 }
          return {
            data: collection,
            meta: {
              totalRowCount: metadata.totalCount,
              pageSize: configuration.defaultPageSize
            }
          }
        }
      })
  }
  const { data, fetchNextPage, refetch, hasNextPage, isFetching, isLoading, updateLocalRecord } = useInfinityQuerySearch<
    IEmailCandidate & { id?: string }
  >({
    configuration,
    fetchData,
    queryKey: variables,
    enabled: false
  })

  return {
    trigger: refetch,
    isLoading,
    fetchNextPage,
    hasNextPage,
    isFetching,
    data: useMemo(() => data?.pages.reduce<Array<IEmailCandidate>>((all, page) => [...all, ...page.data], []), [data]),
    totalRowCount: data?.pages?.[0]?.meta?.totalRowCount,
    updateLocalRecord
  }
}
