import type { TFunction } from 'next-i18next'
import { useTranslation } from 'next-i18next'
import { useCallback, useMemo } from 'react'
import type { AnyVariables, OperationResult } from 'urql'

import configuration from '~/configuration'
import type { IToast } from '~/core/@types/global'
import useMutationGraphQL from '~/core/middleware/use-mutation-graphQL'
import useQueryGraphQL from '~/core/middleware/use-query-graphQL'
import type { LucideIconName } from '~/core/ui/IconWrapper'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'

import useProfileViewDisplayStore from '~/lib/features/candidates/store/profile-view-display-slice'

import type { FieldSettingType } from '~/components/DisplayConfig'

import MutationUpdateUserSetting from '../graphql/mutation-update-user-setting'
import QueryUserSetting from '../graphql/query-user-setting'
import QueryUserSettingIsDefault from '../graphql/query-user-setting-is-default'
import type { InputFieldsSettingType } from '../types/user-setting'
import { FIELDS_USER_SETTING_DISPLAY } from '../utilities/enum'

const hiddenDisplayFields = ['applicantDisqualified', 'jobArchived']

export const mappingCustomFieldIcon = (fieldKind: string) => {
  if (!fieldKind) return 'Type'

  return (
    {
      string: 'Type',
      number: 'Hash',
      text: 'AlignLeft',
      boolean: 'CheckSquare',
      array: 'ListChecks',
      date: 'Calendar',
      multiple: 'LayoutList'
    } as { [key: string]: LucideIconName }
  )[fieldKind]
}

export const mappingDisplayColumnsSetting = (data: InputFieldsSettingType[], t?: TFunction) => {
  const fields = data
  const arr: FieldSettingType[] = []
  fields.forEach(fieldItem => {
    const isDefaultField = !fieldItem?.field_level || fieldItem?.field_level === 'default'

    arr[fieldItem?.index] = {
      id: String(fieldItem?.index),
      value: fieldItem.field_name,
      accessorKey: isDefaultField ? fieldItem.field_name : `column-custom-field-${fieldItem?.custom_field_id}`,
      locked: fieldItem?.locked,
      name: fieldItem?.name,
      hidden: hiddenDisplayFields.includes(fieldItem.field_name),
      visibleValue: fieldItem?.display,
      isDisableVisible: fieldItem?.display_locked,
      label: isDefaultField
        ? t
          ? `${t(`candidates:userSettingsDisplay:${FIELDS_USER_SETTING_DISPLAY?.[fieldItem.field_name]?.key}`)}`
          : ''
        : fieldItem?.name?.en,
      iconsMenu:
        fieldItem?.field_level === 'default'
          ? FIELDS_USER_SETTING_DISPLAY?.[fieldItem.field_name]?.iconMenus
          : mappingCustomFieldIcon(fieldItem.field_kind || ''),
      filter: !!fieldItem?.filter,
      fieldLevel: fieldItem?.field_level,
      fieldKind: fieldItem?.field_kind,
      customSettingId: fieldItem?.custom_field_id,
      visibility: fieldItem?.visibility,
      roleIds: fieldItem?.role_ids,
      isDefaultField
    }
  })

  return Object.values(arr)
}

export const useUserSetting = ({ setToast }: { setToast: (state: IToast) => void }) => {
  const { t } = useTranslation()
  const { profileViewDisplay, setProfileViewDisplay } = useProfileViewDisplayStore()

  const {
    trigger: fetchUserSetting,
    isLoading: isFetching,
    data: userSetting
  } = useQueryGraphQL({
    query: QueryUserSetting,
    variables: {},
    shouldPause: false
  })
  const {
    trigger: fetchIsUserSettingDefault,
    isLoading: isFetchingDefaultFlag,
    data: userIsSettingDefault
  } = useQueryGraphQL({
    query: QueryUserSettingIsDefault,
    variables: {},
    shouldPause: false
  })
  const {
    trigger: updateUserSetting,
    isLoading: isUpdating,
    data: updateResponseData
  } = useMutationGraphQL({
    query: MutationUpdateUserSetting
  })

  const resetAsDefault = useCallback<() => Promise<void>>(() => {
    const params = profileViewDisplay?.id
      ? { id: Number(profileViewDisplay.id), group: 'profile_view' }
      : {
          ...(userSetting?.userSettingsProfileViewColumns?.id ? { id: Number(userSetting?.userSettingsProfileViewColumns?.id) } : {}),
          group: userSetting?.userSettingsProfileViewColumns?.group
          // values: formateData,
        }

    return updateUserSetting({
      ...params,
      profileDisplay: userSetting?.userSettingsProfileViewColumns?.profileDisplay.map(f => ({
        ...f,
        custom_field_id: f.custom_field_id ? f.custom_field_id : null,
        field_level: f.field_level ? f.field_level : null
      })),
      resetDefault: true
    }).then(
      (
        response: OperationResult<
          {
            userSettingsProfileViewUpdate: {
              userSetting: {
                id: number
                profileColumnsIsDefault: boolean
                profileDisplay: FieldSettingType[]
              }
            }
          },
          AnyVariables
        >
      ) => {
        if (response.error) {
          catchErrorFromGraphQL({
            error: response.error,
            page: configuration.path.candidates.list,
            setToast
          })
        }

        const result = response?.data?.userSettingsProfileViewUpdate
        if (result?.userSetting?.id) {
          if (profileViewDisplay?.id) {
            setProfileViewDisplay({
              ...profileViewDisplay,
              profileDisplay: result?.userSetting?.profileDisplay,
              profileColumnsIsDefault: result?.userSetting?.profileColumnsIsDefault
            })
          } else {
            fetchUserSetting()
            fetchIsUserSettingDefault()
          }
        }
      }
    )
  }, [
    fetchUserSetting,
    fetchIsUserSettingDefault,
    setToast,
    updateUserSetting,
    userSetting?.userSettingsProfileViewColumns?.group,
    userSetting?.userSettingsProfileViewColumns?.id,
    userSetting?.userSettingsProfileViewColumns?.profileDisplay,
    profileViewDisplay
  ])

  const updateFieldsSetting = useCallback<(data: FieldSettingType[]) => Promise<void>>(
    data => {
      const filterData = data.filter(item => item)
      const formateData = filterData.map((field, index) => ({
        field_name: field?.value,
        display: !!field?.visibleValue,
        name: field?.name,
        index: index + 1,
        filter: field?.filter,
        locked: !!field?.locked,
        display_locked: !!field?.isDisableVisible,
        visibility: field?.visibility,
        field_kind: field?.fieldKind,
        field_level: field?.fieldLevel,
        custom_field_id: field?.customSettingId || null,
        role_ids: field?.roleIds,
        sort: ''
      }))

      const params = profileViewDisplay?.id
        ? { id: Number(profileViewDisplay.id), group: 'profile_view' }
        : {
            ...(userSetting?.userSettingsProfileViewColumns?.id ? { id: Number(userSetting?.userSettingsProfileViewColumns?.id) } : {}),
            group: userSetting?.userSettingsProfileViewColumns?.group
            // values: formateData,
          }

      return updateUserSetting({
        ...params,
        profileDisplay: formateData
      }).then(
        (
          response: OperationResult<
            {
              userSettingsProfileViewUpdate: {
                userSetting: {
                  id: number
                  profileColumnsIsDefault: boolean
                  profileDisplay: FieldSettingType[]
                }
              }
            },
            AnyVariables
          >
        ) => {
          if (response.error) {
            catchErrorFromGraphQL({
              error: response.error,
              page: configuration.path.candidates.list,
              setToast
            })
          }

          const result = response?.data?.userSettingsProfileViewUpdate

          if (result?.userSetting?.id) {
            if (profileViewDisplay?.id) {
              setProfileViewDisplay({
                ...profileViewDisplay,
                profileDisplay: result?.userSetting?.profileDisplay,
                profileColumnsIsDefault: result?.userSetting?.profileColumnsIsDefault
              })
            } else {
              fetchUserSetting()
              fetchIsUserSettingDefault()
            }
          }
        }
      )
    },
    [
      fetchIsUserSettingDefault,
      setToast,
      updateUserSetting,
      userSetting?.userSettingsProfileViewColumns?.group,
      userSetting?.userSettingsProfileViewColumns?.id,
      userSetting?.userSettingsProfileViewColumns?.profileDisplay,
      profileViewDisplay
    ]
  )

  return {
    userSetting: useMemo(
      () => ({
        ...userSetting?.userSettingsProfileViewColumns,
        profileDisplay: mappingDisplayColumnsSetting(userSetting?.userSettingsProfileViewColumns?.profileDisplay || [], t)
      }),
      [userSetting?.userSettingsProfileViewColumns]
    ),
    isDefaultUserSetting: useMemo(
      () => userIsSettingDefault?.userSettingsProfileViewColumns?.profileColumnsIsDefault,
      [userIsSettingDefault?.userSettingsProfileViewColumns?.profileColumnsIsDefault]
    ),
    resetAsDefault,
    updateFieldsSetting,
    isLoading: isFetching || isUpdating
  }
}
