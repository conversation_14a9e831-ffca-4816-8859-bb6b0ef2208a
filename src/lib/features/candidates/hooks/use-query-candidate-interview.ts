import { useMemo } from 'react'
import type { AnyVariables, TypedDocumentNode } from 'urql'

import configuration from '~/configuration'
import type { IResponseContextResult } from '~/core/middleware/use-context-graphQL'
import useContextGraphQL from '~/core/middleware/use-context-graphQL'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'

import { useInfinityQuerySearch } from '~/lib/hooks/use-infinity-query-search'

import QueryInterviewsCandidateList from '../../calendar/graphql/query-interviews-candidate-list'
import type { InterviewDetailType } from '../../calendar/types'

export function useQueryCandidateInterviews({
  variables,
  graphQLQuery,
  keyGraphQL
}: {
  graphQLQuery?: TypedDocumentNode<any, AnyVariables>
  keyGraphQL?: string
  variables: {
    applicantId?: number
    page?: number
    limit?: number
  }
}) {
  const { clientGraphQL } = useContextGraphQL()

  const fetchData = async (params = {}) => {
    return clientGraphQL
      .query(graphQLQuery || QueryInterviewsCandidateList, params)
      .toPromise()
      .then((result: IResponseContextResult<InterviewDetailType>) => {
        if (result.error) {
          catchErrorFromGraphQL({
            error: result.error
          })

          return {
            data: [],
            meta: {
              totalRowCount: 0,
              pageSize: configuration.defaultPageSize
            }
          }
        }

        if (keyGraphQL) {
          const { collection, metadata } = result.data[keyGraphQL] || {
            collection: [],
            metadata: { totalCount: 0 }
          }
          return {
            data: collection,
            meta: {
              totalRowCount: metadata.totalCount,
              pageSize: configuration.defaultPageSize
            }
          }
        } else {
          const { interviewsList } = result.data
          const collection = interviewsList?.collection || []
          const metadata = interviewsList?.metadata || { totalCount: 0 }
          return {
            data: collection,
            meta: {
              totalRowCount: metadata.totalCount,
              pageSize: configuration.defaultPageSize
            }
          }
        }
      })
  }
  const { data, fetchNextPage, refetch, hasNextPage, isFetching, isLoading, updateLocalRecord } = useInfinityQuerySearch<
    InterviewDetailType & { id?: string }
  >({
    configuration,
    fetchData,
    queryKey: variables,
    enabled: false
  })

  return {
    trigger: refetch,
    isLoading,
    fetchNextPage,
    hasNextPage,
    isFetching,
    data: useMemo(() => data?.pages.reduce<Array<InterviewDetailType>>((all, page) => [...all, ...page.data], []), [data]),
    totalRowCount: data?.pages?.[0]?.meta?.totalRowCount,
    updateLocalRecord
  }
}
