import type { TFunction } from 'i18next'
import { z } from 'zod'

export const schemaApplicantMoveToStage = (t: TFunction) => {
  return z.object({
    jobStageId: z
      .object({
        value: z.string(),
        supportingObj: z.object({
          name: z.string(),
          shortDescription: z.string()
        })
      })
      .nullish()
      .refine(obj => !!obj?.value, {
        message: `${t('form:requiredField')}`
      })
  })
}
