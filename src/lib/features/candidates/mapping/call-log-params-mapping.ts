import { formatISO } from 'date-fns'

import type { CallLogsFormType } from '../types'

export const paramsAddCallLogMapping = (param: CallLogsFormType, callToObject = 'profile') => {
  return {
    callToObject,
    callToId: Number(param?.callTo?.value) || undefined,
    phoneNumber: param?.phoneNumber || '',
    callTime: param?.callTime ? formatISO(param?.callTime) : undefined,
    direction: param?.direction?.value || undefined,
    outcome: param?.outCome?.value || undefined,
    notes: param?.notes || undefined
  }
}

export const paramsEditCallLogMapping = (param: CallLogsFormType, id: number) => {
  return {
    id,
    phoneNumber: param?.phoneNumber || '',
    callTime: param?.callTime ? formatISO(param?.callTime) : undefined,
    direction: param?.direction?.value || undefined,
    outcome: param?.outCome?.value || undefined,
    notes: param?.notes || ''
  }
}
