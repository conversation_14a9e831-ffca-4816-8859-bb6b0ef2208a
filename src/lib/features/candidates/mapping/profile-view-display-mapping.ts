import type { TFunction } from 'next-i18next'

import { mappingCustomFieldKind } from '~/lib/features/settings/profile-fields/mapping/custom-field-mapping'

import type { ICandidatesFilter, IProfileViewDisplay } from '../types'
import { FILTER_CANDIDATE_FIELDS_VALUE, OPERATOR } from '../utilities/enum'
import { convertToDate } from '../utilities/format'

export const mappingGroupProfileViewDisplayList = ({ data, userId, t }: { data?: IProfileViewDisplay[]; userId: number; t: TFunction }) => {
  if (!data) return []

  const results = data.reduce(
    (result, item) => {
      const isMyView = item?.createdById === userId || item.id === undefined

      if (isMyView) {
        return {
          myViews: [...(result.myViews || []), item],
          otherViews: result.otherViews
        }
      }

      return {
        myViews: result.myViews,
        otherViews: [...(result.otherViews || []), item]
      }
    },
    { myViews: [], otherViews: [] } as {
      myViews: Array<IProfileViewDisplay>
      otherViews: Array<IProfileViewDisplay>
    }
  )

  return results
}

export const mappingProfileViewFilter = ({
  profileView,
  filter
}: {
  profileView: IProfileViewDisplay | undefined
  filter: ICandidatesFilter | undefined
}) => {
  const relatedObjects = profileView?.profileFilters?.relatedObjects
    ? Object.entries(profileView?.profileFilters?.relatedObjects).reduce((acc: { [key: string]: object }, [key, value]) => {
        acc[key] =
          [FILTER_CANDIDATE_FIELDS_VALUE.created_at, FILTER_CANDIDATE_FIELDS_VALUE.updated_at, FILTER_CANDIDATE_FIELDS_VALUE.birthday].includes(
            key
          ) || key.includes('date')
            ? convertToDate(value)
            : value
        return acc
      }, {})
    : {}

  return {
    ...filter,
    ...relatedObjects,
    search: profileView?.profileFilters?.search || undefined,
    operator: profileView?.profileFilters?.operator || OPERATOR.and,
    fieldsFilter: profileView?.profileFilters?.fieldsFilter
      ? profileView?.profileFilters?.fieldsFilter?.map(item => ({
          id: item.fieldType === 'custom_field' ? String(`${mappingCustomFieldKind(String(item.fieldKind))}_${item.id}`) : item.fieldType,
          field:
            item.fieldType === 'custom_field'
              ? String(`${item.fieldType}_${mappingCustomFieldKind(String(item.fieldKind))}_${item.id}`)
              : item.object,
          direction: item.direction
        }))
      : []
  }
}
