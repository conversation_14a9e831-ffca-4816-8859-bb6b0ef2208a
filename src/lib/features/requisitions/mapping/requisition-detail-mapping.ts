import type { TFunction } from 'i18next'

import type { ApproveFlowStepType } from '~/components/Requisitions/components/ApprovalFlow'

import type { IRequisitionDetail } from '../types/requisition-detail'
import { APPROVER_STATUS_VALUE_ENUM, REQUISITION_STATUS_VALUE_ENUM, STEP_STATUS_ENUM } from '../utilities/enum'

export const mappingApprovalFlow = ({
  requisitionStatus,
  steps,
  t
}: {
  requisitionStatus: IRequisitionDetail['status']
  steps: IRequisitionDetail['requisitionSteps']
  t: TFunction
}): ApproveFlowStepType[] => {
  return (steps || [])?.map((step, index) => ({
    title:
      (steps || []).length > 1
        ? step?.requisitionApprovers?.length > step?.minimumApproval
          ? `${t('requisitions:detail:step_title_multi_approvers', {
              index: (step?.index || 0) + 1,
              minimumApproval: step?.minimumApproval || 1
            })}`
          : `${t('requisitions:detail:step_title_single_approver', {
              index: (step?.index || 0) + 1
            })}`
        : step?.requisitionApprovers?.length > step?.minimumApproval
          ? `${t('requisitions:detail:must_be_approved_by_at_least', {
              minimumApproval: step?.minimumApproval || 1
            })}`
          : `${t('requisitions:detail:must_be_approved_by')}`,
    isCurrentStep: step?.status === STEP_STATUS_ENUM.current_step,
    approvers: step?.requisitionApprovers?.map(approver => ({
      ...approver?.approver,
      status:
        (approver?.status === APPROVER_STATUS_VALUE_ENUM.pending && step?.status === STEP_STATUS_ENUM?.passed_step) ||
        (approver?.status === APPROVER_STATUS_VALUE_ENUM.pending && requisitionStatus === REQUISITION_STATUS_VALUE_ENUM.rejected)
          ? 'not_yet_reviewed'
          : approver?.status,
      rejectReason: approver?.rejectReason
    }))
  }))
}
