import type { IUserInformation } from '~/core/@types/global'
import { REMOTE_STATUS } from '~/core/constants/enum'

import type { IRequisitionForm } from '../types'
import { REQUISITION_STATUS_VALUE_ENUM } from '../utilities/enum'

export const mappingAddRequisition = (user: IUserInformation) => {
  return {
    name: '',
    locationIds: [],
    description: '',
    departmentId: undefined,
    ownerId: {
      value: String(user.id),
      supportingObj: { name: String(user.fullName || user.email) }
    },
    remoteStatus: REMOTE_STATUS.NO_REMOTE,
    typeOfSalary: 'annually',
    currency: user?.currentTenant?.currency,
    status: REQUISITION_STATUS_VALUE_ENUM.pending,
    headcount: ''
  }
}

export const mappingPublishAddRequisitionGraphQL = (data: IRequisitionForm) => {
  return {
    name: data?.name,
    status: data?.status || REQUISITION_STATUS_VALUE_ENUM.pending,
    departmentId: data.departmentId ? Number(data?.departmentId.value) : undefined,
    ownerId: data?.ownerId?.value ? Number(data?.ownerId?.value) : undefined,
    description: data?.description,
    salaryFrom: data?.salaryFrom !== '' && data?.salaryFrom !== undefined ? Number(data?.salaryFrom) : undefined,
    salaryTo: data?.salaryTo !== '' && data?.salaryTo !== undefined ? Number(data?.salaryTo) : undefined,
    currency: data?.currency,
    typeOfSalary: data?.typeOfSalary,
    hiringManagerIds: [Number(data?.hiringManagerIds?.value)],
    locationIds: data?.locationIds?.map(l => Number(l.value)),
    requisitionTemplateId: Number(data?.requisitionTemplateId?.value),
    reason: data?.reason,
    headcount: Number(data?.headcount)
  }
}
