import type { IUserInformation } from '~/core/@types/global'
import { trimFirstContentBreakLine } from '~/core/utilities/common'

import type { IRequisitionDetailForm, IRequisitionForm } from '../types'
import { REQUISITION_STATUS_VALUE_ENUM } from '../utilities/enum'

export const mappingDuplicateRequisition = (data: IRequisitionDetailForm, user: IUserInformation) => {
  const jobData = mappingEditRequisition(data)
  delete jobData.id
  return {
    ...jobData,
    name: data.name + ' (2)',
    ownerId: {
      value: user.id?.toString(),
      supportingObj: {
        name: String(user?.fullName || user.email)
      }
    },
    status: REQUISITION_STATUS_VALUE_ENUM.pending,
    headcount: ''
  }
}

export const mappingEditRequisition = (data: IRequisitionDetailForm) => {
  return {
    id: data.id,
    name: data.name,
    status: data.status,
    departmentId: data.department
      ? {
          value: data.department.id,
          supportingObj: {
            name: data.department.name
          }
        }
      : undefined,
    ownerId: data.owner
      ? {
          value: data.owner.id,
          supportingObj: {
            name: String(data.owner?.fullName || data.owner.email)
          }
        }
      : undefined,
    description: trimFirstContentBreakLine(data.description),
    salaryFrom: String(data.salaryFrom || ''),
    salaryTo: String(data.salaryTo || ''),
    currency: data.currency || '',
    typeOfSalary: data.typeOfSalary || '',
    hiringManagerIds:
      data?.managers?.length > 0
        ? {
            value: data?.managers[0]?.id,
            avatar: data?.managers[0]?.avatarVariants?.thumb?.url,
            avatarVariants: data?.managers[0]?.avatarVariants,
            supportingObj: {
              name: data?.managers[0]?.fullName,
              defaultColour: data?.managers[0]?.defaultColour
            }
          }
        : undefined,
    locationIds: (data.locations || []).map(item => ({
      value: String(item.id),
      supportingObj: {
        name: item.name
      }
    })),
    reason: data?.reason,
    requisitionTemplateId: data?.requisitionTemplate
      ? {
          value: data?.requisitionTemplate?.id,
          reqTemplateSteps: data?.requisitionTemplate?.reqTemplateSteps,
          supportingObj: {
            name: data?.requisitionTemplate?.name
          }
        }
      : undefined,
    headcount: String(data?.headcount)
  }
}

export const mappingPublishEditRequisitionGraphQL = (data: IRequisitionForm) => {
  return {
    id: data.id,
    name: data?.name,
    status: data?.status === REQUISITION_STATUS_VALUE_ENUM.draft ? data?.status : REQUISITION_STATUS_VALUE_ENUM.pending,
    departmentId: data?.departmentId?.value === undefined ? null : Number(data?.departmentId.value),
    ownerId: data?.ownerId?.value ? Number(data?.ownerId?.value) : undefined,
    description: data?.description,
    salaryFrom: data?.salaryFrom !== '' && data?.salaryFrom !== undefined ? Number(data?.salaryFrom) : undefined,
    salaryTo: data?.salaryTo !== '' && data?.salaryTo !== undefined ? Number(data?.salaryTo) : undefined,
    currency: data?.currency,
    typeOfSalary: data?.typeOfSalary,
    hiringManagerIds: [Number(data?.hiringManagerIds?.value)],
    locationIds: data?.locationIds?.map(l => Number(l.value)),
    requisitionTemplateId: Number(data?.requisitionTemplateId?.value),
    reason: data?.reason,
    headcount: Number(data?.headcount)
  }
}
