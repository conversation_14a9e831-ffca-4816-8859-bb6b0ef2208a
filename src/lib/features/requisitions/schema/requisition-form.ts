import type { TFunction } from 'i18next'
import { z } from 'zod'

import { removeHTMLTags } from '~/core/utilities/common'

const schemaRequisitionForm = (t: TFunction) => {
  return z
    .object({
      name: z
        .string()
        .trim()
        .min(1, {
          message: `${t('form:requiredField')}`
        })
        .max(80, {
          message: `${t('form:field_max_number_required', { number: 80 })}`
        }),
      departmentId: z
        .object(
          {
            value: z.string().optional()
          },
          {
            required_error: `${t('form:requiredField')}`
          }
        )
        .nullish()
        .refine(obj => !!obj, {
          message: `${t('form:requiredField')}`
        }),
      ownerId: z
        .object({ value: z.string() })
        .nullish()
        .refine(obj => !!obj?.value, {
          message: `${t('form:requiredField')}`
        }),
      description: z
        .string()
        .min(1, {
          message: `${t('form:requiredField')}`
        })
        .refine(
          description => {
            return removeHTMLTags(description || '').length <= 10000
          },
          {
            message: `${t('form:field_max_number_required', { number: 10000 })}`
          }
        ),
      status: z.string().optional(),
      reason: z.string({
        required_error: `${t('form:requiredField')}`
      }),
      salaryFrom: z.string().optional(),
      salaryTo: z.string().optional(),
      currency: z.string().optional(),
      typeOfSalary: z.string().optional(),
      hiringManagerIds: z
        .object({ value: z.string() })
        .nullish()
        .refine(obj => !!obj?.value, {
          message: `${t('form:requiredField')}`
        }),
      locationIds: z.array(z.object({ value: z.string() })).min(1, {
        message: `${t('form:requiredField')}`
      }),
      requisitionTemplateId: z
        .object(
          {
            value: z.string().optional()
          },
          {
            required_error: `${t('form:requiredField')}`
          }
        )
        .nullish()
        .refine(obj => !!obj, {
          message: `${t('form:requiredField')}`
        }),
      headcount: z
        .string()
        .min(1, {
          message: `${t('form:requiredField')}`
        })
        .max(3, {
          message: `${t('form:max_number_field', { number: 999 })}`
        })
    })
    .refine(
      data => {
        if (!data.salaryFrom || !data.salaryTo) return true

        return Number(data.salaryFrom) <= Number(data.salaryTo)
      },
      data => ({
        message: `${t('form:this_number_should_be_lesser_number_field_to')}`,
        path: ['salaryFrom']
      })
    )
    .refine(
      data => {
        if (!data.salaryFrom || !data.salaryTo) return true

        return Number(data.salaryFrom) <= Number(data.salaryTo)
      },
      data => ({
        message: `${t('form:this_number_should_be_greater_number_field_from')}`,
        path: ['salaryTo']
      })
    )
}

export default schemaRequisitionForm
