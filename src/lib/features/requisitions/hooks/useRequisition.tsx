import { useTranslation } from 'react-i18next'

import configuration from '~/configuration'
import type { IPromiseSearchOption } from '~/core/@types/global'
import type { IResponseContextResult } from '~/core/middleware/use-context-graphQL'
import useContextGraphQL from '~/core/middleware/use-context-graphQL'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'

import QueryTenantLocation from '../../settings/locations/graphql/query-tenant-location'
import type { ILocation } from '../../settings/locations/types'

const useRequisition = () => {
  const { t } = useTranslation()
  const { clientGraphQL } = useContextGraphQL()

  const promiseLocationOptions = (params = {} as IPromiseSearchOption) =>
    new Promise<any>(resolve => {
      clientGraphQL
        .query(QueryTenantLocation, params)
        .toPromise()
        .then((result: IResponseContextResult<ILocation>) => {
          if (result.error) {
            catchErrorFromGraphQL({
              error: result.error
            })
            resolve({
              metadata: {
                totalCount: configuration.defaultAsyncLoadingOptions
              },
              collection: []
            })
          }

          const { tenantLocationsList } = result.data
          const collection = tenantLocationsList?.collection || []
          const metadata = tenantLocationsList?.metadata || { totalCount: 0 }

          const cloneData = collection.map((item: ILocation) => {
            return {
              value: item.id,
              supportingObj: {
                name: item.name,
                badge: item.headquarter ? `${t('settings:teamMembers:headQuarter')}` : undefined,
                description: [item.address, item.city, item.state].filter(item => item).join(', ')
              }
            }
          })

          return resolve({ metadata, collection: cloneData })
        })
    })

  return {
    promiseLocationOptions
  }
}

export default useRequisition
