import pathConfiguration from 'src/configuration/path'
import configuration from '~/configuration'
import type { IPromiseSearchOption } from '~/core/@types/global'
import type { IResponseContextResult } from '~/core/middleware/use-context-graphQL'
import useContextGraphQL from '~/core/middleware/use-context-graphQL'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'

import QueryRequisitionTemplateList from '../graphql/query-requisition-templates'

const usePromiseRequisitionTemplateOptions = (configParams?: { default?: boolean }) => {
  const { clientGraphQL } = useContextGraphQL()

  const promiseRequisitionTemplateOptions = (params = {} as IPromiseSearchOption) =>
    new Promise<any>(resolve => {
      return clientGraphQL
        .query(QueryRequisitionTemplateList, {
          ...params,
          ...(configParams ? configParams : {})
        })
        .toPromise()
        .then((result: IResponseContextResult<any>) => {
          if (result.error) {
            resolve({
              metadata: {
                totalCount: configuration.defaultAsyncLoadingOptions
              },
              collection: []
            })
            return catchErrorFromGraphQL({
              error: result.error,
              page: pathConfiguration?.requisitions?.list
            })
          }
          const { requisitionTemplatesList } = result.data
          const collection = requisitionTemplatesList?.collection || []
          const metadata = requisitionTemplatesList?.metadata || {
            totalCount: 0
          }

          const cloneData = collection.map(item => {
            return {
              value: item.id,
              supportingObj: {
                name: item.name
              },
              reqTemplateSteps: item.reqTemplateSteps,
              default: item.default
            }
          })

          return resolve({ metadata, collection: cloneData })
        })
    })
  return { promiseRequisitionTemplateOptions }
}
export default usePromiseRequisitionTemplateOptions
