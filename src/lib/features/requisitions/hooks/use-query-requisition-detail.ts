import { useMemo } from 'react'
import type { AnyVariables, TypedDocumentNode } from 'urql'

import useQueryGraphQL from '~/core/middleware/use-query-graphQL'

import type { IRequisitionDetailForm } from '../types'

export function useQueryRequisitionDetail({
  query,
  variables,
  shouldPause = true
}: {
  query: TypedDocumentNode<
    {
      requisitionsShow: IRequisitionDetailForm
    },
    AnyVariables
  >
  variables: object
  shouldPause?: boolean
}) {
  const {
    trigger,
    isLoading,
    data: response,
    error
  } = useQueryGraphQL({
    query,
    variables,
    shouldPause
  })
  const data = useMemo(() => (response?.requisitionsShow || {}) as IRequisitionDetailForm, [response?.requisitionsShow])

  return {
    trigger,
    isLoading,
    data,
    error
  }
}
