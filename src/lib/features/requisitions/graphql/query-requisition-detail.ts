import { gql } from 'urql'

import type { IRequisitionDetail } from '../types/requisition-detail'

const QueryRequisitionDetail = gql<
  { requisitionsShow: IRequisitionDetail },
  {
    id: number
  }
>`
  query ($id: Int!) {
    requisitionsShow(id: $id) {
      id
      name
      description
      statusDescription
      status
      reasonDescription
      createdAt
      createdBy {
        fullName
      }
      locations {
        id
        name
        city
        state
        country
      }
      department {
        id
        name
      }
      managers {
        id
        fullName
        avatarVariants
        defaultColour
      }
      owner {
        id
        email
        fullName
        avatarVariants
        defaultColour
      }
      salaryFrom
      salaryTo
      currency
      currencyDescription
      typeOfSalary
      typeOfSalaryDescription
      jobCreatable
      editable
      archivable
      requisitionSteps {
        status
        minimumApproval
        index
        requisitionApprovers {
          status
          rejectReason
          approver {
            id
            email
            fullName
            avatarVariants
            defaultColour
          }
        }
      }
      jobs {
        id
        title
        status
      }
      headcount
    }
  }
`

export default QueryRequisitionDetail
