import { gql } from 'urql'

import type { PublicRequisitionDetailType } from '../types/requisition-public'

export const ApproveRejectPublicRequisition = gql<
  {
    publicRequisitionsApproveReject: {
      requisition: PublicRequisitionDetailType
    }
  },
  {
    uuid: string
    userToken: string
    status?: string
    rejectReason?: string
  }
>`
  mutation ($uuid: String!, $userToken: String!, $status: RequisitionStatus, $rejectReason: String) {
    publicRequisitionsApproveReject(input: { uuid: $uuid, userToken: $userToken, status: $status, rejectReason: $rejectReason }) {
      requisition {
        name
      }
    }
  }
`
