import { gql } from 'urql'

import type { ActivityBase, RequisitionActionLogType } from '../../activity/types'
import type { RequisitionPublicHistoryParam } from '../types/requisition-public'

const QueryPublicRequisitionHistory = gql<
  {
    publicRequisitionActivitiesList: {
      collection: Array<ActivityBase & RequisitionActionLogType>
      metadata: {
        totalCount: number
      }
    }
  },
  RequisitionPublicHistoryParam
>`
  query ($requisitionUuid: String!, $userToken: String!, $page: Int, $limit: Int) {
    publicRequisitionActivitiesList(page: $page, limit: $limit, requisitionUuid: $requisitionUuid, userToken: $userToken) {
      collection {
        actionKey
        createdAt
        loggedDate
        propertiesRelated
        payloadRelated
        user {
          fullName
          avatarVariants
          defaultColour
        }
        requisition {
          name
        }
      }
      metadata {
        totalCount
      }
    }
  }
`

export default QueryPublicRequisitionHistory
