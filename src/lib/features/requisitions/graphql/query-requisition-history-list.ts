import { gql } from 'urql'

import type { ActivityBase, RequisitionActionLogType } from '../../activity/types'
import type { RequisitionActionParam } from '../types/requisition-detail'

const QueryRequisitionHistory = gql<
  {
    requisitionActivitiesList: {
      collection: Array<ActivityBase & RequisitionActionLogType>
      metadata: {
        totalCount: number
      }
    }
  },
  RequisitionActionParam
>`
  query ($requisitionId: Int!, $page: Int, $limit: Int) {
    requisitionActivitiesList(page: $page, limit: $limit, requisitionId: $requisitionId) {
      collection {
        id
        actionKey
        properties
        payload
        createdAt
        loggedDate
        propertiesRelated
        user {
          id
          fullName
          avatarVariants
          defaultColour
        }
        payloadRelated
        requisition {
          id
          name
        }
      }
      metadata {
        totalCount
      }
    }
  }
`

export default QueryRequisitionHistory
