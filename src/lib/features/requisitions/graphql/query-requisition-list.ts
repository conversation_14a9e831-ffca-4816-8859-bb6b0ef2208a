import { gql } from 'urql'

import type { IQueryRequisitionParam, IRequisitionType } from '../types/management-page-type'

const QueryRequisitionList = gql<
  {
    requisitionsList: {
      collection: Array<IRequisitionType>
      metadata: {
        totalCount: number
      }
    }
  },
  IQueryRequisitionParam
>`
  query ($page: Int, $limit: Int, $search: String, $status: [RequisitionStatus!], $ownerId: Int, $departmentIds: [Int!], $sorting: JSON) {
    requisitionsList(
      page: $page
      limit: $limit
      search: $search
      status: $status
      ownerId: $ownerId
      departmentIds: $departmentIds
      sorting: $sorting
    ) {
      collection {
        id
        name
        statusDescription
        status
        locations {
          city
          state
          country
        }
        department {
          name
        }
        salaryFrom
        salaryTo
        currency
        currencyDescription
        typeOfSalaryDescription
        owner {
          id
          fullName
          avatarVariants
          defaultColour
        }
        jobs {
          id
          title
          status
          currentUserAccessible
        }
        requisitionSteps {
          status
          requisitionApprovers {
            approver {
              id
              fullName
            }
            status
          }
        }
        createdAt
        jobCreatable
        editable
        archivable
      }
      metadata {
        totalCount
      }
    }
  }
`

export default QueryRequisitionList
