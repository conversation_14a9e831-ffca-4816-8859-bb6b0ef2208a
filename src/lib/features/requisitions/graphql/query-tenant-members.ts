import { gql } from 'urql'

import type { OwnerType } from '../types/management-page-type'

const QueryTenantMembers = gql<
  {
    tenantMembers: {
      collection: Array<OwnerType>
      metadata: {
        totalCount: number
      }
    }
  },
  {
    page?: number
    limit?: number
    search?: string
    userReqPermisisons?: boolean
  }
>`
  query ($page: Int, $limit: Int, $search: String, $userReqPermisisons: Boolean) {
    tenantMembers(page: $page, limit: $limit, search: $search, userReqPermisisons: $userReqPermisisons) {
      collection {
        id
        email
        fullName
        avatarVariants
        defaultColour
        roles {
          id
          name
        }
      }
      metadata {
        totalCount
      }
    }
  }
`

export default QueryTenantMembers
