import { gql } from 'urql'

import type { PublicRequisitionDetailType } from '../types/requisition-public'

const QueryPublicRequisitionDetail = gql<
  { requisitionsShow: PublicRequisitionDetailType },
  {
    uuid: string
    userToken: string
  }
>`
  query ($uuid: String!, $userToken: String!) {
    publicRequisitionsShow(uuid: $uuid, userToken: $userToken) {
      name
      description
      status
      statusDescription
      reasonDescription
      createdAt
      createdBy {
        fullName
      }
      locations {
        name
        city
        state
      }
      tenant
      department
      managers {
        fullName
        avatarVariants
        defaultColour
      }
      owner {
        fullName
        avatarVariants
        defaultColour
      }
      salaryFrom
      salaryTo
      currencyDescription
      typeOfSalaryDescription
      requisitionSteps {
        status
        minimumApproval
        index
        requisitionApprovers {
          status
          rejectReason
          approver {
            fullName
            avatarVariants
            defaultColour
          }
        }
      }
      jobs {
        title
      }
      toastMessage
      showActionButtons
    }
  }
`

export default QueryPublicRequisitionDetail
