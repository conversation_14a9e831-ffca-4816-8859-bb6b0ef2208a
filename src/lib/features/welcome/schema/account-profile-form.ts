import type { TFunction } from 'i18next'
import { z } from 'zod'

const schemaWelcomeForm = (t: TFunction) => {
  return z.object({
    fullName: z
      .string()
      .trim()
      .min(1, {
        message: `${t('form:requiredField')}`
      })
      .max(250, {
        message: `${t('form:field_max_number_required', { number: 250 })}`
      }),
    avatar: z.any(),
    phoneNumber: z
      .string()
      .trim()
      .min(1, {
        message: `${t('form:requiredField')}`
      })
      .max(15, {
        message: `${t('form:invalid_phone_number')}`
      }),
    timezone: z.string().optional(),
    language: z.string().optional()
  })
}

export default schemaWelcomeForm
