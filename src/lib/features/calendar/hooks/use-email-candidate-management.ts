'use client'

import { useCallback, useEffect } from 'react'

import type { I<PERSON><PERSON>er<PERSON>ith<PERSON> } from '~/core/@types/global'
import useContextGraphQL from '~/core/middleware/use-context-graphQL'
import useQueryGraphQL from '~/core/middleware/use-query-graph<PERSON>'

import QueryTenantMembersEmail from '../../candidates/graphql/query-tenant-member-email'
import MutationSendEmailToApplicantCandidate from '../graphql/mutation-send-email-to-applicant-candidate'
import MutationSendEmailToProfileCandidate from '../graphql/mutation-send-email-to-profile-candidate'
import type { IEmailParamsSendType } from '../types'

const useEmailCandidateManagement = ({ applicantId, shouldCallRequest }: { applicantId?: IRouterWithID; shouldCallRequest?: boolean }) => {
  const { clientGraphQL } = useContextGraphQL()

  const { trigger: triggerTenantEmail, data: tenantEmailResponse } = useQueryGraphQL({
    query: QueryTenantMembersEmail,
    variables: {},
    shouldPause: true
  })

  useEffect(() => {
    if (shouldCallRequest) {
      triggerTenantEmail()
    }
  }, [shouldCallRequest, triggerTenantEmail])

  const sendEmail = useCallback<(data: IEmailParamsSendType) => Promise<void>>(
    data => {
      return clientGraphQL
        .mutation(MutationSendEmailToApplicantCandidate, data)
        .toPromise()
        .then((rs: { data?: { emailLog?: boolean } }) => rs.data)
    },
    [clientGraphQL]
  )
  const sendProfileEmail = useCallback<(data: IEmailParamsSendType) => Promise<void>>(
    data => {
      return clientGraphQL
        .mutation(MutationSendEmailToProfileCandidate, data)
        .toPromise()
        .then((rs: { data?: { emailLog?: boolean } }) => rs.data)
    },
    [clientGraphQL]
  )

  return {
    emailsTenant: {
      data: tenantEmailResponse
    },
    action: {
      sendEmail: (args: IEmailParamsSendType) => sendEmail(args),
      sendProfileEmail: (args: IEmailParamsSendType) => sendProfileEmail(args)
    }
  }
}

export default useEmailCandidateManagement
