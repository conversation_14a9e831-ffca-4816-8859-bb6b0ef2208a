import useQueryGraphQL from '~/core/middleware/use-query-graphQL'
import type { ISelectOption } from '~/core/ui/Select'

import QueryLocationList from '../graphql/query-location-list'

export function useQueryLocationList({ variables, shouldPause = true }: { variables: object; shouldPause?: boolean }) {
  const {
    trigger,
    isLoading,
    data: response,
    error
  } = useQueryGraphQL({
    query: QueryLocationList,
    variables,
    shouldPause
  })
  const data: Array<ISelectOption> = (response?.tenantLocationsList.collection || []).map(location => ({
    value: String(location.id),
    supportingObj: {
      name: [location.address, location.city, location.state, location.country].filter(item => item).join(', ')
    }
  }))

  return {
    trigger,
    isLoading,
    data: data,
    error
  }
}
