'use client'

import { useCallback, useEffect, useState } from 'react'

import type { IRouterWithID } from '~/core/@types/global'
import useContextGraphQL from '~/core/middleware/use-context-graphQL'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'

import QueryUpdateInterviewMutation from '../graphql/mutation-update-interview'
import QueryInterviewsCandidateList from '../graphql/query-interviews-candidate-list'
import type { InterviewCandidateListResponseType, InterviewParamsType } from '../types'

type IInterviewFilter = {
  applicantId?: number
  profileId?: number
}
const useInterviewsCandidateManagement = ({
  applicantId,
  profileId,
  shouldCallRequest
}: {
  applicantId?: IRouterWithID
  profileId?: IRouterWithID
  shouldCallRequest?: boolean
}) => {
  const { clientGraphQL } = useContextGraphQL()
  const [interviewList, setInterviewList] = useState<InterviewCandidateListResponseType>()
  const [isLoading, setIsLoading] = useState<boolean>(false)

  const fetchInterviewList = useCallback(() => {
    setIsLoading(true)
    return clientGraphQL
      .query(QueryInterviewsCandidateList, {
        ...(applicantId ? { applicantId: Number(applicantId) } : {})
      })
      .toPromise()
      .then((result: { error: { graphQLErrors: Array<object>; networkError?: object }; data: InterviewCandidateListResponseType }) => {
        if (result.error) {
          catchErrorFromGraphQL({
            error: result.error
          })
        }

        setInterviewList(result?.data)
        setIsLoading(false)
      })
  }, [clientGraphQL, applicantId, profileId])

  useEffect(() => {
    if (applicantId && shouldCallRequest) {
      fetchInterviewList()
    }
  }, [shouldCallRequest, applicantId, fetchInterviewList])

  const updateInterview = useCallback<(data: IInterviewFilter) => Promise<void>>(
    data => {
      return clientGraphQL
        .mutation(QueryUpdateInterviewMutation, data)
        .toPromise()
        .then(
          (rs: {
            data: {
              interview: {
                id: string
                fromDatetime: string
                toDatetime: string
              }
            }
          }) => {
            return rs.data
          }
        )
    },
    [clientGraphQL]
  )

  return {
    interviewsListControl: {
      data: interviewList,
      isLoading,
      refetch: fetchInterviewList
    },
    action: {
      interviewUpdateAction: {
        updateInterview: (args: InterviewParamsType) => updateInterview(args)
      }
    }
  }
}

export default useInterviewsCandidateManagement
