'use client'

import { formatISO, lastDayOfMonth, startOfMonth } from 'date-fns'
import { useCallback, useEffect, useMemo, useState } from 'react'

import type { IUserInformation } from '~/core/@types/global'
import useContextGraphQL from '~/core/middleware/use-context-graphQL'
import useQueryGraphQL from '~/core/middleware/use-query-graphQL'

import QueryUpdateInterviewMutation from '../graphql/mutation-update-interview'
import QueryInterviewsList from '../graphql/query-interviews-list'
import type { IInterviewsManagementFilter, InterviewParamsType } from '../types'
import { addTzToDate } from '../utilities/helper-schedule-interview'

const useInterviewsManagement = ({ user }: { user: IUserInformation }) => {
  const { clientGraphQL } = useContextGraphQL()
  const [filterValue, onChangeFilter] = useState<IInterviewsManagementFilter>()

  const {
    trigger,
    isLoading,
    data: response,
    error
  } = useQueryGraphQL(
    useMemo(
      () => ({
        query: QueryInterviewsList,
        variables: {
          fromDatetime: filterValue?.fromDatetime,
          toDatetime: filterValue?.toDatetime,
          ...(filterValue?.attendeeIds && filterValue.attendeeIds.length > 0
            ? {
                attendeeIds: filterValue.attendeeIds.map(item => Number(item.value))
              }
            : {})
        },
        shouldPause: !filterValue || Object.keys(filterValue).length === 0 || !user.id
      }),
      // eslint-disable-next-line react-hooks/exhaustive-deps
      [filterValue]
    )
  )

  useEffect(() => {
    user?.timezone &&
      onChangeFilter({
        fromDatetime: addTzToDate(formatISO(startOfMonth(new Date())), user?.timezone),
        toDatetime: addTzToDate(formatISO(lastDayOfMonth(new Date())), user?.timezone),
        attendeeIds: [
          {
            value: String(user?.id),
            supportingObj: {
              name: user?.fullName || '',
              defaultColour: user?.defaultColour
            },
            avatar: user.avatarVariants?.thumb?.url,
            avatarVariants: user?.avatarVariants
          }
        ]
      })
  }, [user?.avatarVariants, user?.defaultColour, user?.fullName, user?.id, user?.timezone])

  const updateInterview = useCallback<(data: InterviewParamsType) => Promise<void>>(
    data => {
      return clientGraphQL
        .mutation(QueryUpdateInterviewMutation, data)
        .toPromise()
        .then(
          (rs: {
            data: {
              interview: {
                id: string
                fromDatetime: string
                toDatetime: string
                ikitFeedbacksSummary: Array<{}>
              }
            }
          }) => {
            return rs.data
          }
        )
    },
    [clientGraphQL]
  )

  return {
    interviewsListControl: {
      data: response,
      isLoading,
      error,
      refetch: trigger
    },
    filterControl: useMemo(() => ({ value: filterValue, onChange: onChangeFilter }), [filterValue]),
    action: {
      interviewUpdateAction: {
        updateInterview: (args: InterviewParamsType) => updateInterview(args)
      }
    }
  }
}

export default useInterviewsManagement
