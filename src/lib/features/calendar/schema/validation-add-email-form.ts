import type { TFunction } from 'i18next'
import { z } from 'zod'

import { regexEmailValidation } from '~/core/utilities/common'

const schemaAddEmailForm = (t: TFunction) => {
  return z.object({
    email: z
      .string()
      .refine(value => value.trim() !== '', {
        message: `${t('form:requiredField')}`
      })
      .refine(value => regexEmailValidation.test(value), {
        message: `${t('form:invalid_email')}`
      })
  })
}

export default schemaAddEmailForm
