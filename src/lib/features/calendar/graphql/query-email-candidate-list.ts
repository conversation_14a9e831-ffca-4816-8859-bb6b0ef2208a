import { gql } from 'urql'

import type { IEmailCandidateListResponseType } from '../types'

const QueryEmailCandidateList = gql<IEmailCandidateListResponseType>`
  query ($page: Int, $limit: Int, $profileId: Int!) {
    profileEmailsList(page: $page, limit: $limit, profileId: $profileId) {
      collection {
        id
        payload
        body
        createdAt
        attachments {
          id
        }
        createdBy {
          fullName
          avatarVariants
          email
          defaultColour
        }
      }
      metadata {
        totalCount
      }
    }
  }
`

export default QueryEmailCandidateList
