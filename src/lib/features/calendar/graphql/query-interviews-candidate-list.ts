import { gql } from 'urql'

import type { InterviewCandidateListResponseType } from '../types'

const QueryInterviewsCandidateList = gql<
  InterviewCandidateListResponseType,
  {
    page?: number
    limit?: number
    fromDatetime?: string
    toDatetime?: string
    attendeeIds?: Array<number>
    profileId?: number
    applicantId?: number
    filterBy?: string
  }
>`
  query (
    $page: Int
    $limit: Int
    $fromDatetime: ISO8601DateTime
    $toDatetime: ISO8601DateTime
    $attendeeIds: [Int!]
    $profileId: Int
    $applicantId: Int
    $filterBy: String
  ) {
    interviewsList(
      page: $page
      limit: $limit
      fromDatetime: $fromDatetime
      toDatetime: $toDatetime
      attendeeIds: $attendeeIds
      profileId: $profileId
      applicantId: $applicantId
      filterBy: $filterBy
    ) {
      collection {
        id
        fromDatetime
        toDatetime
        timezone
        eventType
        eventTypeDescription
        meetingUrl
        previewLink
        location {
          id
          address
          city
          state
          country
        }
        profile {
          id
          fullName
          avatarVariants
          email
          applicants {
            id
            job {
              id
              title
            }
          }
        }
        organizer {
          id
          avatarVariants
          fullName
          defaultColour
          email
        }
        attendees {
          id
          avatarVariants
          fullName
          email
          defaultColour
          availableForSchedule
          roles {
            id
            name
          }
        }
        applicant {
          job {
            id
            title
            slug
            owner {
              email
              fullName
            }
          }
        }
        remindSchedule
        remindFeedback
        job {
          id
          title
          owner {
            id
          }
        }
        jobIkit {
          id
          name
        }
        jobStage {
          id
          stageLabel
        }
        currentUserFeedback {
          id
          overallFeedback
          status
        }
        ikitFeedbacksSummary
        state
        stateDescription
        interviewTimeSlots {
          fromDatetime
          toDatetime
        }
      }
      metadata {
        totalCount
      }
    }
  }
`

export default QueryInterviewsCandidateList
