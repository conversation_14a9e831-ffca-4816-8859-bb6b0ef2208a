import { gql } from 'urql'

import type { IEmailCandidateApplicantListResponseType } from '../types'

const QueryEmailApplicantCandidateList = gql<IEmailCandidateApplicantListResponseType>`
  query ($page: Int, $limit: Int, $id: Int!) {
    applicantEmailsList(page: $page, limit: $limit, id: $id) {
      collection {
        id
        payload
        body
        createdAt
        attachments {
          id
        }
        createdBy {
          fullName
          avatarVariants
          email
          defaultColour
        }
      }
      metadata {
        totalCount
      }
    }
  }
`

export default QueryEmailApplicantCandidateList
