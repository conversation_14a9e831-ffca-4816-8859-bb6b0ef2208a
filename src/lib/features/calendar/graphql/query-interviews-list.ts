import { gql } from 'urql'

import type { InterviewListResponseType } from '../types'

const QueryInterviewsList = gql<
  InterviewListResponseType,
  {
    fromDatetime?: string
    toDatetime?: string
    timezone?: string
    attendeeIds?: Array<number>
    profileId?: number
    applicantId?: number
  }
>`
  query (
    $page: Int
    $limit: Int
    $fromDatetime: ISO8601DateTime
    $toDatetime: ISO8601DateTime
    $attendeeIds: [Int!]
    $profileId: Int
    $applicantId: Int
  ) {
    interviewsList(
      page: $page
      limit: $limit
      fromDatetime: $fromDatetime
      toDatetime: $toDatetime
      attendeeIds: $attendeeIds
      profileId: $profileId
      applicantId: $applicantId
    ) {
      collection {
        id
        fromDatetime
        toDatetime
        previewLink
        timezone
        profile {
          fullName
        }
        ikitFeedbacksSummary
      }
      metadata {
        totalCount
      }
    }
  }
`

export default QueryInterviewsList
