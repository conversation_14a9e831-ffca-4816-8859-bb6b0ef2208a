import { gql } from 'urql'

import type { TaskItemType } from '../../tasks/types'

const QueryTaskDetail = gql<
  { tasksShow: TaskItemType },
  {
    id: number
  }
>`
  query ($id: Int!) {
    tasksShow(id: $id) {
      id
      title
      dueDate
      profile {
        id
        fullName
        avatarVariants
        email
        applicants {
          id
          job {
            id
            title
          }
        }
      }
      assignees {
        id
        avatarVariants
        fullName
        defaultColour
        email
      }
      creator {
        id
        avatarVariants
        fullName
        defaultColour
        email
      }
      company {
        id
        permittedFields
        logoVariants
      }
      status
      applicant {
        id
        profile {
          id
          fullName
          avatarVariants
          email
        }
      }
    }
  }
`

export default QueryTaskDetail
