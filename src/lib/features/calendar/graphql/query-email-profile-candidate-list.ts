import { gql } from 'urql'

import type { IEmailCandidateApplicantListResponseType } from '../types'

const QueryEmailProfileCandidateList = gql<IEmailCandidateApplicantListResponseType>`
  query ($page: Int, $limit: Int, $profileId: Int!) {
    profileEmailsList(page: $page, limit: $limit, profileId: $profileId) {
      collection {
        id
        payload
        body
        createdAt
        attachments {
          id
        }
        createdBy {
          fullName
          avatarVariants
          email
          defaultColour
        }
      }
      metadata {
        totalCount
      }
    }
  }
`

export default QueryEmailProfileCandidateList
