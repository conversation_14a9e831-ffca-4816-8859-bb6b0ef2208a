'use client'

import { useRouter } from 'next/navigation'
import { useCallback, useEffect, useState } from 'react'

import useContextGraphQL from '~/core/middleware/use-context-graphQL'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'

import { useRouterContext } from '~/lib/next/use-router-context'
import useToastStore from '~/lib/store/toast'

import QueryApplicationCustomFields from '../graphql/query-application-custom-fields'
import type { ApplicationField } from '../types/application-fields'

const useApplicationFieldsHook = ({ jobId }: { jobId: number }) => {
  const { clientGraphQL } = useContextGraphQL()
  const router = useRouter()
  const { pathname, params } = useRouterContext()

  const { setToast } = useToastStore()
  const [applicationFields, setApplicationFields] = useState<ApplicationField[] | undefined>()

  const fetchApplicationFieldsList = useCallback(() => {
    return clientGraphQL
      .query(QueryApplicationCustomFields, {
        jobId
      })
      .toPromise()
      .then((result: { error: { graphQLErrors: Array<object> }; data: { jobApplicationFieldsList: ApplicationField[] } }) => {
        if (result.error) {
          return catchErrorFromGraphQL({
            error: result.error,
            setToast,
            router: {
              push: router.push,
              pathname,
              params
            }
          })
        }

        const { jobApplicationFieldsList } = result.data
        setApplicationFields(jobApplicationFieldsList)

        return
      })
  }, [jobId])

  useEffect(() => {
    if (jobId) fetchApplicationFieldsList()
  }, [jobId])

  return {
    applicationFields,
    setApplicationFields,
    fetchApplicationFieldsList
  }
}

export default useApplicationFieldsHook
