'use client'

import { useCallback, useEffect, useMemo, useState } from 'react'

import configuration from '~/configuration'
import type { ISelectOption } from '~/core/ui/Select'

import { useInfinityGraphPage } from '~/lib/features/jobs/hooks/use-infinity-graph-page'
import { trimObjectProps } from '~/lib/features/tasks/utilities/common'

import QueryRecommendationJobList from '../graphql/query-recommendation-job-list'

export interface IJobsRecommendationFilter {
  locationId?: ISelectOption
  departmentIds?: Array<ISelectOption>
  jobLevel?: ISelectOption
  minSalary?: number
  currency?: string
  talentPool?: Array<ISelectOption>
  isFilterTouched?: boolean
  operator?: string
  tags?: {
    id: string
    value: string
  }[]
  talentPoolId?: string
}

const useJobRecommendationManagement = ({
  isCompanyKind,
  isLoaded,
  profileId,
  profileTab,
  suspend,
  talentPoolId
}: {
  isCompanyKind: boolean
  isLoaded: boolean
  profileId: number
  profileTab?: string
  suspend?: boolean
  talentPoolId?: string
}) => {
  const [filterValue, onChangeFilter] = useState<IJobsRecommendationFilter | undefined>({
    currency: 'USD',
    operator: 'or'
  })

  const jobRecommendationPaging = useInfinityGraphPage({
    queryDocumentNote: useMemo(() => QueryRecommendationJobList, []),
    getVariable: page => {
      const { locationId, departmentIds, jobLevel, minSalary, currency, talentPool, tags, operator } = filterValue || {}

      interface ObjectLiteral {
        [key: string]: any
      }

      let params: ObjectLiteral = {
        limit: configuration.defaultPageSize,
        page,
        ...(locationId?.value
          ? {
              countryStateId: Number(locationId?.value)
            }
          : {}),

        ...(jobLevel?.value
          ? {
              jobLevel: jobLevel?.value
            }
          : {}),
        departmentIds: (departmentIds?.length || 0) > 0 ? departmentIds?.map(item => parseInt(item.value)) : undefined,

        salaryFrom: minSalary ? Number(minSalary) : undefined,
        currency: currency ? currency : undefined,
        profileId,
        talentPoolIds: (talentPool?.length || 0) > 0 ? talentPool?.map(item => parseInt(item.value)) : undefined,
        tagIds: tags ? tags.map(t => Number(t.value)) : undefined,
        operator
      }

      if (talentPoolId) {
        params = { ...params, talentPoolId: Number(talentPoolId) }
      }

      return trimObjectProps({
        ...params
      })
    },
    getPageAttribute: (_lastGroup, groups) => ({
      totalCount: _lastGroup?.recommendedJobsList?.metadata?.totalCount,
      pageLength: Number(groups?.[0]?.recommendedJobsList?.collection?.length)
    }),
    queryKey: ['job-recommendation-list', String(profileId)],
    enabled: !suspend
  })

  useEffect(() => {
    if (isLoaded && !suspend) {
      jobRecommendationPaging.refetch()
    }
  }, [filterValue, isLoaded, suspend, profileTab])

  return {
    jobRecommendationPaging,
    filterControl: useMemo(
      () => ({
        value: filterValue,
        onChange: onChangeFilter
      }),
      [filterValue]
    )
  }
}

export default useJobRecommendationManagement
