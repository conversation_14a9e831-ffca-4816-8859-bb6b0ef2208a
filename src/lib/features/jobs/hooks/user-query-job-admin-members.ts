import type { AnyVariables, TypedDocumentNode } from 'urql'

import useQueryGraphQL from '~/core/middleware/use-query-graphQL'

export function useQueryMemberAdminOfJob({
  query,
  variables,
  shouldPause = true
}: {
  query: TypedDocumentNode<any, AnyVariables>
  variables: object
  shouldPause?: boolean
}) {
  const {
    trigger,
    isLoading,
    data: response,
    error
  } = useQueryGraphQL({
    query,
    variables,
    shouldPause
  })
  const data = response?.tenantMembers?.collection || []

  return {
    trigger,
    isLoading,
    data,
    error
  }
}
