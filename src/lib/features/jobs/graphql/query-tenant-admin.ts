import { gql } from 'urql'

import type { IMemberInfo } from './query-job-detail-mini'

const QueryTenantAdmins = gql<
  {
    tenantMembers: {
      collection: IMemberInfo[]
    }
  },
  { onlyAdmins: boolean }
>`
  query ($onlyAdmins: Boolean) {
    tenantMembers(onlyAdmins: $onlyAdmins) {
      collection {
        id
        avatarVariants
        email
        fullName
        defaultColour
        roles {
          name
        }
      }
    }
  }
`

export default QueryTenantAdmins
