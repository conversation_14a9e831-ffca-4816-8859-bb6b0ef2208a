import { gql } from 'urql'

import type { IJobManagementItemQuery } from '../types'

const QueryJobList = gql<
  {
    jobsList: {
      collection: IJobManagementItemQuery[]
      metadata: { totalCount: number; currentPage: number }
    }
  },
  {
    search?: string
    departmentId?: number | string
    locationIds?: number[]
    status?: string[]
    filterSavedJobs?: string
    exclusionJobIds?: number[]
  }
>`
  query (
    $page: Int
    $limit: Int
    $search: String
    $departmentIds: [Int!]
    $locationIds: [Int!]
    $countryStateIds: [Int!]
    $status: [JobStatus!]
    $hiringMemberIds: [Int!]
    $tagIds: [Int!]
    $operator: String
    $filterSavedJobs: String
    $membersOfDepartmentIds: [Int!]
    $exclusionJobIds: [Int!]
    $companyId: Int
  ) {
    jobsList(
      limit: $limit
      page: $page
      search: $search
      departmentIds: $departmentIds
      countryStateIds: $countryStateIds
      locationIds: $locationIds
      status: $status
      hiringMemberIds: $hiringMemberIds
      tagIds: $tagIds
      operator: $operator
      filterSavedJobs: $filterSavedJobs
      membersOfDepartmentIds: $membersOfDepartmentIds
      exclusionJobIds: $exclusionJobIds
      companyId: $companyId
    ) {
      collection {
        id
        title
        status
        headcount
        jobReferable
        jobLocations {
          name
          address
          state
          city
          country
        }
        slug
        company {
          id
          permittedFields
        }
        jobStages {
          id
          stageTypeId
          stageLabel
          index
          updateable
          stageGroup
        }
        department {
          name
        }
        statistic {
          viewCount
          applicantsCount
          newApplicantCount
        }
        jobRecruiters {
          id
          user {
            email
            fullName
            defaultColour
            avatarVariants
          }
        }
        status
        createdAt
        tenant {
          slug
        }
        tags {
          id
          name
        }
        listSavedJobMemberIds
        permittedFields
      }
      metadata {
        currentPage
        totalCount
      }
    }
  }
`

export default QueryJobList
