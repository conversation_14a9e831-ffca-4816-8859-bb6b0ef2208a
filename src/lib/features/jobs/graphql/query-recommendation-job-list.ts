import { gql } from 'urql'

import type { IJobManagementItemQuery } from '../types'

const QueryRecommendationJobList = gql<
  {
    recommendedJobsList: {
      collection: IJobManagementItemQuery[]
      metadata: {
        totalCount: number
        currentPage: number
      }
    }
  },
  {
    page?: number
    limit?: number
    search?: string
    profileId: number
    country?: string
    state?: string
    jobLevel?: string
    departmentIds?: Array<number>
    salaryFrom?: number
    currency?: string
  }
>`
  query (
    $page: Int
    $limit: Int
    $search: String
    $profileId: Int!
    $country: String
    $state: String
    $language: String
    $jobLevel: JobJobLevel
    $departmentIds: [Int!]
    $salaryFrom: Float
    $currency: String
    $talentPoolIds: [Int!]
    $countryStateId: Int
    $tagIds: [Int!]
    $operator: String
  ) {
    recommendedJobsList(
      page: $page
      limit: $limit
      search: $search
      profileId: $profileId
      country: $country
      state: $state
      language: $language
      jobLevel: $jobLevel
      departmentIds: $departmentIds
      salaryFrom: $salaryFrom
      currency: $currency
      talentPoolIds: $talentPoolIds
      countryStateId: $countryStateId
      tagIds: $tagIds
      operator: $operator
    ) {
      collection {
        id
        title
        slug
        jobReferable
        jobLocations {
          name
          address
          state
          city
          country
        }
        tenant {
          slug
        }
        department {
          name
        }
        recommendationMatchedFields
        status
        statusDescription
        createdAt
        permittedFields
      }
      metadata {
        currentPage
        totalCount
      }
    }
  }
`

export default QueryRecommendationJobList
