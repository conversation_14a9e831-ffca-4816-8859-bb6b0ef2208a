import { gql } from 'urql'

import type { IProfileListType } from '../../candidates/types'

const QueryRecommendedProfilesList = gql<
  {
    recommendedProfilesList: {
      collection: IProfileListType[]
      metadata: { totalCount: number }
    }
  },
  {}
>`
  query (
    $limit: Int
    $page: Int
    $search: String
    $jobId: Int!
    $country: String
    $state: String
    $profileLevel: ProfileProfileLevel
    $salaryFrom: Float
    $salaryTo: Float
    $currency: String
    $tagIds: [Int!]
    $operator: String
    $ownerIds: [Int!]
    $filterTalentPoolIds: [Int!]
    $countryStateId: Int
    $recommendWeight: JSON
  ) {
    recommendedProfilesList(
      limit: $limit
      page: $page
      search: $search
      jobId: $jobId
      country: $country
      state: $state
      profileLevel: $profileLevel
      salaryFrom: $salaryFrom
      salaryTo: $salaryTo
      currency: $currency
      tagIds: $tagIds
      operator: $operator
      ownerIds: $ownerIds
      filterTalentPoolIds: $filterTalentPoolIds
      countryStateId: $countryStateId
      recommendWeight: $recommendWeight
    ) {
      collection {
        defaultAccessibleApplicantId
        recommendationMatchedFields
        id
        fullName
        email
        phoneNumber
        avatarVariants
        createdAt
        updatedAt
        headline
        links
        tags {
          name
        }
        owner {
          fullName
          avatarVariants
          defaultColour
        }
        profileCvs {
          id
          attachments {
            id
            file
            blobs
          }
        }
        applicants {
          id
          job {
            id
            title
            status
            statusDescription
            currentUserAccessible
          }
          jobStage {
            stageLabel
            stageTypeId
          }
          flagNew
          rejectedReasonLabel
          status
        }
        permittedFields
      }
      metadata {
        totalCount
      }
    }
  }
`

export default QueryRecommendedProfilesList
