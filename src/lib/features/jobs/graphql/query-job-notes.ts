import { gql } from 'urql'

import type { ILogoAndAvatarVariants } from '~/core/@types/global'

export type TypeNote = {
  id: string
  content: string
  user: {
    id: string
    email: string
    fullName: string
    avatarVariants?: ILogoAndAvatarVariants
    defaultColour: string
  }
  createdAt: string
  updatedAt: string
  sharedUsers: {
    id: string
    email: string
    fullName: string
    avatarVariants?: ILogoAndAvatarVariants
    defaultColour: string
    description: string
  }[]
  attachments?: Array<{
    id: string
    blobs: {
      filename: string
      content_type: string
      size: number
    }
    file: string
  }>
}
export type JobNoteQery = {
  jobNotesList: {
    collection: TypeNote[]
    metadata: { totalCount: number }
  }
}

export const QueryJobNotes = gql<
  JobNoteQery,
  {
    page?: number
    limit?: number
    jobId?: number
  }
>`
  query ($page: Int, $limit: Int, $jobId: Int!, $userId: Int) {
    jobNotesList(page: $page, limit: $limit, jobId: $jobId, userId: $userId) {
      collection {
        id
        content
        user {
          id
          email
          fullName
          avatarVariants
          defaultColour
        }
        sharedUsers {
          id
          email
          fullName
          avatarVariants
          defaultColour
        }
        createdAt
        updatedAt
        attachments {
          id
          blobs
          file
        }
      }
      metadata {
        totalCount
      }
    }
  }
`
type CreateCommentInput = {
  commentableId?: number
  commentableType?: string
  content?: string
  sharedUserIds?: number[]
}
type UpdateCommentInput = {
  id: number
  content: string
}
type DeleteCommentInput = {
  id: number
}
export const MutationCreateNote = gql<{}, CreateCommentInput>`
  mutation (
    $commentableId: Int!
    $content: String!
    $sharedUserIds: [Int!]
    $attachments: [File!]
    $withFollowUpTask: Boolean
    $dueDate: ISO8601DateTime
  ) {
    jobNotesCreate(
      input: {
        commentableId: $commentableId
        content: $content
        sharedUserIds: $sharedUserIds
        attachments: $attachments
        withFollowUpTask: $withFollowUpTask
        dueDate: $dueDate
      }
    ) {
      jobNote {
        content
      }
    }
  }
`
export const MutationUpdateNote = gql<{}, UpdateCommentInput>`
  mutation ($id: Int!, $content: String!, $sharedUserIds: [Int!], $attachments: [File!]) {
    jobNotesUpdate(input: { id: $id, content: $content, sharedUserIds: $sharedUserIds, attachments: $attachments }) {
      jobNote {
        content
      }
    }
  }
`
export const MutationDeleteNote = gql<{}, DeleteCommentInput>`
  mutation ($id: Int!) {
    jobNotesDelete(input: { id: $id }) {
      success
    }
  }
`
