'use client'

import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { parseCookies } from 'nookies'
import type { ReactElement } from 'react'
import { useEffect, useMemo, useState } from 'react'

import { useLayoutData } from 'src/app/layoutApp'
import configuration from '~/configuration'
import { SESSION_COOKIE_CURRENT_TENANT, SESSION_ERROR_MESSAGE } from '~/core/constants/cookies'
import { SETTINGS_CAREERS_URL } from '~/core/constants/url'

import useSubscriptionPlan from '~/components/Subscription/useSubscriptionPlan'

import type { redirectTo } from './auth-utilities'
import { handleAuthenticationFlow } from './auth-utilities'

const getSlugTenantFromCareerHubUrlClient = (path: string) => {
  const parts = path.split('/')
  const careerhubIndex = parts.indexOf('careerhub')
  return careerhubIndex !== -1 && careerhubIndex + 1 < parts.length ? parts[careerhubIndex + 1] : undefined
}

const withClientAuthenticationMiddleware = (Component: (props: any) => ReactElement) => {
  const WithClientAuthenticationMiddleware = (props: any) => {
    const { userInitialize: user, searchParams, pathname, asPath } = useLayoutData()

    const params = useParams()
    const router = useRouter()
    const search = typeof window !== 'undefined' && window.location.search.substring(1)
    const query = search ? JSON.parse('{"' + decodeURI(search).replace(/"/g, '\\"').replace(/&/g, '","').replace(/=/g, '":"') + '"}') : {}
    const { data } = useSubscriptionPlan()
    const [suspend, setSuspend] = useState(true)
    const cookies = parseCookies()

    const isMatch = useMemo(
      () =>
        new RegExp(
          /^\/((?!login|register|verify-email|auth|400|404|500|access-denied|careerhub\/[^\/]+\/(verify-email|auth|400|404|500|access-denied)|\b(career|careers)\b).)*$/gm
        ).test(asPath) || [SETTINGS_CAREERS_URL].includes(asPath),
      [asPath]
    )

    useEffect(() => {
      if (searchParams && params && pathname) {
        const authenticationMiddleware = async () => {
          if (isMatch) {
            const planInfo = data && {
              isExpired: data?.isExpired,
              defaultPlan: data?.defaultPlan
            }
            const tenantSlug = getSlugTenantFromCareerHubUrlClient(asPath)
            const flowResult = handleAuthenticationFlow({
              user,
              resolvedUrl: asPath,
              query,
              redirectPath: tenantSlug
                ? cookies[SESSION_ERROR_MESSAGE]
                  ? configuration.path.careerHub.register(tenantSlug)
                  : configuration.path.careerHub.login(tenantSlug)
                : configuration.path.login,
              currentTenant: cookies[SESSION_COOKIE_CURRENT_TENANT],
              extendedReturn: {},
              planInfo
            })
            const redirectResponse = flowResult as ReturnType<typeof redirectTo>
            const pagesNotRedirect = [
              configuration.path.careerHub.login(tenantSlug as string),
              configuration.path.careerHub.register(tenantSlug as string)
            ]
            const redirectSearch = pagesNotRedirect.filter(item => asPath?.search(item) !== -1)
            // use only for careerHub [page login, register]
            const redirectDestination = String(redirectResponse.redirect?.destination)
            if (redirectResponse.redirect && redirectSearch.length === 0) {
              const PAGES_USE_WINDOW_LOCATION: string[] = [configuration.path.login]
              if (PAGES_USE_WINDOW_LOCATION.some(url => redirectDestination.startsWith(url))) {
                window.location.href = redirectDestination
              } else {
                router.push(redirectDestination)
              }
            } else {
              setSuspend(false)
            }
          } else {
            setSuspend(false)
          }
        }

        authenticationMiddleware()
      }
    }, [data, isMatch, searchParams, params, pathname])

    return suspend && isMatch ? <></> : <Component {...props} showOnServer={!isMatch} />
  }

  // Add display name for better debugging
  WithClientAuthenticationMiddleware.displayName = `WithClientAuthenticationMiddleware(${Component.name || 'Component'})`

  return WithClientAuthenticationMiddleware
}

export default withClientAuthenticationMiddleware
