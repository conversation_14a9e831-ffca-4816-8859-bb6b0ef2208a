'use client'

import type { ISelectOption } from '~/core/@types/global'
import { convertToSelectOptions } from '~/core/utilities/common'

/**
 * Client-side utility function to process searchParams
 * This function processes searchParams similar to how getServerSideProps processed query
 *
 * @param searchParams - URL search parameters from Next.js App Router
 * @param stringArrayFields - Fields that should be processed as string arrays
 * @param customProcessors - Custom processing functions for specific fields
 * @returns Processed query object
 */
export function processClientSideProps<T extends Record<string, any>>(
  searchParams: { [key: string]: string | string[] | undefined },
  stringArrayFields: string[] = [],
  customProcessors?: {
    [key: string]: (value: any) => any
  }
): T {
  const query: Record<string, any> = {}

  // Convert searchParams to query object (similar to getServerSideProps)
  Object.entries(searchParams).forEach(([key, value]) => {
    query[key] = value
  })

  // Process string array fields (similar to getServerSideProps)
  stringArrayFields.forEach(field => {
    const value = query[field]
    if (typeof value === 'string') {
      query[field] = value.split(',')
    }
  })

  // Apply custom processors if provided
  if (customProcessors) {
    Object.entries(customProcessors).forEach(([key, processor]) => {
      if (query[key] !== undefined) {
        query[key] = processor(query[key])
      }
    })
  }

  return query as T
}

/**
 * Client-side helper function to convert query parameters to select options
 * Similar to convertToSelectOptions but with proper type casting
 */
export function convertQueryToSelectOptions(value: string | string[] | undefined): ISelectOption[] | undefined {
  if (!value) return undefined

  const result = convertToSelectOptions(value)
  return result === '' ? undefined : (result as ISelectOption[])
}

/**
 * Client-side helper function to convert single value to select option
 */
export function convertQueryToSingleSelectOption(value: string | undefined): ISelectOption | undefined {
  if (!value) return undefined

  return { value, supportingObj: { name: value } }
}

/**
 * Client-side helper function to check if any filter is touched
 */
export function isFilterTouched(query: Record<string, any>, fields: string[]): boolean {
  return fields.some(field => !!query[field])
}
