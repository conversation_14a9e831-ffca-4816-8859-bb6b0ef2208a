'use client'

import { useRouter } from 'next/navigation'
import { parseCookies } from 'nookies'
import type { ComponentType } from 'react'
import { useCallback, useEffect, useMemo, useRef } from 'react'

import { useLayoutData } from 'src/app/layoutApp'
import type { IUserInformation } from '~/core/@types/global'
import { DEFAULT_URL } from '~/core/constants/url'

import { useRouterContext } from './use-router-context'
import { handleRedirectForLoginRegisterPage } from './with-auth'

export const withAuthPropsRoutingHandler = <T extends { user?: IUserInformation }>(Component: ComponentType<any>) => {
  const AuthPropsRoutingHandler = (props: T) => {
    const hasRedirectedRef = useRef(false)
    const router = useRouter()
    const { asPath } = useRouterContext()
    const { userInitialize: user } = useLayoutData()

    // Memoize cookies to prevent parsing on every render
    const cookies = useMemo(() => parseCookies(), [])

    // Memoize redirect parameters
    const redirectParams = useMemo(
      () => ({
        redirectPath: DEFAULT_URL,
        user,
        resolvedUrl: asPath,
        cookies
      }),
      [user, asPath, cookies]
    )

    // Handle user redirect with useCallback to prevent recreation
    const handleUserRedirect = useCallback(() => {
      // Prevent multiple redirects
      if (hasRedirectedRef.current) return

      if (user?.id) {
        handleRedirectForLoginRegisterPage(redirectParams)
      }
    }, [user?.id, redirectParams])

    // Handle pageshow event with useCallback
    const handlePageShow = useCallback(
      (event: PageTransitionEvent) => {
        // Reset redirect flag on page show (e.g., back button)
        if (event.persisted) {
          hasRedirectedRef.current = false
        }
        handleUserRedirect()
      },
      [handleUserRedirect]
    )

    useEffect(() => {
      // Initial check
      handleUserRedirect()

      // Add pageshow listener for back/forward navigation
      window.addEventListener('pageshow', handlePageShow)

      return () => {
        window.removeEventListener('pageshow', handlePageShow)
      }
    }, [])

    return <Component {...props} />
  }

  // Add display name for better debugging
  AuthPropsRoutingHandler.displayName = `AuthPropsRoutingHandler(${Component.displayName || Component.name || 'Component'})`

  return AuthPropsRoutingHandler
}

export default withAuthPropsRoutingHandler
