'use client'

import type { ReactElement } from 'react'
import { useEffect } from 'react'

import { useDiscardRouteChange } from '../hooks/use-discard-route-change'

interface withDiscardRouteProps {
  anyChangesForm?: boolean
  setAnyChangesFrom?: () => void
}

const withDiscardRouteChange = <T extends withDiscardRouteProps>(Component: (props: T) => ReactElement) => {
  const WithDiscardRouteChange = <A extends T>(props: A) => {
    const { anyChangesForm, setAnyChangesFrom } = useDiscardRouteChange()

    const beforeUnLoad = (e: BeforeUnloadEvent) => {
      if (anyChangesForm) {
        e.preventDefault()
        e.stopPropagation()
        e.returnValue = ''
      }
    }

    useEffect(() => {
      window.addEventListener('beforeunload', beforeUnLoad)

      return () => {
        window.removeEventListener('beforeunload', beforeUnLoad)
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [anyChangesForm])

    return <Component {...props} anyChangesForm={anyChangesForm} setAnyChangesFrom={setAnyChangesFrom} />
  }

  // Add display name for better debugging
  WithDiscardRouteChange.displayName = `WithDiscardRouteChange(${Component.name || 'Component'})`

  return WithDiscardRouteChange
}

export default withDiscardRouteChange
