import type { StateCreator } from 'zustand'

export interface ITenantFeatureSettingType {
  key: string
  enabling_feature: boolean
}

export interface TenantFeatureSettingSlice {
  featureSetting?: ITenantFeatureSettingType[]
  setFeatureSetting: (featureSetting: ITenantFeatureSettingType[]) => void
}

export const createTenantFeatureSettingSlice: StateCreator<TenantFeatureSettingSlice, [], [], TenantFeatureSettingSlice> = (set: Function) => ({
  featureSetting: undefined,
  setFeatureSetting: (featureSetting: ITenantFeatureSettingType[]) => set({ featureSetting })
})
