import type { StateCreator } from 'zustand'

export interface FilterSlice {
  filterValues?: any
  setFilterValues: (val: any) => void
  clearFilterValues: () => void
}

export const createFilterSlice: StateCreator<FilterSlice, [], [], FilterSlice> = (set: Function) => ({
  filterValues: null,
  setFilterValues: (val: any) => set({ filterValues: val }),
  clearFilterValues: () => set({ filterValues: null })
})
