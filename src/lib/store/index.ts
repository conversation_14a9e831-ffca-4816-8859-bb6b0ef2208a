import { create } from 'zustand'

import type { BulkActionSlice } from './bulk-action-slice'
import { createBulkActionSlice } from './bulk-action-slice'
import type { TenantFeatureSettingSlice } from './feature-setting-slice'
import { createTenantFeatureSettingSlice } from './feature-setting-slice'
import type { FilterSlice } from './filter-slice'
import { createFilterSlice } from './filter-slice'
import type { MyDeleteRefeshSlice } from './my-list-delete-slice'
import { createMyDeleteSlice } from './my-list-delete-slice'
import type { MyListSlice } from './my-list-slice'
import { createMyListSlice } from './my-list-slice'
import type { TenantPermissionSettingSlice } from './permission-setting-slice'
import { createTenantPermissionSettingSlice } from './permission-setting-slice'
import type { TasksSlice } from './tasks-drawer-slice'
import { createTasksSlice } from './tasks-drawer-slice'
import type { UserDevicesSlice } from './user-devices-slice'
import { createUserDevicesSlice } from './user-devices-slice'
import type { UserRoleSlice } from './user-role-slice'
import { createUserRoleSlice } from './user-role-slice'
import type { UserSlice } from './user-slice'
import { createUserSlice } from './user-slice'

const useBoundStore = create<
  UserSlice &
    UserRoleSlice &
    UserDevicesSlice &
    TasksSlice &
    MyDeleteRefeshSlice &
    MyListSlice &
    TenantPermissionSettingSlice &
    TenantFeatureSettingSlice &
    BulkActionSlice &
    FilterSlice
>()((...a) => ({
  ...createUserSlice(...a),
  ...createUserRoleSlice(...a),
  ...createUserDevicesSlice(...a),
  ...createTasksSlice(...a),
  ...createMyListSlice(...a),
  ...createTenantPermissionSettingSlice(...a),
  ...createTenantFeatureSettingSlice(...a),
  ...createMyDeleteSlice(...a),
  ...createBulkActionSlice(...a),
  ...createFilterSlice(...a)
}))

export default useBoundStore
