import type { StateCreator } from 'zustand'

import type { IUserInformation } from '~/core/@types/global'

export interface UserSlice {
  user: IUserInformation
  setUser: (user: IUserInformation) => void
}

export const createUserSlice: StateCreator<UserSlice, [], [], UserSlice> = (set: Function) => ({
  user: {
    id: undefined,
    email: undefined,
    avatarVariants: undefined,
    roles: { data: [] },
    tenants: { data: [] },
    ownTenant: undefined,
    currentTenant: undefined,
    fullName: undefined,
    phoneNumber: undefined,
    timezone: undefined,
    language: undefined,
    jobTitle: undefined,
    provider: undefined,
    userTenants: undefined,
    signInCount: undefined
  },
  setUser: (user: IUserInformation) => set({ user })
})
