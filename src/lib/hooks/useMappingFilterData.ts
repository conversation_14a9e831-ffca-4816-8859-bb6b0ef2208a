import type { ISelectOption } from '~/core/ui/Select'

import useSwitchRole from '~/lib/hooks/use-switch-role'

import type { IMember } from '../features/settings/members/types'
import type { ITeam } from '../features/settings/teams/types'

export const useMappingFilterData = () => {
  const switchRole = useSwitchRole()

  const mappingTeamsData = (defaultCollection: ITeam[], metadata: any) => {
    const collection = defaultCollection ?? []

    const mappedCollection: ISelectOption[] = collection.flatMap((team: ITeam) => {
      const parentOption: ISelectOption = {
        value: String(team.id),
        parentId: undefined,
        supportingObj: {
          name: team.name || ''
        }
      }

      const subOptions: ISelectOption[] = (team.subordinates ?? []).map(
        (sub: { id?: string; name?: string; parentId?: number; membersCount?: number }) => ({
          value: String(sub.id),
          parentId: String(team.id),
          supportingObj: {
            name: sub.name || ''
          }
        })
      )

      return [parentOption, ...subOptions]
    })

    return {
      metadata,
      collection: mappedCollection
    }
  }

  const mappingMembersData = (defaultCollection: IMember[], metadata: any) => {
    const collection = defaultCollection ?? []

    const fullList = collection.map(member => ({
      value: String(member.id),
      avatar: member.avatarVariants?.thumb?.url,
      avatarVariants: member.avatarVariants,
      supportingObj: {
        name: member.fullName,
        defaultColour: member.defaultColour
      }
    }))

    return {
      metadata,
      collection: switchRole({
        default: fullList,
        member: fullList
      })
    }
  }

  return {
    mappingMembersData,
    mappingTeamsData
  }
}
